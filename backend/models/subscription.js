const mongoose = require("mongoose");

const subscriptionSchema = new mongoose.Schema(
  {
    subscriber: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    creator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    pricingPlanId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    // Snapshot of the plan at the time of subscription
    planSnapshot: {
      noOfMonths: { type: Number, default: 1 },
      amount: { type: Number, default: 1000 },
      discountPercentage: { type: Number, default: 0 },
      title: { type: String, default: "1 Month Plan" },
      description: { type: String, default: "Default subscription plan" },
    },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    autoRenew: { type: Boolean, default: true },
    paymentMethod: {
      type: String,
      enum: ["wallet", "paystack", "flutterwave"],
      required: true,
    },
    status: {
      type: String,
      enum: [
        "active",
        "cancelled",
        "expired",
        "payment_failed",
        "payment_pending",
      ],
      default: "payment_pending",
    },
    paymentStatus: {
      type: String,
      enum: ["pending", "success", "failed"],
      default: "pending",
    },
    lastRenewalDate: Date,
    nextRenewalDate: Date,
    renewalAttempts: { type: Number, default: 0 },
    maxRenewalAttempts: { type: Number, default: 3 },
    cancellationReason: String,
    // Replace transactionHistory array with a virtual for transactions
    currentTransaction: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Transaction",
      required: true,
    },
    metadata: {
      type: Object,
      default: {},
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual to get all transactions related to this subscription
subscriptionSchema.virtual("transactions", {
  ref: "Transaction",
  localField: "_id",
  foreignField: "subscriptionId",
  options: { sort: { createdAt: -1 } },
});

// Static methods for subscription queries
subscriptionSchema.statics = {
  // Check if user has any active or pending subscription for a creator
  async checkExistingSubscription(subscriberId, creatorId) {
    const subscription = await this.findOne({
      subscriber: subscriberId,
      creator: creatorId,
      status: {
        $in: ["active", "payment_pending"],
      },
      endDate: { $gt: new Date() },
    }).populate("currentTransaction");

    if (!subscription) return null;

    // If subscription exists but payment failed, return it with canRetry flag
    if (
      subscription.status === "payment_pending" &&
      subscription.paymentStatus === "failed"
    ) {
      return {
        subscription,
        canRetry: true,
        message: "Previous payment failed. You can retry the payment.",
      };
    }

    // If subscription is pending payment but not failed
    if (subscription.status === "payment_pending") {
      return {
        subscription,
        canRetry: false,
        message:
          "You have a pending subscription. Please wait for payment confirmation.",
      };
    }

    // If subscription is active
    if (subscription.status === "active") {
      return {
        subscription,
        canRetry: false,
        message: "You already have an active subscription.",
      };
    }

    return null;
  },

  // Get all subscriptions that need renewal
  async getSubscriptionsNeedingRenewal() {
    const renewalWindow = new Date();
    renewalWindow.setDate(renewalWindow.getDate() + 3); // 3 days window

    return this.find({
      status: "active",
      autoRenew: true,
      endDate: {
        $lte: renewalWindow,
        $gt: new Date(),
      },
      renewalAttempts: { $lt: this.maxRenewalAttempts },
    }).populate("subscriber creator");
  },
};

// Instance methods
subscriptionSchema.methods = {
  // Check if subscription is active
  isActive() {
    return this.status === "active" && this.endDate > new Date();
  },

  // Check if subscription can be renewed
  canRenew() {
    return (
      this.autoRenew &&
      this.status !== "cancelled" &&
      this.renewalAttempts < this.maxRenewalAttempts
    );
  },

  // Cancel subscription
  async cancel(reason) {
    this.status = "cancelled";
    this.autoRenew = false;
    this.cancellationReason = reason;
    await this.save();
  },

  // Update payment status
  async updatePaymentStatus(status, transactionId) {
    this.paymentStatus = status;

    if (status === "success") {
      this.status = "active";
    } else if (status === "failed") {
      this.status = "payment_failed";
      this.renewalAttempts += 1;
    }

    if (transactionId) {
      this.currentTransaction = transactionId;
    }

    await this.save();
  },

  // Check if subscription needs renewal
  needsRenewal() {
    if (!this.autoRenew || this.status === "cancelled") return false;

    const now = new Date();
    const renewalWindow = new Date(this.endDate);
    renewalWindow.setDate(renewalWindow.getDate() - 3); // 3 days before expiry

    return now >= renewalWindow && this.canRenew();
  },

  // Check if payment can be retried
  canRetryPayment() {
    return (
      this.status === "payment_failed" &&
      this.renewalAttempts < this.maxRenewalAttempts &&
      this.endDate > new Date()
    );
  },
};

// Add a pre-save middleware to ensure planSnapshot is populated
subscriptionSchema.pre("save", async function (next) {
  if (this.isNew || this.isModified("pricingPlanId")) {
    try {
      const User = mongoose.model("User");
      const creator = await User.findById(this.creator);
      if (!creator) {
        throw new Error("Creator not found");
      }

      const plan = creator.pricingPlans.id(this.pricingPlanId);
      if (!plan) {
        throw new Error("Pricing plan not found");
      }

      this.planSnapshot = {
        noOfMonths: plan.noOfMonths,
        amount: plan.amount,
        discountPercentage: plan.discountPercentage,
        title: plan.title,
        description: plan.description,
      };
    } catch (error) {
      next(error);
    }
  }
  next();
});

// Indexes for better query performance
subscriptionSchema.index({ subscriber: 1, creator: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ endDate: 1 });
subscriptionSchema.index({ paymentStatus: 1 });
subscriptionSchema.index({ currentTransaction: 1 });

module.exports = mongoose.model("Subscription", subscriptionSchema);
