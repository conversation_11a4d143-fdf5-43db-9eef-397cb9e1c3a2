const mongoose = require("mongoose");

const withdrawalAccountSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    paymentMethod: {
      type: String,
      enum: ["crypto", "bank"],
      required: true,
    },
    // Crypto payment details
    cryptoDetails: {
      network: {
        type: String,
        enum: ["ETH", "BSC", "MATIC", "USDT"],
      },
      walletAddress: String,
    },
    // Bank payment details
    bankDetails: {
      country: {
        name: String,
        iso_code: String,
      },
      bankName: String,
      bankCode: String,
      accountNumber: String,
      accountName: String,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      enum: ["active", "inactive", "pending_verification"],
      default: "pending_verification",
    },
  },
  { timestamps: true }
);

// Ensure user can only have one default withdrawal account
withdrawalAccountSchema.pre("save", async function (next) {
  if (this.isDefault) {
    await this.constructor.updateMany(
      { user: this.user, _id: { $ne: this._id } },
      { $set: { isDefault: false } }
    );
  }
  next();
});

const WithdrawalAccount = mongoose.model(
  "WithdrawalAccount",
  withdrawalAccountSchema
);

module.exports = WithdrawalAccount;
