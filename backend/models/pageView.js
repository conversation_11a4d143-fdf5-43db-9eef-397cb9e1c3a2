const mongoose = require("mongoose");

const pageViewSchema = new mongoose.Schema(
  {
    profileId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    viewerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      default: null,
    },
    // IP address to help prevent duplicate counts
    ipAddress: {
      type: String,
      required: true,
    },
    // User agent to help identify unique views
    userAgent: {
      type: String,
      required: true,
    },
    // Session identifier for unauthorized users
    sessionId: {
      type: String,
      sparse: true,
    },
    isAuthenticated: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

// Updated compound index for both authenticated and unauthenticated views
pageViewSchema.index(
  {
    profileId: 1,
    ipAddress: 1,
    sessionId: 1,
    createdAt: 1,
  },
  { sparse: true }
);

module.exports = mongoose.model("PageView", pageViewSchema);
