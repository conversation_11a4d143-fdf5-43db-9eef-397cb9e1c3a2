const mongoose = require("mongoose");

const storySchema = new mongoose.Schema({
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  mediaUrl: {
    type: String,
    required: true,
  },
  mediaType: {
    type: String,
    enum: ["image", "video", "audio", "application"],
    required: true,
  },
  visibility: {
    type: String,
    enum: ["public", "subscribers"],
    default: "public",
    required: true,
  },
  public_id: {
    type: String,
    required: true,
  },
  asset_id: {
    type: String,
    required: true,
  },
  format: String,
  duration: Number,
  thumbnail: String,

  viewers: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      viewedAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 86400, // 24 hours in seconds
  },
});

module.exports = mongoose.model("Story", storySchema);
