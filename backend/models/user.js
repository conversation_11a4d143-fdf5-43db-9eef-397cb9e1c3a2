const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");

const pricingPlanSchema = new mongoose.Schema(
  {
    noOfMonths: { type: Number, required: true },
    amount: { type: Number, required: true },
    discountPercentage: { type: Number, default: 0 },
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
    title: { type: String },
    description: { type: String },
  },
  { _id: true }
);

const userSchema = new mongoose.Schema(
  {
    username: { type: String, required: true, unique: true },
    email: { type: String, required: true, unique: true },
    role: {
      type: String,
      enum: ["admin", "user", "creator"],
      default: "user",
    },
    password: { type: String, required: true },
    avatar: String,
    coverImage: String,
    header: String,
    displayName: { type: String },
    about: { type: String },
    resetPasswordToken: String,
    resetPasswordExpire: Date,
    emailVerificationToken: String,
    emailVerificationExpire: Date,
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    isCreator: { type: Boolean, default: false },
    creatorCategory: { type: String },
    socialLinks: {
      website: String,
      twitter: String,
      instagram: String,
      facebook: String,
      tiktok: String,
    },
    // subscriberCount: { type: Number, default: 0 },
    subscriptionCount: { type: Number, default: 0 },
    followersCount: { type: Number, default: 0 },
    followingCount: { type: Number, default: 0 },
    totalPosts: { type: Number, default: 0 },
    paymentInfo: {
      accountId: String,
      verified: { type: Boolean, default: false },
    },
    verified: { type: Boolean, default: false },
    subscriptions: [
      { type: mongoose.Schema.Types.ObjectId, ref: "Subscription" },
    ],
    pricingPlans: [pricingPlanSchema],
    withdrawalAccountId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "WithdrawalAccount",
    },
    kycStatus: {
      type: String,
      enum: ["pending", "approved", "rejected"],
      default: "pending",
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    verificationCode: {
      type: String,
      select: false,
    },
    verificationCodeExpires: {
      type: Date,
      select: false,
    },
  },
  { timestamps: true }
);

userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) {
    next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

userSchema.methods.createJWT = function () {
  return jwt.sign(
    { userId: this._id, email: this.email, role: this.role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_LIFETIME }
  );
};

userSchema.methods.comparePassword = async function (candidatePassword) {
  const isMatch = await bcrypt.compare(candidatePassword, this.password);
  return isMatch;
};

userSchema.methods.isSubscribedTo = async function (creatorId) {
  const Subscription = mongoose.model("Subscription");

  const subscription = await Subscription.findOne({
    subscriber: this._id,
    creator: creatorId,
    status: "active",
    endDate: { $gte: new Date() },
  });

  return !!subscription;
};

const User = mongoose.model("User", userSchema);

module.exports = User;
