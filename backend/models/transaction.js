const mongoose = require("mongoose");

const transactionSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    recipientId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: function () {
        return ["tip", "subscription"].includes(this.purpose);
      },
    },
    transactionType: {
      type: String,
      enum: ["credit", "debit"],
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    reference: {
      type: Object,
      required: false,
      default: null,
    },
    paymentMethod: {
      type: String,
      enum: ["wallet", "paystack", "flutterwave", "bank_transfer"],
      required: true,
    },
    purpose: {
      type: String,
      enum: ["tip", "subscription", "withdrawal", "deposit"],
      required: true,
    },
    tipType: {
      type: String,
      enum: [
        "post_tip",
        "livestream_tip",
        "story_tip",
        "chat_tip",
        "profile_tip",
        "video_tip",
        "audio_tip",
      ],
      required: function () {
        return this.purpose === "tip";
      },
    },
    status: {
      type: String,
      enum: ["pending", "success", "failed"],
      default: "pending",
    },
    isPending: {
      type: Boolean,
      default: true,
    },
    balance: {
      type: Number,
      required: function () {
        return this.paymentMethod === "wallet";
      },
    },
    pendingBalance: {
      type: Number,
      required: false,
    },
    bankDetails: {
      accountNumber: {
        type: String,
        required: function () {
          return this.purpose === "withdrawal";
        },
      },
      bankName: {
        type: String,
        required: function () {
          return this.purpose === "withdrawal";
        },
      },
      accountName: {
        type: String,
        required: function () {
          return this.purpose === "withdrawal";
        },
      },
    },
    subscriptionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subscription",
      required: false,
    },
    metadata: {
      type: Object,
      default: {},
    },
    subscriptionType: {
      type: String,
      enum: ["initial", "renewal", "auto-renewal"],
      required: function () {
        return this.purpose === "subscription";
      },
    },
  },
  { timestamps: true }
);

// Indexes for better query performance
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ recipientId: 1, createdAt: -1 });
transactionSchema.index({ purpose: 1, status: 1 });
transactionSchema.index({ subscriptionId: 1, createdAt: -1 });

module.exports = mongoose.model("Transaction", transactionSchema);
