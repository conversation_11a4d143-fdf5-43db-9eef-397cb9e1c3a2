const mongoose = require("mongoose");

const automationSchema = new mongoose.Schema(
  {
    creatorId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    triggerEvent: {
      type: String,
      required: true,
      enum: [
        "WELCOME_MESSAGE",
        "TIP_RECEIVED",
        "SUBSCRIPTION_EXPIRED",
        "RESUBSCRIBED",
      ],
    },
    message: {
      type: String,
      required: true,
    },
    timing: {
      type: {
        type: String,
        enum: ["immediately", "delay"],
        default: "immediately",
      },
      delayDuration: {
        type: Number, // delay in minutes
        default: 0,
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("Automation", automationSchema);
