const mongoose = require("mongoose");

const walletSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      unique: true,
    },
    balance: {
      type: Number,
      default: 0,
      min: 0,
    },
    pendingBalance: {
      type: Number,
      default: 0,
      min: 0,
    },
    currency: {
      type: String,
      enum: ["NGN", "USD"],
      default: "NGN",
    },
  },
  { timestamps: true }
);

// Virtual field for total balance (available + pending)
walletSchema.virtual("totalBalance").get(function () {
  return this.balance + this.pendingBalance;
});

// Ensure virtuals are included when converting document to JSON
walletSchema.set("toJSON", { virtuals: true });

module.exports = mongoose.model("Wallet", walletSchema);
