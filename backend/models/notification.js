const mongoose = require("mongoose");

const notificationSchema = new mongoose.Schema(
  {
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    type: {
      type: String,
      enum: [
        "LIKE",
        "COMMENT",
        "SUBSCRIBE",
        "COMMENT_LIKE",
        "MENTION",
        "REPLY",
        "NEW_POST",
        "TIP",
      ],
      required: true,
    },
    read: {
      type: Boolean,
      default: false,
    },
    post: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Post",
      // Not required because FOLLOW/SUBSCRIBE notifications don't have a post
    },
    comment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Comment",
      // Only for comment-related notifications
    },
    message: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("Notification", notificationSchema);
