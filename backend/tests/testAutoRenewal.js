require("dotenv").config();
const mongoose = require("mongoose");
const { handleAutoRenewal } = require("../services/cronService");
const { createLogger } = require("../utils/logger");

const logger = createLogger("testAutoRenewal");

async function testAutoRenewal() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info("Connected to MongoDB");

    // Run the auto-renewal process
    await handleAutoRenewal();
    logger.info("Completed auto-renewal process");
  } catch (error) {
    logger.error("Test failed:", error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info("Disconnected from MongoDB");
  }
}

// Run the test
testAutoRenewal();
