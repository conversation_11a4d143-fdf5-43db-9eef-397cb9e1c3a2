require("dotenv").config();
const mongoose = require("mongoose");
const { sendRenewalReminders } = require("../services/cronService");
const logger = require("../utils/logger");

async function testRenewalReminders() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info("Connected to MongoDB");

    // Run the renewal reminders
    await sendRenewalReminders();
    logger.info("Completed sending renewal reminders");
  } catch (error) {
    logger.error("Test failed:", error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info("Disconnected from MongoDB");
  }
}

// Run the test
testRenewalReminders();
