const express = require("express");
require("express-async-errors");
const app = express();
const cors = require("cors");
const dotenv = require("dotenv");
const connectDB = require("./db/connect");
const morgan = require("morgan");
const routes = require("./routes");
const rateLimit = require("express-rate-limit");
const errorHandlerMiddleware = require("./middlewares/error-handler");
const notFoundMiddleware = require("./middlewares/not-found");
const { initCronJobs } = require("./services/cronService");
const subscriptionRoutes = require("./routes/subscription");
// const { createBullBoard } = require("@bull-board/api");
// const { BullAdapter } = require("@bull-board/api/bullAdapter");
const { ExpressAdapter } = require("@bull-board/express");
// Comment out Redis/Bull connection code
/* 
const welcomeMessageQueue = require('./queues/welcomeMessageQueue');
const messageQueue = require('./queues/messageQueue');
const notificationQueue = require('./queues/notificationQueue');
*/

dotenv.config();

// Create HTTP server
const http = require("http").createServer(app);

// Initialize Bull Board
const serverAdapter = new ExpressAdapter();
// const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
//   queues: [new BullAdapter(welcomeMessageQueue)],
//   serverAdapter,
// });

// Mount Bull Board UI (protect this route in production)
// serverAdapter.setBasePath("/admin/queues");

// CORS Configuration
const corsOptions = {
  origin: "*", // Allow all origins
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
  ],
  credentials: true,
  optionsSuccessStatus: 200,
  preflightContinue: false,
};

// Apply CORS middleware before any other middleware
app.use(cors(corsOptions));

// Enable pre-flight requests for all routes
app.options("*", cors(corsOptions));

// Socket.IO setup with proper CORS
const io = require("socket.io")(http, {
  path: "/socket.io",
  cors: {
    origin: [
      process.env.FRONTEND_URL,
      "https://erossphere.com",
      "https://www.erossphere.com",
    ],
    methods: ["GET", "POST"],
    credentials: true,
  },
  transports: ["websocket", "polling"],
  allowEIO3: true,
});

// Store connected users and their online status
const connectedUsers = new Map();
const userStatus = new Map();
const lastSeenTimes = new Map();

// Helper function to update user status and last seen
const updateUserStatus = (userId, isOnline) => {
  const currentTime = new Date();
  userStatus.set(userId, isOnline);
  lastSeenTimes.set(userId, currentTime);

  return {
    userId,
    online: isOnline,
    lastSeen: currentTime,
  };
};

// Socket.IO connection handling
io.on("connection", (socket) => {
  console.log("A user connected", socket.id);
  let currentUserId = null;
  let activityTimeout;

  // Function to handle user activity
  const handleUserActivity = (userId) => {
    if (activityTimeout) {
      clearTimeout(activityTimeout);
    }

    // Update user's active status
    const statusUpdate = updateUserStatus(userId, true);
    io.emit("user_status_change", statusUpdate);

    // Set timeout to mark user as inactive after 5 minutes of no activity
    activityTimeout = setTimeout(() => {
      const inactiveUpdate = updateUserStatus(userId, false);
      io.emit("user_status_change", inactiveUpdate);
    }, 5 * 60 * 1000); // 5 minutes
  };

  socket.on("join", (userId) => {
    console.log(`User ${userId} joined with socket ${socket.id}`);
    currentUserId = userId;
    connectedUsers.set(userId, socket.id);

    // Update and broadcast user's status
    const statusUpdate = updateUserStatus(userId, true);
    socket.join(userId);
    io.emit("user_status_change", statusUpdate);

    // Initialize activity tracking
    handleUserActivity(userId);
  });

  socket.on("get_user_status", (userId) => {
    const isOnline = userStatus.get(userId) || false;
    const lastSeen = lastSeenTimes.get(userId) || null;

    socket.emit("user_status", {
      userId,
      online: isOnline,
      lastSeen,
    });
  });

  // Track user activity on various events
  socket.on("user_activity", () => {
    if (currentUserId) {
      handleUserActivity(currentUserId);
    }
  });

  socket.on("private message", async (data) => {
    const { receiverId, message } = data;
    console.log(`Sending message to ${receiverId}:`, message);

    // Update sender's activity
    if (currentUserId) {
      handleUserActivity(currentUserId);
    }

    // Send to receiver if they're connected
    const receiverSocketId = connectedUsers.get(receiverId);
    if (receiverSocketId) {
      io.to(receiverSocketId).emit("new message", message);
    }

    // Also send back to sender to ensure consistency
    socket.emit("new message", message);
  });

  socket.on("disconnect", () => {
    console.log("User disconnected", socket.id);
    if (currentUserId) {
      connectedUsers.delete(currentUserId);

      // Clear any existing activity timeout
      if (activityTimeout) {
        clearTimeout(activityTimeout);
      }

      // Update and broadcast offline status
      const statusUpdate = updateUserStatus(currentUserId, false);
      io.emit("user_status_change", statusUpdate);
    }
  });
});

// Mount Bull Board UI with basic auth protection
// app.use("/admin/queues", (req, res, next) => {
//   const auth = {
//     login: process.env.ADMIN_USER || "admin",
//     password: process.env.ADMIN_PASSWORD || "admin",
//   };
//   const b64auth = (req.headers.authorization || "").split(" ")[1] || "";
//   const [login, password] = Buffer.from(b64auth, "base64")
//     .toString()
//     .split(":");

//   if (login && password && login === auth.login && password === auth.password) {
//     return serverAdapter.getRouter()(req, res, next);
//   }

//   res.set("WWW-Authenticate", 'Basic realm="401"');
//   res.status(401).send("Authentication required.");
// });

// General rate limiter for all routes
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 1000,
  message: {
    status: 429,
    message:
      "Too many requests from this IP, please try again after 15 minutes",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Stricter limiter for authentication routes
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  message: {
    status: 429,
    message: "Too many login attempts, please try again after 15 minutes",
  },
});

app.use("/api/v1/auth", authLimiter);
app.use("/api/v1", limiter);
app.use(express.json());
app.use(express.json({ limit: "50mb" }));
app.use(
  express.urlencoded({ extended: true, limit: "50mb", parameterLimit: 50000 })
);
app.use(morgan("dev"));
app.use("/api/v1", routes);
app.use("/api/v1/subscriptions", subscriptionRoutes);

app.use(notFoundMiddleware);
app.use(errorHandlerMiddleware);

// Export socket.io instance and status tracking maps
app.set("io", io);
app.set("connectedUsers", connectedUsers);
app.set("userStatus", userStatus);
app.set("lastSeenTimes", lastSeenTimes);

const PORT = process.env.PORT || 3001;

const start = async () => {
  try {
    await connectDB(process.env.MONGODB_URI);
    console.log("Database connected successfully");

    // Initialize cron jobs after database connection
    await initCronJobs();
    console.log("Cron jobs initialized successfully");

    // // Initialize welcome message queue
    // welcomeMessageQueue.on("error", (error) => {
    //   console.error("Welcome message queue error:", error);
    // });

    // console.log("Welcome message queue initialized successfully");

    // Use http.listen instead of app.listen
    http.listen(PORT, () => {
      console.log(`Server is running at http://localhost:${PORT}`);
      console.log(`Socket.IO server is ready on port ${PORT}`);
      // console.log(
      //   `Queue monitoring available at http://localhost:${PORT}/admin/queues`
      // );
    });
  } catch (error) {
    console.log(error);
  }
};

start();

// Export both app and http server
module.exports = { app, http, io };
