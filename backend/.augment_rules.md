# Backend Augment Rules - OnlyFans-like Platform (Erossphere)

## 🏗️ Architecture Principles

### 1. **API Design & Structure**

- **RESTful API Design**: Follow REST conventions with clear resource naming

  - Use plural nouns for resources: `/api/v1/users`, `/api/v1/posts`, `/api/v1/subscriptions`
  - HTTP methods: GET (read), POST (create), PUT/PATCH (update), DELETE (remove)
  - Status codes: 200 (success), 201 (created), 400 (bad request), 401 (unauthorized), 403 (forbidden), 404 (not found), 500 (server error)

- **Route Organization**: Group related routes in separate files
  - `/routes/auth.js` - Authentication & authorization
  - `/routes/user.js` - User management & profiles
  - `/routes/post.js` - Content creation & management
  - `/routes/subscription.js` - Subscription management
  - `/routes/chat.js` - Messaging system
  - `/routes/booking.js` - Appointment booking (to be implemented)
  - `/routes/transaction.js` - Payment processing

### 2. **Security & Authentication**

- **JWT Authentication**: Use Bearer tokens with 7-day expiry
- **Role-based Access Control**: Implement user roles (admin, creator, user)
- **Route Protection**: Use middleware for authentication and authorization
- **Input Validation**: Validate all inputs using express-validator or Joi
- **Rate Limiting**: Implement rate limiting to prevent abuse
- **CORS Configuration**: Properly configure CORS for frontend domains

### 3. **Database Design (MongoDB)**

- **Schema Design**: Use Mongoose schemas with proper validation
- **Relationships**: Use ObjectId references for relationships
- **Indexing**: Add indexes for frequently queried fields
- **Aggregation**: Use MongoDB aggregation for complex queries
- **Transactions**: Use MongoDB transactions for critical operations

## 📊 Data Models & Schemas

### 1. **User Model** (`/models/user.js`)

```javascript
// Core user fields
username, email, password, role, avatar, coverImage, displayName, about
// Creator-specific fields
isCreator, creatorCategory, pricingPlans[], socialLinks
// Verification & KYC
isEmailVerified, isVerified, kycStatus, verificationCode
// Statistics
subscriptionCount, followersCount, followingCount, totalPosts
// Payment info
paymentInfo: { accountId, verified }, withdrawalAccountId
// Booking settings (to be added)
bookingEnabled, availabilitySchedule, meetingRates[]
```

### 2. **Subscription Model** (`/models/subscription.js`)

```javascript
// Relationship
subscriber: ObjectId, creator: ObjectId, pricingPlanId: ObjectId
// Plan snapshot (immutable record)
planSnapshot: { noOfMonths, amount, discountPercentage, title, description }
// Status & dates
status: enum, paymentStatus: enum, startDate, endDate, autoRenew
// Payment tracking
currentTransaction: ObjectId, renewalAttempts, maxRenewalAttempts
```

### 3. **Booking Model** (to be implemented)

```javascript
// Participants
creator: ObjectId, subscriber: ObjectId
// Booking details
appointmentDate: Date, duration: Number, meetingType: enum
// Status & payment
status: enum, paymentStatus: enum, amount: Number
// Meeting details
meetingLink: String, meetingId: String, notes: String
```

## 🔐 Authentication & Authorization

### 1. **Middleware Implementation**

- **authenticateUser**: Verify JWT token and attach user to request
- **optionalAuth**: Allow both authenticated and guest access
- **roleAuth**: Check user roles for specific endpoints
- **creatorAuth**: Ensure user is a verified creator

### 2. **Security Best Practices**

- Hash passwords using bcryptjs with salt rounds ≥ 12
- Implement email verification for new accounts
- Use secure JWT secrets and rotate them regularly
- Implement password reset with time-limited tokens
- Add two-factor authentication for creators

## 💳 Payment Processing

### 1. **Payment Integration**

- **Multiple Providers**: Support Paystack, Flutterwave, and Stripe
- **Wallet System**: Internal wallet for tips and earnings
- **Transaction Tracking**: Comprehensive transaction history
- **Automated Payouts**: Scheduled payouts to creators

### 2. **Subscription Billing**

- **Recurring Payments**: Automatic subscription renewals
- **Proration**: Handle mid-cycle plan changes
- **Failed Payment Handling**: Retry logic with grace periods
- **Refund Management**: Process refunds and chargebacks

## 📱 Real-time Features

### 1. **Socket.IO Implementation**

- **User Presence**: Track online/offline status
- **Live Messaging**: Real-time chat between creators and subscribers
- **Notifications**: Instant notifications for likes, comments, tips
- **Live Streaming**: Support for live video streaming (future)

### 2. **Event Handling**

```javascript
// Socket events to implement
"user_activity", "join", "leave", "new_message", "message_read";
"notification", "tip_received", "subscription_update";
"booking_request", "booking_confirmed", "booking_cancelled";
```

## 📄 Content Management

### 1. **Post System**

- **Content Types**: Support images, videos, audio, text
- **Visibility Control**: Public vs premium content
- **Media Processing**: Image/video optimization and thumbnails
- **Content Moderation**: Automated and manual content review

### 2. **File Upload & Storage**

- **Cloud Storage**: Use Cloudinary for media storage
- **File Validation**: Validate file types, sizes, and content
- **CDN Integration**: Serve media through CDN for performance
- **Backup Strategy**: Regular backups of user content

## 🔔 Notification System

### 1. **Notification Types**

```javascript
// Supported notification types
LIKE, COMMENT, SUBSCRIBE, TIP, NEW_POST, MENTION, REPLY;
BOOKING_REQUEST, BOOKING_CONFIRMED, BOOKING_CANCELLED;
SUBSCRIPTION_EXPIRED, PAYMENT_FAILED, PAYOUT_PROCESSED;
```

### 2. **Delivery Channels**

- **In-app Notifications**: Real-time via Socket.IO
- **Email Notifications**: Transactional emails via SendGrid/Mailgun
- **Push Notifications**: Mobile push notifications (future)

## 📅 Booking System (To Be Implemented)

### 1. **Appointment Management**

- **Availability Calendar**: Creators set available time slots
- **Booking Requests**: Subscribers request appointments
- **Payment Integration**: Secure payment before confirmation
- **Meeting Integration**: Zoom/Google Meet integration

### 2. **Booking Workflow**

```javascript
// Booking states
PENDING → CONFIRMED → COMPLETED
PENDING → CANCELLED
CONFIRMED → RESCHEDULED → CONFIRMED
```

## 🔄 Background Jobs & Automation

### 1. **Cron Jobs** (`/services/cronService.js`)

- **Subscription Management**: Handle renewals and expirations
- **Payment Processing**: Process pending payments
- **Content Cleanup**: Remove expired stories and temp files
- **Analytics Updates**: Update user statistics

### 2. **Automation Rules** (`/models/automation.js`)

- **Welcome Messages**: Auto-send welcome messages to new subscribers
- **Tip Acknowledgments**: Auto-respond to tips
- **Subscription Reminders**: Remind users of expiring subscriptions

## 📈 Analytics & Reporting

### 1. **Creator Analytics**

- **Earnings Tracking**: Revenue, tips, subscription income
- **Subscriber Metrics**: Growth, churn, engagement rates
- **Content Performance**: Views, likes, comments per post
- **Booking Analytics**: Appointment rates, revenue per booking

### 2. **Platform Analytics**

- **User Engagement**: Daily/monthly active users
- **Revenue Metrics**: Platform fees, transaction volumes
- **Content Metrics**: Upload rates, content types
- **Performance Monitoring**: API response times, error rates

## 🛡️ Error Handling & Logging

### 1. **Error Management**

- **Custom Error Classes**: BadRequestError, UnauthenticatedError, NotFoundError
- **Global Error Handler**: Centralized error handling middleware
- **Error Logging**: Log errors with context and stack traces
- **User-friendly Messages**: Return appropriate error messages to clients

### 2. **Logging Strategy**

- **Request Logging**: Log all API requests with Morgan
- **Application Logging**: Use Winston for application logs
- **Error Tracking**: Integrate with Sentry for error monitoring
- **Performance Monitoring**: Track slow queries and endpoints

## 🔧 Development & Deployment

### 1. **Code Quality**

- **ESLint Configuration**: Enforce coding standards
- **Prettier Integration**: Consistent code formatting
- **Git Hooks**: Pre-commit hooks for linting and testing
- **Code Reviews**: Mandatory code reviews for all changes

### 2. **Testing Strategy**

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test API endpoints and database operations
- **Load Testing**: Test performance under high load
- **Security Testing**: Regular security audits and penetration testing

### 3. **Environment Management**

- **Environment Variables**: Use .env files for configuration
- **Staging Environment**: Mirror production for testing
- **Database Migrations**: Version-controlled database changes
- **Deployment Automation**: CI/CD pipelines for automated deployment

## 🎯 OnlyFans-Specific Implementation Patterns

### 1. **Content Monetization**

- **Tiered Subscriptions**: Multiple pricing plans per creator
- **Pay-per-View Content**: Individual content purchases
- **Tip System**: Direct monetary support from fans
- **Live Stream Monetization**: Paid live streaming sessions

### 2. **Creator Economy Features**

- **Revenue Sharing**: Platform commission structure (typically 20%)
- **Payout Schedules**: Weekly/monthly automated payouts
- **Tax Reporting**: Generate tax documents for creators
- **Referral Programs**: Creator referral bonuses

### 3. **Fan Engagement**

- **Direct Messaging**: Private chat with creators
- **Custom Content Requests**: Paid custom content creation
- **Exclusive Content**: Subscriber-only posts and media
- **Interactive Features**: Polls, Q&A, live interactions

### 4. **Privacy & Safety**

- **Content Protection**: Watermarking and download prevention
- **User Verification**: ID verification for creators and high-value users
- **Content Moderation**: AI and human content review
- **Reporting System**: User reporting and moderation tools

## 📋 API Endpoint Patterns

### 1. **Authentication Endpoints**

```
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout
POST /api/v1/auth/forgot-password
POST /api/v1/auth/reset-password/:token
GET  /api/v1/auth/verify-email/:token
```

### 2. **User Management Endpoints**

```
GET    /api/v1/users/profile
PATCH  /api/v1/users/profile
GET    /api/v1/users/:username
GET    /api/v1/users/search?q=query
POST   /api/v1/users/follow/:userId
DELETE /api/v1/users/follow/:userId
```

### 3. **Content Endpoints**

```
GET    /api/v1/posts
POST   /api/v1/posts
GET    /api/v1/posts/:id
PATCH  /api/v1/posts/:id
DELETE /api/v1/posts/:id
POST   /api/v1/posts/:id/like
GET    /api/v1/posts/subscribed
```

### 4. **Subscription Endpoints**

```
POST   /api/v1/subscriptions/subscribe
GET    /api/v1/subscriptions/my-subscriptions
GET    /api/v1/subscriptions/subscribers
POST   /api/v1/subscriptions/:id/cancel
GET    /api/v1/subscriptions/check/:creatorId
```

### 5. **Booking Endpoints** (To Be Implemented)

```
POST   /api/v1/bookings
GET    /api/v1/bookings
GET    /api/v1/bookings/:id
PATCH  /api/v1/bookings/:id
DELETE /api/v1/bookings/:id
POST   /api/v1/bookings/:id/confirm
POST   /api/v1/bookings/:id/cancel
GET    /api/v1/bookings/availability/:creatorId
```

## 🔒 Security Implementation Guidelines

### 1. **Data Protection**

- **Encryption**: Encrypt sensitive data at rest and in transit
- **PII Handling**: Secure handling of personally identifiable information
- **GDPR Compliance**: Implement data protection regulations
- **Data Retention**: Policies for data retention and deletion

### 2. **Content Security**

- **DRM Protection**: Digital rights management for premium content
- **Watermarking**: Add creator watermarks to prevent piracy
- **Access Control**: Granular permissions for content access
- **Audit Trails**: Log all content access and modifications

### 3. **Financial Security**

- **PCI Compliance**: Follow payment card industry standards
- **Fraud Detection**: Implement fraud detection algorithms
- **Transaction Monitoring**: Monitor for suspicious activities
- **Secure Webhooks**: Validate payment provider webhooks

## 🚀 Performance Optimization

### 1. **Database Optimization**

- **Indexing Strategy**: Create indexes for frequently queried fields
- **Query Optimization**: Use aggregation pipelines efficiently
- **Connection Pooling**: Optimize database connections
- **Caching Layer**: Implement Redis for frequently accessed data

### 2. **API Performance**

- **Response Compression**: Use gzip compression
- **Pagination**: Implement cursor-based pagination
- **Rate Limiting**: Prevent API abuse
- **CDN Integration**: Serve static assets via CDN

### 3. **Monitoring & Alerting**

- **Health Checks**: Implement health check endpoints
- **Performance Metrics**: Track response times and throughput
- **Error Monitoring**: Set up error alerting
- **Resource Monitoring**: Monitor CPU, memory, and disk usage

## 📝 Code Organization Best Practices

### 1. **File Structure**

```
backend/
├── controllers/     # Business logic
├── models/         # Database schemas
├── routes/         # API route definitions
├── middlewares/    # Custom middleware
├── services/       # External service integrations
├── utils/          # Utility functions
├── config/         # Configuration files
├── tests/          # Test files
└── migrations/     # Database migrations
```

### 2. **Naming Conventions**

- **Files**: Use kebab-case (user-controller.js)
- **Functions**: Use camelCase (getUserProfile)
- **Constants**: Use UPPER_SNAKE_CASE (MAX_FILE_SIZE)
- **Database Fields**: Use camelCase (createdAt, userId)

### 3. **Documentation Standards**

- **API Documentation**: Use Swagger/OpenAPI
- **Code Comments**: Document complex business logic
- **README Files**: Comprehensive setup instructions
- **Changelog**: Track version changes and updates
