{"info": {"_postman_id": "random-uuid", "name": "Auth API", "description": "Authentication API endpoints collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/auth/register", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user"}}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/auth/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login existing user"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/auth/forgot-password", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "forgot-password"]}, "description": "Request password reset email"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"reset-token-from-email\",\n    \"password\": \"newpassword123\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/auth/reset-password", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "reset-password"]}, "description": "Reset password with token"}}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/auth/logout", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout user (invalidate token)"}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{BASE_URL}}/api/v1/auth/me", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "auth", "me"]}, "description": "Get current user profile"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "BASE_URL", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "", "type": "string"}]}