// const Queue = require("bull");
// const { Message, Conversation } = require("../models/chat");
// const { createLogger } = require("../utils/logger");

// const logger = createLogger("welcomeMessageQueue");

// // Create the queue instance
// const welcomeMessageQueue = new Queue(
//   "welcomeMessages",
//   process.env.REDIS_URL,
//   {
//     defaultJobOptions: {
//       attempts: 3, // Retry failed jobs up to 3 times
//       backoff: {
//         type: "exponential",
//         delay: 5000, // Start with 5 seconds delay
//       },
//       removeOnComplete: true, // Remove successful jobs
//       removeOnFail: false, // Keep failed jobs for inspection
//     },
//   }
// );

// // Process welcome messages
// welcomeMessageQueue.process(async (job) => {
//   const { creatorId, subscriber, processedMessage } = job.data;
//   logger.info(`Processing welcome message for subscriber ${subscriber}`);

//   try {
//     // Find or create conversation
//     let conversation = await Conversation.findOne({
//       participants: { $all: [subscriber, creatorId] },
//     });

//     if (!conversation) {
//       conversation = await Conversation.create({
//         participants: [subscriber, creatorId],
//         messages: [],
//       });
//     }

//     // Create and save the message
//     const message = await Message.create({
//       sender: creatorId,
//       receiver: subscriber,
//       content: processedMessage,
//       conversation: conversation._id,
//       readBy: [creatorId],
//       messageType: "text",
//     });

//     // Update conversation
//     conversation.lastMessage = message._id;
//     conversation.messages.push(message._id);
//     await conversation.save();

//     // Populate message for socket emission
//     const populatedMessage = await message.populate([
//       {
//         path: "sender",
//         select: "username email avatar displayName",
//       },
//       {
//         path: "conversation",
//         select: "_id",
//       },
//       {
//         path: "readBy",
//         select: "username _id",
//       },
//     ]);

//     // Return the populated message for socket emission
//     return { message: populatedMessage, conversation: conversation._id };
//   } catch (error) {
//     logger.error("Error processing welcome message:", error);
//     throw error; // Rethrow to trigger retry mechanism
//   }
// });

// // Queue event handlers for monitoring
// welcomeMessageQueue.on("completed", (job, result) => {
//   logger.info(`Job ${job.id} completed successfully`);

//   // Emit socket event on completion
//   const io = require("../app").io;
//   if (io) {
//     const { message, conversation } = result;
//     io.to(job.data.subscriber.toString()).emit("new_message", {
//       message,
//       conversation,
//     });
//   }
// });

// welcomeMessageQueue.on("failed", (job, error) => {
//   logger.error(`Job ${job.id} failed:`, error);
// });

// welcomeMessageQueue.on("error", (error) => {
//   logger.error("Queue error:", error);
// });

// // Export the queue instance
// module.exports = welcomeMessageQueue;
