const Notification = require("../models/notification");
const { fCurrency } = require("./formatNumber");

const createLikeNotification = async (
  postId,
  likerId,
  postAuthorId,
  message
) => {
  if (likerId.toString() === postAuthorId.toString()) return; // Don't notify if user likes their own post

  await Notification.create({
    recipient: postAuthorId,
    sender: likerId,
    type: "LIKE",
    post: postId,
    message,
  });
};

const createCommentNotification = async (
  postId,
  commentId,
  commenterId,
  postAuthorId,
  message
) => {
  if (commenterId.toString() === postAuthorId.toString()) return; // Don't notify if user comments on their own post

  await Notification.create({
    recipient: postAuthorId,
    sender: commenterId,
    type: "COMMENT",
    post: postId,
    comment: commentId,
    message,
  });
};

const createSubscribeNotification = async (followerId, followeeId, message) => {
  await Notification.create({
    recipient: followeeId,
    sender: followerId,
    type: "SUBSCRIBE",
    message,
  });
};

const createMentionNotification = async (
  postId,
  mentionerId,
  mentionedUserId,
  message,
  commentId = null // Optional comment ID if mention is in a comment
) => {
  if (mentionerId.toString() === mentionedUserId.toString()) return; // Don't notify if user mentions themselves

  await Notification.create({
    recipient: mentionedUserId,
    sender: mentionerId,
    type: "MENTION",
    post: postId,
    comment: commentId, // Will be null if mention is in post content
    message,
  });
};

const createNewPostNotification = async (
  postId,
  postAuthorId,
  subscriberId,
  message
) => {
  if (postAuthorId.toString() === subscriberId.toString()) return; // Don't notify the author about their own post

  await Notification.create({
    recipient: subscriberId,
    sender: postAuthorId,
    type: "NEW_POST",
    post: postId,
    message,
  });
};

const createCommentLikeNotification = async (
  postId,
  commentId,
  likerId,
  commentAuthorId,
  message
) => {
  if (likerId.toString() === commentAuthorId.toString()) return; // Don't notify if user likes their own comment

  await Notification.create({
    recipient: commentAuthorId,
    sender: likerId,
    type: "COMMENT_LIKE",
    post: postId,
    comment: commentId,
    message,
  });
};

const createReplyNotification = async (
  postId,
  commentId,
  replyerId,
  commentAuthorId,
  message
) => {
  await Notification.create({
    recipient: commentAuthorId,
    sender: replyerId,
    type: "REPLY",
    post: postId,
    comment: commentId,
    message,
  });
};

const createTipNotification = async (senderId, recipientId, amount) => {
  await Notification.create({
    recipient: recipientId,
    sender: senderId,
    type: "TIP",
    message: `You received a tip of ${fCurrency(amount)}`,
    metadata: {
      amount,
    },
  });
};

module.exports = {
  createLikeNotification,
  createCommentNotification,
  createSubscribeNotification,
  createMentionNotification,
  createNewPostNotification,
  createCommentLikeNotification,
  createReplyNotification,
  createTipNotification,
};
