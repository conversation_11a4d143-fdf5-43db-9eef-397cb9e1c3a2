const nodemailer = require("nodemailer");
const mjml2html = require("mjml");
const Handlebars = require("handlebars");
const fs = require("fs").promises;
const path = require("path");

// Cache for compiled templates
const templateCache = new Map();

// Create reusable transporter object using SMTP transport
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: 465,
  secure: true,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

// // Verify transporter connection configuration
// transporter.verify(function (error, success) {
//   if (error) {
//     console.log("Error verifying mail server:", error);
//   } else {
//     console.log("Mail server is ready to send messages");
//   }
// });

/**
 * Loads and compiles an MJML template
 * @param {string} templateName - Name of the template file without extension
 * @returns {Promise<Function>} Compiled template function
 */
async function getTemplate(templateName) {
  if (templateCache.has(templateName)) {
    return templateCache.get(templateName);
  }

  try {
    const templatePath = path.join(
      __dirname,
      "email-templates",
      `${templateName}.mjml`
    );
    const mjmlTemplate = await fs.readFile(templatePath, "utf-8");
    const { html, errors } = mjml2html(mjmlTemplate);

    // Log any MJML errors
    if (errors && errors.length) {
      console.error("MJML Template Errors:", errors);
    }

    const template = Handlebars.compile(html);
    templateCache.set(templateName, template);
    return template;
  } catch (error) {
    console.error(`Error processing template ${templateName}:`, error);
    throw error;
  }
}

/**
 * Formats a date for email display
 * @param {Date} date
 * @returns {string}
 */
function formatDate(date) {
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

/**
 * Sends a subscription confirmation email
 * @param {Object} params
 * @param {string} params.email - Recipient email
 * @param {string} params.subscriberName - Name of the subscriber
 * @param {string} params.creatorName - Name of the creator
 * @param {number} params.planDuration - Duration of the subscription in months
 * @param {number} params.amount - Subscription amount
 * @param {Date} params.startDate - Subscription start date
 * @param {Date} params.endDate - Subscription end date
 * @param {string} params.creatorProfileUrl - URL to creator's profile
 */
async function sendSubscriptionConfirmation({
  email,
  subscriberName,
  creatorName,
  planDuration,
  amount,
  startDate,
  endDate,
  creatorProfileUrl,
}) {
  const template = await getTemplate("subscription-confirmation");
  const html = template({
    subscriberName,
    creatorName,
    planDuration,
    amount: amount.toFixed(2),
    startDate: formatDate(startDate),
    endDate: formatDate(endDate),
    creatorProfileUrl,
    year: new Date().getFullYear(),
    logoUrl: "https://erossphere.com/logo.png",
  });

  await transporter.sendMail({
    from: `"Erossphere" <${process.env.EMAIL_FROM}>`,
    to: email,
    subject: "Subscription Confirmation - Erossphere",
    html,
  });
}

/**
 * Generic function to send an email using a template
 * @param {Object} params
 * @param {string} params.email - Recipient email
 * @param {string} params.subject - Email subject
 * @param {string} params.template - Template name
 * @param {Object} params.data - Template data
 */
async function sendTemplatedEmail({ email, subject, template, data }) {
  const compiledTemplate = await getTemplate(template);
  const html = compiledTemplate({
    ...data,
    year: new Date().getFullYear(),
    logoUrl: "https://erossphere.com/logo.png",
  });

  await transporter.sendMail({
    from: `"Erossphere" <${process.env.EMAIL_FROM}>`,
    to: email,
    subject,
    html,
  });
}

// Example function for sending password reset emails
const sendPasswordResetEmail = async (userEmail, resetToken) => {
  const resetUrl = `${process.env.APP_URL}/reset-password?token=${resetToken}`;

  return sendTemplatedEmail({
    email: userEmail,
    subject: "Password Reset Request",
    template: "password-reset",
    data: {
      resetUrl,
    },
  });
};

// Update this function
const sendNewPostNotification = async (subscriber, creator, post) => {
  const postUrl = `${process.env.APP_URL}/post/${post._id}`;

  return sendTemplatedEmail({
    email: subscriber.email,
    subject: `New Post from ${creator.displayName || creator.username}`,
    template: "new-post",
    data: {
      subscriberName: subscriber.displayName || subscriber.username,
      creatorName: creator.displayName || creator.username,
      postContent: post.content,
      postUrl: postUrl,
      appUrl: process.env.APP_URL,
    },
  });
};

const sendRenewalReminder = async (subscriber, creator, subscription) => {
  return sendTemplatedEmail({
    email: subscriber.email,
    subject: "Renewal Reminder",
    template: "renewal-reminder",
    data: {
      creatorName: creator.displayName || creator.username,
      amount: subscription.planSnapshot.amount,
      endDate: subscription.endDate,
      appUrl: process.env.APP_URL,
    },
  });
};

const sendRenewalSuccess = async (subscriber, creator, subscription) => {
  return sendTemplatedEmail({
    email: subscriber.email,
    subject: "Subscription Successfully Renewed",
    template: "renewal-success",
    data: {
      subscriberName: subscriber.name || subscriber.username,
      creatorName: creator.displayName || creator.username,
      amount: subscription.planSnapshot.amount,
      nextRenewalDate: formatDate(
        subscription.nextRenewalDate || subscription.endDate
      ),
      appUrl: process.env.APP_URL,
    },
  });
};

const sendTipNotification = async (recipient, sender, amount) => {
  return sendTemplatedEmail({
    email: recipient.email,
    subject: "You Received a Tip! 🎉",
    template: "tip-notification",
    data: {
      recipientName: recipient.displayName || recipient.username,
      senderName: sender.displayName || sender.username,
      amount: amount.toFixed(2),
      appUrl: process.env.APP_URL,
    },
  });
};

const sendWithdrawalRequestEmail = async (
  user,
  amount,
  transactionId,
  withdrawalAccount
) => {
  const isBankTransfer = withdrawalAccount.paymentMethod === "bank";
  const isCrypto = withdrawalAccount.paymentMethod === "crypto";

  return sendTemplatedEmail({
    email: user.email,
    subject: "Withdrawal Request Confirmation",
    template: "withdrawal-request",
    data: {
      userName: user.displayName || user.username,
      amount: amount.toFixed(2),
      transactionId,
      paymentMethod:
        withdrawalAccount.paymentMethod === "bank"
          ? "Bank Transfer"
          : "Cryptocurrency",
      isBankTransfer,
      isCrypto,
      bankDetails: isBankTransfer ? withdrawalAccount.bankDetails : null,
      cryptoDetails: isCrypto ? withdrawalAccount.cryptoDetails : null,
      appUrl: process.env.APP_URL,
    },
  });
};

/**
 * Sends a verification email with a 6-digit code
 * @param {string} email - Recipient email
 * @param {string} verificationCode - 6-digit verification code
 */
const sendVerificationEmail = async (email, verificationCode) => {
  return sendTemplatedEmail({
    email,
    subject: "Verify Your Email Address - Erossphere",
    template: "email-verification",
    data: {
      verificationCode,
      year: new Date().getFullYear(),
      logoUrl: "https://erossphere.com/logo.png",
    },
  });
};

module.exports = {
  sendSubscriptionConfirmation,
  sendTemplatedEmail,
  sendPasswordResetEmail,
  sendNewPostNotification,
  sendRenewalReminder,
  sendRenewalSuccess,
  sendTipNotification,
  sendWithdrawalRequestEmail,
  sendVerificationEmail,
};
