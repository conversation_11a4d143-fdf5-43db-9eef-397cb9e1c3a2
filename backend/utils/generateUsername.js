const User = require("../models/user");

/**
 * Generates a unique username based on the display name or email
 * @param {string} baseString - The display name or email to base the username on
 * @returns {Promise<string>} A unique username
 */
const generateUniqueUsername = async (baseString) => {
  // Remove special characters and spaces, convert to lowercase
  let baseUsername = baseString
    .toLowerCase()
    .replace(/[^a-zA-Z0-9]/g, "")
    .slice(0, 15); // Limit to 15 characters

  let username = baseUsername;
  let counter = 1;

  // Keep trying until we find a unique username
  while (true) {
    // Check if username exists
    const existingUser = await User.findOne({ username });

    // If no user found with this username, we can use it
    if (!existingUser) {
      return username;
    }

    // If username exists, append number and try again
    username = `${baseUsername}${counter}`;
    counter++;

    // Prevent infinite loop by limiting attempts
    if (counter > 1000) {
      throw new Error("Unable to generate unique username");
    }
  }
};

module.exports = generateUniqueUsername;
