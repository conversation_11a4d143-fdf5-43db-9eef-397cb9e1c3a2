const logger = {
  info: (message) => {
    console.log(`[${new Date().toISOString()}] INFO: ${message}`);
  },
  error: (message, error) => {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error);
  },
};

const createLogger = (name) => {
  return {
    info: (message) => {
      console.log(`[${new Date().toISOString()}] [${name}] INFO: ${message}`);
    },
    error: (message, error) => {
      console.error(
        `[${new Date().toISOString()}] [${name}] ERROR: ${message}`,
        error
      );
    },
  };
};

module.exports = { logger, createLogger };
