const cloudinary = require("cloudinary").v2;
require("dotenv").config();

// Configure cloudinary with environment variables
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Validate configuration
const validateConfig = () => {
  const { cloud_name, api_key, api_secret } = cloudinary.config();
  if (!cloud_name || !api_key || !api_secret) {
    throw new Error(
      "Missing Cloudinary configuration. Please check your environment variables."
    );
  }
  console.log("Cloudinary configured successfully");
};

// Call validation on startup
validateConfig();

/**
 * Uploads a file to Cloudinary
 * @param {string} file - The file path or base64 string
 * @param {string} folder - The destination folder in Cloudinary
 * @returns {Promise<Object>} - Returns the upload result
 */
const uploads = (file, options) => {
  return new Promise((resolve, reject) => {
    // Add default options for video uploads
    const finalOptions = {
      ...options,
      ...(options.resource_type === "video" && {
        chunk_size: 6000000,
        timeout: 120000, // Increase timeout for video uploads
      }),
    };

    cloudinary.uploader.upload(file, finalOptions, (error, result) => {
      if (error) {
        console.error("Cloudinary upload error:", error);
        return reject(error);
      }
      console.log("Upload successful:", result);
      resolve(result);
    });
  });
};

/**
 * Deletes a file from Cloudinary
 * @param {string} public_id - The public ID of the file to delete
 * @returns {Promise<Object>} - Returns the deletion result
 */
const deleteFile = async (public_id) => {
  try {
    if (!public_id) throw new Error("No public_id provided");

    const result = await cloudinary.uploader.destroy(public_id);
    console.log("File deleted successfully:", public_id);
    return result;
  } catch (error) {
    console.error("Cloudinary deletion error:", error);
    throw new Error(`Failed to delete file: ${error.message}`);
  }
};

module.exports = { uploads, deleteFile };
