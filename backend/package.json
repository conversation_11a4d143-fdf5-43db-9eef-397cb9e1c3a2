{"name": "project-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon app.js", "start": "node app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@bull-board/api": "^6.7.9", "@bull-board/express": "^6.7.9", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "bull": "^4.16.5", "cloudinary": "^2.5.1", "compare-versions": "^6.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.21.1", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.4.1", "google-auth-library": "^9.15.1", "handlebars": "^4.7.8", "http-status-codes": "^2.3.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mjml": "^4.15.3", "mongoose": "^8.7.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "numeral": "^2.0.6", "paystack-sdk": "^2.5.19", "socket.io": "^4.8.1", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.1.7"}}