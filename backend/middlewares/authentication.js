const jwt = require("jsonwebtoken");
const { UnauthenticatedError } = require("../errors");
const user = require("../models/user");
const auth = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer")) {
    throw new UnauthenticatedError("Authentication Invalid");
  }

  const token = authHeader.split(" ")[1];

  try {
    const payload = jwt.verify(token, process.env.JWT_SECRET);
    const userAccount = await user.findById(payload.userId).select("-password");

    req.user = userAccount;
    next();
  } catch (error) {
    throw new UnauthenticatedError("Not authorized to access this route");
  }
};

module.exports.adminAuth = async (req, res, next) => {
  if (req.user.role === "Admin")
    throw new UnauthenticatedError(
      "Non-admin are not authorized to access this route"
    );

  next();
};

module.exports = auth;
