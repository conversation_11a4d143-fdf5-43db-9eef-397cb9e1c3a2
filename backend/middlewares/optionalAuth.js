const jwt = require("jsonwebtoken");
const User = require("../models/user");

const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith("Bearer")) {
      const token = authHeader.split(" ")[1];
      const payload = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(payload.userId).select("-password");

      if (user) {
        req.user = user;
      }
    }

    next();
  } catch (error) {
    // If token is invalid or expired, continue without user
    next();
  }
};

module.exports = optionalAuth;
