require("dotenv").config();
const mongoose = require("mongoose");
const Subscription = require("../models/subscription");
const User = require("../models/user");

console.log("Environment variables:", {
  MONGODB_URI: process.env.MONGODB_URI,
  NODE_ENV: process.env.NODE_ENV,
});

async function connectDB() {
  try {
    if (!process.env.MONGODB_URI) {
      throw new Error("MONGODB_URI environment variable is not set");
    }
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("Connected to MongoDB");
  } catch (error) {
    console.error("MongoDB connection failed:", error);
    process.exit(1);
  }
}

async function migratePricingPlans() {
  try {
    await connectDB();
    console.log("Starting migration...");

    const users = await User.find({ isCreator: true });
    console.log(`Found ${users.length} creators to process`);

    for (const user of users) {
      if (!user.pricingPlans || user.pricingPlans.length === 0) {
        // Convert old pricing to new pricingPlans
        const newPricingPlans = (user.pricing || []).map((plan) => ({
          noOfMonths: plan.noOfMonths || 1,
          amount: plan.amount || 1000,
          discountPercentage: plan.discountPercentage || 0,
          status: plan.status || "active",
          title: `${plan.noOfMonths || 1} Month Plan`,
          description: `Subscription for ${plan.noOfMonths || 1} month(s)`,
        }));

        // If no plans exist, create a default one
        if (newPricingPlans.length === 0) {
          newPricingPlans.push({
            noOfMonths: 1,
            amount: 1000,
            discountPercentage: 0,
            status: "active",
            title: "1 Month Plan",
            description: "Default monthly subscription plan",
          });
        }

        user.pricingPlans = newPricingPlans;
        await user.save();
        console.log(`Updated pricing plans for user ${user._id}`);
      }
    }

    const subscriptions = await Subscription.find({});
    console.log(`Found ${subscriptions.length} subscriptions to process`);

    for (const subscription of subscriptions) {
      try {
        let needsUpdate = false;

        // If neither planSnapshot nor plan exists, create default values
        if (!subscription.planSnapshot && !subscription.plan) {
          subscription.planSnapshot = {
            noOfMonths: 1,
            amount: 1000,
            discountPercentage: 0,
            title: "1 Month Plan",
            description: "Default subscription plan",
          };
          needsUpdate = true;
        } else if (!subscription.planSnapshot && subscription.plan) {
          subscription.planSnapshot = {
            noOfMonths: subscription.plan.noOfMonths || 1,
            amount: subscription.plan.amount || 1000,
            discountPercentage: subscription.plan.discountPercentage || 0,
            title: `${subscription.plan.noOfMonths || 1} Month Plan`,
            description: `Subscription for ${
              subscription.plan.noOfMonths || 1
            } month(s)`,
          };
          needsUpdate = true;
        }

        if (!subscription.pricingPlanId) {
          // Find matching pricing plan from creator
          const creator = await User.findById(subscription.creator);
          if (creator) {
            let matchingPlan = creator.pricingPlans.find(
              (plan) =>
                plan.noOfMonths === subscription.planSnapshot.noOfMonths &&
                plan.amount === subscription.planSnapshot.amount
            );

            if (!matchingPlan) {
              console.log(
                `No matching plan found for subscription ${subscription._id}, creating new plan`
              );

              // Create a new plan using the schema
              const newPlan = creator.pricingPlans.create({
                noOfMonths: subscription.planSnapshot.noOfMonths || 1,
                amount: subscription.planSnapshot.amount || 1000,
                discountPercentage:
                  subscription.planSnapshot.discountPercentage || 0,
                status: "active",
                title: subscription.planSnapshot.title || "1 Month Plan",
                description:
                  subscription.planSnapshot.description ||
                  "Default subscription plan",
              });

              // Add the new plan to the creator's pricingPlans array
              creator.pricingPlans.push(newPlan);
              await creator.save();

              // Get the newly created plan
              matchingPlan =
                creator.pricingPlans[creator.pricingPlans.length - 1];
              console.log(
                `Created new plan for creator ${creator._id} with ID ${matchingPlan._id}`
              );
            }

            subscription.pricingPlanId = matchingPlan._id;
            needsUpdate = true;
            console.log(
              `Linked subscription ${subscription._id} to plan ${matchingPlan._id}`
            );
          }
        }

        if (needsUpdate) {
          await subscription.save();
          console.log(`Updated subscription ${subscription._id}`);
        }
      } catch (error) {
        console.error(
          `Error updating subscription ${subscription._id}:`,
          error.message
        );
        continue;
      }
    }

    console.log("Migration completed successfully");
  } catch (error) {
    console.error("Migration failed:", error);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

migratePricingPlans();
