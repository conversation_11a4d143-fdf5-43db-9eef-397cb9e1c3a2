const cron = require("node-cron");
const { handleExpiredSubscriptions } = require("../controllers/subscription");
const Subscription = require("../models/subscription");
const Wallet = require("../models/wallet");
const {
  sendRenewalSuccess,
  sendRenewalReminder,
} = require("../utils/sendEmails");
const { createLogger } = require("../utils/logger");

const logger = createLogger("cronService");

// Handle subscription auto-renewal
const handleAutoRenewal = async () => {
  try {
    logger.info("Starting subscription auto-renewal process");

    // Find subscriptions that are about to expire (within 24 hours)
    const subscriptions = await Subscription.find({
      status: "active",
      endDate: {
        $lte: new Date(Date.now() + 24 * 60 * 60 * 1000),
      },
      autoRenew: true,
    }).populate("subscriber creator");

    logger.info(
      `Found ${subscriptions.length} subscriptions to process for auto-renewal`
    );

    for (const subscription of subscriptions) {
      try {
        if (!subscription.planSnapshot) {
          logger.error(
            `Subscription ${subscription._id} has no plan snapshot defined, skipping renewal`
          );
          continue;
        }

        if (subscription.paymentMethod === "wallet") {
          const wallet = await Wallet.findOne({
            user: subscription.subscriber,
          });

          if (wallet && wallet.balance >= subscription.planSnapshot.amount) {
            // Process renewal
            wallet.balance -= subscription.planSnapshot.amount;
            await wallet.save();

            // Update subscription dates
            const oldEndDate = new Date(subscription.endDate);
            subscription.startDate = oldEndDate;
            subscription.endDate = new Date(
              oldEndDate.setMonth(
                oldEndDate.getMonth() + subscription.planSnapshot.noOfMonths
              )
            );
            subscription.lastRenewalDate = new Date();
            subscription.nextRenewalDate = subscription.endDate;

            subscription.transactionHistory.push({
              transactionId: `renewal-${Date.now()}`,
              amount: subscription.planSnapshot.amount,
              date: new Date(),
              status: "success",
              paymentMethod: "wallet",
              type: "auto-renewal",
            });

            await subscription.save();

            // Send renewal confirmation
            await sendRenewalSuccess(
              subscription.subscriber,
              subscription.creator,
              subscription
            );

            logger.info(
              `Successfully renewed subscription for user ${subscription.subscriber._id} to creator ${subscription.creator._id}`
            );
          } else {
            // Handle insufficient funds
            subscription.status = "expired";
            subscription.autoRenew = false; // Disable auto-renewal due to payment failure
            await subscription.save();

            await sendRenewalReminder(
              subscription.subscriber,
              subscription.creator
            );

            logger.warn(
              `Subscription renewal failed due to insufficient funds for user ${subscription.subscriber._id}`
            );
          }
        }
      } catch (error) {
        logger.error(
          `Error processing subscription ${subscription._id}: ${error.message}`
        );
        // Continue with next subscription even if one fails
        continue;
      }
    }

    // Handle expired subscriptions
    const expiredResult = await Subscription.updateMany(
      {
        status: "active",
        endDate: { $lte: new Date() },
        autoRenew: false,
      },
      {
        $set: {
          status: "expired",
          expirationDate: new Date(),
        },
      }
    );

    logger.info(`Updated ${expiredResult.modifiedCount} expired subscriptions`);
  } catch (error) {
    logger.error(`Error in auto-renewal process: ${error.message}`);
    throw error;
  }
};

// Send renewal reminders for subscriptions about to expire
const sendRenewalReminders = async () => {
  try {
    // Find subscriptions expiring in 3 days
    const expiringSubscriptions = await Subscription.find({
      status: "active",
      endDate: {
        $gte: new Date(),
        $lte: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      },
      autoRenew: false,
      planSnapshot: { $exists: true },
      pricingPlanId: { $exists: true },
    }).populate("subscriber creator");

    for (const subscription of expiringSubscriptions) {
      try {
        if (!subscription.planSnapshot || !subscription.pricingPlanId) {
          logger.error(
            `Subscription ${subscription._id} has missing plan data, skipping reminder`
          );
          continue;
        }

        await sendRenewalReminder(
          subscription.subscriber,
          subscription.creator,
          subscription
        );

        logger.info(
          `Sent renewal reminder for subscription ${subscription._id}`
        );
      } catch (error) {
        logger.error(
          `Error sending renewal reminder for subscription ${subscription._id}: ${error.message}`
        );
      }
    }
  } catch (error) {
    logger.error(`Error in renewal reminder process: ${error.message}`);
  }
};

// Initialize all cron jobs
const initCronJobs = async () => {
  try {
    // Run subscription check and auto-renewal every day at midnight (00:00)
    cron.schedule("0 0 * * *", async () => {
      logger.info("Running daily subscription checks...");
      try {
        await handleExpiredSubscriptions();
        await handleAutoRenewal();
        await sendRenewalReminders();
        logger.info("Daily subscription checks completed successfully");
      } catch (error) {
        logger.error(`Error in daily subscription check: ${error.message}`);
      }
    });

    // Run hourly checks to catch any missed expirations
    cron.schedule("0 * * * *", async () => {
      logger.info("Running hourly subscription checks...");
      try {
        await handleExpiredSubscriptions();
        await handleAutoRenewal();
        logger.info("Hourly subscription checks completed");
      } catch (error) {
        logger.error(`Error in hourly subscription check: ${error.message}`);
      }
    });

    // Run renewal reminders daily at 9 AM
    cron.schedule("0 9 * * *", async () => {
      logger.info("Sending subscription renewal reminders...");
      try {
        await sendRenewalReminders();
        logger.info("Renewal reminders sent successfully");
      } catch (error) {
        logger.error(`Error sending renewal reminders: ${error.message}`);
      }
    });

    logger.info("All cron jobs initialized successfully");
    return Promise.resolve();
  } catch (error) {
    logger.error(`Error initializing cron jobs: ${error.message}`);
    throw error;
  }
};

// Update existing subscriptions for testing
const updateExistingForTesting = async () => {
  try {
    // Update subscriptions to be within 24 hours of expiry
    await Subscription.updateMany(
      { autoRenew: true },
      {
        $set: {
          endDate: new Date(Date.now() + 23 * 60 * 60 * 1000),
          status: "active",
        },
      }
    );

    // Update some subscriptions for reminder testing
    await Subscription.updateMany(
      { autoRenew: false },
      {
        $set: {
          endDate: new Date(Date.now() + 2.5 * 24 * 60 * 60 * 1000),
          status: "active",
        },
      }
    );

    logger.info("Subscriptions updated for testing");
  } catch (error) {
    logger.error(`Error updating subscriptions for testing: ${error.message}`);
  }
};

// Add this test helper function
const createTestSubscriptions = async () => {
  try {
    // Create a subscription that's about to expire in 24 hours (for auto-renewal)
    const autoRenewSubscription = new Subscription({
      status: "active",
      startDate: new Date(),
      endDate: new Date(Date.now() + 23 * 60 * 60 * 1000), // 23 hours from now
      autoRenew: true,
      subscriber: "679f1f14ecaa0fe735425b5b", // Replace with actual test user ID
      creator: "679f1a63ecaa0fe735425ac2", // Replace with actual test creator ID
      paymentMethod: "wallet",
      pricingPlanId: "679f1a9aecaa0fe735425ad0",
      planSnapshot: {
        amount: 1000,
        noOfMonths: 1,
        title: "1 Month Plan",
        description: "Subscription for 1 month(s)",
        discountPercentage: 0,
      },
    });
    await autoRenewSubscription.save();

    // Create a subscription that will trigger renewal reminder (3 days)
    const reminderSubscription = new Subscription({
      status: "active",
      startDate: new Date(),
      endDate: new Date(Date.now() + 2.5 * 24 * 60 * 60 * 1000), // 2.5 days from now
      autoRenew: false,
      paymentMethod: "wallet",
      pricingPlanId: "679f1a9aecaa0fe735425ad0",
      planSnapshot: {
        amount: 1000,
        noOfMonths: 1,
        title: "1 Month Plan",
        description: "Subscription for 1 month(s)",
        discountPercentage: 0,
      },
      subscriber: "679f1f14ecaa0fe735425b5b", // Replace with actual test user ID
      creator: "679f1a63ecaa0fe735425ac2", // Replace with actual test creator ID
    });
    await reminderSubscription.save();

    logger.info("Test subscriptions created successfully");
  } catch (error) {
    logger.error(`Error creating test subscriptions: ${error.message}`);
  }
};

// Add this test function to run both processes
const testRenewalProcesses = async () => {
  try {
    // First create test data
    await createTestSubscriptions();

    // Then run both processes
    await handleAutoRenewal();
    await sendRenewalReminders();

    logger.info("Test renewal processes completed");
  } catch (error) {
    logger.error(`Error in test renewal processes: ${error.message}`);
  }
};

module.exports = {
  initCronJobs,
  handleAutoRenewal,
  sendRenewalReminders,
  updateExistingForTesting,
  testRenewalProcesses,
};
