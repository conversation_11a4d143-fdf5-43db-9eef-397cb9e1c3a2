const express = require("express");
const router = express.Router();

const {
  getUserProfile,
  getUsers,
  updateUser,
  deleteUser,
  getUserPosts,
  getUserFollowers,
  getUserFollowing,
  getRecommendedUsers,
  searchUsers,
  getSuggestedCreators,
  checkUsernameAvailability,
  checkUsernameAvailabilityPublic,
  getCreatorStats,
  trackPageView,
  getCreatorSubscribers,
} = require("../controllers/users");
const authentication = require("../middlewares/authentication");
const optionalAuth = require("../middlewares/optionalAuth");

router.get("/check-username", authentication, checkUsernameAvailability);
router.get("/check-username-public", checkUsernameAvailabilityPublic);
router.get("/search", searchUsers);
router.get("/suggested-creators", optionalAuth, getSuggestedCreators);
router.get("/:username", optionalAuth, getUserProfile);
router.get("/", getUsers);
router.patch("/:id", authentication, updateUser);
router.delete("/:id", authentication, deleteUser);
router.get("/:username/posts", getUserPosts);
router.get("/:username/followers", getUserFollowers);
router.get("/:username/following", getUserFollowing);
router.get("/:username/subscribers", authentication, getCreatorSubscribers);
router.get("/recommended", getRecommendedUsers);
router.get("/:username/stats", authentication, getCreatorStats);
router.post("/:username/track-view", optionalAuth, trackPageView);

module.exports = router;
