const express = require("express");
const router = express.Router();
const authenticate = require("../middlewares/authentication");
const chatController = require("../controllers/chat");

// Get all conversations for the authenticated user
router.get("/conversations", authenticate, chatController.getConversations);

// Create a new conversation
router.post("/conversations", authenticate, chatController.createConversation);

// Get messages for a specific conversation
router.get(
  "/messages/:conversationId",
  authenticate,
  chatController.getMessages
);

// Send a new message
router.post("/messages", authenticate, chatController.sendMessage);

// mark conversation as read
router.post(
  "/conversations/:conversationId/read",
  authenticate,
  chatController.markConversationAsRead
);

module.exports = router;
