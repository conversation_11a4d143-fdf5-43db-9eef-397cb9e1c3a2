const express = require("express");
const router = express.Router();

const authRouter = require("./auth");
const postRouter = require("./post");
const commentRouter = require("./comment");
const subscriptionRouter = require("./subscription");
const userRouter = require("./user");
const notificationRouter = require("./notification");
const walletRouter = require("./wallet");
const uploadRoutes = require("./uploadRoutes");
const emailTestRouter = require("./emailTest");
const chatRouter = require("./chat");
const storyRouter = require("./story");
const automationRouter = require("./automation");
const transactionRouter = require("./transaction");
const withdrawalAccountRouter = require("./withdrawalAccountRoutes");

router.use("/auth", authRouter);
router.use("/posts", postRouter);
router.use("/comments", commentRouter);
router.use("/subscriptions", subscriptionRouter);
router.use("/users", userRouter);
router.use("/notifications", notificationRouter);
router.use("/wallet", walletRouter);
router.use("/upload", uploadRoutes);
router.use("/email", emailTestRouter);
router.use("/chat", chatRouter);
router.use("/stories", storyRouter);
router.use("/automations", automationRouter);
router.use("/transactions", transactionRouter);
router.use("/withdrawal-accounts", withdrawalAccountRouter);
module.exports = router;
