const express = require("express");
const router = express.Router();
const WalletController = require("../controllers/wallet");
const auth = require("../middlewares/authentication");

router.get("/", auth, WalletController.getWallet);
router.post("/credit", auth, WalletController.creditWallet);
router.post("/debit", auth, WalletController.debitWallet);
router.get("/transactions", auth, WalletController.getTransactions);

// New routes for pending balance
router.post("/pending-credit", auth, WalletController.addPendingCredit);
router.post(
  "/confirm-transaction/:transactionId",
  auth,
  WalletController.confirmPendingTransaction
);
router.post(
  "/cancel-transaction/:transactionId",
  auth,
  WalletController.cancelPendingTransaction
);

module.exports = router;
