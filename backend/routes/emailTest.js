const express = require("express");
const router = express.Router();
const nodemailer = require("nodemailer");

router.post("/test-connection", async (req, res) => {
  try {
    // Create test transporter using the same config as in sendEmails.js
    // Create reusable transporter object using SMTP transport
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: 465,
      secure: true,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });

    // Log configuration for debugging (excluding sensitive data)
    console.log("Email Configuration:", {
      host: process.env.EMAIL_HOST,
      port: 465,
      secure: true,
      from: process.env.EMAIL_FROM || "not set",
      user: process.env.EMAIL_USER ? "set" : "not set",
      pass: process.env.EMAIL_PASSWORD ? "set" : "not set",
    });

    // Verify the connection
    await transporter.verify();

    // If verification passes, send a test email
    await transporter.sendMail({
      from: `"Erossphere" <${process.env.EMAIL_FROM}>`, // Use EMAIL_USER as the from address
      to: "<EMAIL>",
      subject: "Email Connection Test",
      text: "If you receive this email, your email configuration is working correctly!",
      html: "<p>If you receive this email, your email configuration is working correctly!</p>",
    });

    res.status(200).json({
      success: true,
      message: "Email connection test successful. Test email sent.",
    });
  } catch (error) {
    console.error("Email test failed:", {
      error: error.message,
      code: error.code,
      response: error.response,
    });
    res.status(500).json({
      success: false,
      message: "Email connection test failed",
      error: error.message,
      details: {
        code: error.code,
        response: error.response,
      },
    });
  }
});

module.exports = router;
