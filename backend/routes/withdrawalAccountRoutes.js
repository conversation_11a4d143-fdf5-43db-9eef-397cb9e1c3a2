const express = require("express");
const router = express.Router();
const authentication = require("../middlewares/authentication");

const {
  createWithdrawalAccount,
  getAllWithdrawalAccounts,
  getWithdrawalAccount,
  updateWithdrawalAccount,
  deleteWithdrawalAccount,
  setDefaultWithdrawalAccount,
} = require("../controllers/withdrawalAccountController");

router.use(authentication);

router.route("/").post(createWithdrawalAccount).get(getAllWithdrawalAccounts);

router
  .route("/:id")
  .get(getWithdrawalAccount)
  .patch(updateWithdrawalAccount)
  .delete(deleteWithdrawalAccount);

router.patch("/:id/set-default", setDefaultWithdrawalAccount);

module.exports = router;
