const express = require("express");
const router = express.Router();
const authenticateUser = require("../middlewares/authentication");
const {
  subscribeToCreator,
  getSubscribers,
  getMySubscriptions,
  // cancelSubscription,
  checkExistingSubscription,
} = require("../controllers/subscription");

router.post("/subscribe", authenticateUser, subscribeToCreator);
router.get("/subscribers", authenticateUser, getSubscribers);
router.get("/my-subscriptions", authenticateUser, getMySubscriptions);
// router.post("/:subscriptionId/cancel", authenticateUser, cancelSubscription);
router.get(
  "/check-existing-subscription/:creatorId",
  authenticateUser,
  checkExistingSubscription
);
module.exports = router;
