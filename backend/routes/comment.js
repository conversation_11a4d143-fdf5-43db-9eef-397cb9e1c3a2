const express = require("express");
const router = express.Router();
const authenticateUser = require("../middlewares/authentication");

const {
  createComment,
  getPostComments,
  updateComment,
  deleteComment,
  likeComment,
  createReply,
  getCommentReplies,
} = require("../controllers/comment");

// Get comments for a post
router.get("/post/:postId", getPostComments);

// Protected routes
router.use(authenticateUser);
router.post("/post/:postId", createComment);
router.patch("/:id", updateComment);
router.delete("/:id", deleteComment);
router.post("/:id/like", likeComment);
router.post("/:commentId/reply", createReply);
router.get("/:commentId/replies", getCommentReplies);

module.exports = router;
