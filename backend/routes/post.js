const express = require("express");
const router = express.Router();
const authenticateUser = require("../middlewares/authentication");

const {
  createPost,
  getAllPosts,
  getSinglePost,
  updatePost,
  deletePost,
  likePost,
  dislikePost,
  getPostsSubscribedTo,
  getUserMedia,
  getPostMedia,
  getUserPosts,
} = require("../controllers/post");
const optionalAuth = require("../middlewares/optionalAuth");

// Public routes
router.get("/", getAllPosts); // Get all posts (can be filtered by query params)

router.get("/media/user/:userId?", optionalAuth, getUserMedia); // Get all media from a user's posts
router.get("/user/:userId", optionalAuth, getUserPosts); // Get all posts from a user
// Protected routes - require authentication
router.use(authenticateUser);

// Subscription route - must be before /:id route
router.get("/subscribed", getPostsSubscribedTo);

// Media routes
router.get("/:id/media", getPostMedia); // Get all media from a specific post

// Regular post routes
router.get("/:id", getSinglePost); // Get single post
router.post("/", createPost); // Create a new post
router.patch("/:id", updatePost); // Update a post
router.delete("/:id", deletePost); // Delete a post
router.patch("/:id/like", likePost); // Like/unlike a post
router.patch("/:id/dislike", dislikePost); // Dislike a post
module.exports = router;
