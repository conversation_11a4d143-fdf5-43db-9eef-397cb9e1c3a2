const express = require("express");
const router = express.Router();
const { uploads } = require("../utils/cloudinary");
const multer = require("multer");

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  // limits: {
  //   fileSize: 5 * 1024 * 1024, // 5MB limit
  // },
});

// Single file upload route
router.post("/single", upload.single("file"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file provided" });
    }

    // Determine file type from mimetype
    const fileType = req.file.mimetype.split("/")[0];

    // Convert buffer to base64
    const fileStr = `data:${
      req.file.mimetype
    };base64,${req.file.buffer.toString("base64")}`;

    // Base upload options
    const uploadOptions = {
      resource_type: "auto",
      folder: "erossphere/media",
      public_id: `${Date.now()}-${Math.random().toString(36).substring(7)}`,
    };

    // Add specific options for images and videos if needed
    if (fileType === "image") {
      uploadOptions.transformation = [
        { width: 800, crop: "limit" },
        { quality: "auto" },
      ];
    } else if (fileType === "video") {
      uploadOptions.chunk_size = 6000000;
      uploadOptions.eager = [
        {
          width: 300,
          height: 300,
          crop: "pad",
          audio_codec: "none",
          format: "mp4",
        },
        {
          width: 160,
          height: 100,
          crop: "crop",
          gravity: "south",
          audio_codec: "none",
          format: "mp4",
        },
      ];
      uploadOptions.eager_async = true;
      uploadOptions.format = "mp4";
    }

    // Upload to cloudinary
    const uploadedMedia = await uploads(fileStr, uploadOptions);

    res.status(200).json({
      success: true,
      message: "Upload successful",
      data: {
        url: uploadedMedia.secure_url || uploadedMedia.url,
        public_id: uploadedMedia.public_id.replace("erossphere/media/", ""),
        asset_id: uploadedMedia.asset_id,
        type: fileType,
        format: uploadedMedia.format,
        duration: uploadedMedia.duration,
        width: uploadedMedia.width,
        height: uploadedMedia.height,
        thumbnail:
          fileType === "video" && uploadedMedia.eager
            ? uploadedMedia.eager[0].secure_url || uploadedMedia.eager[0].url
            : null,
      },
    });
  } catch (error) {
    console.error("Upload error:", error);
    res.status(500).json({
      success: false,
      message: "Upload failed",
      error: error.message,
    });
  }
});

// Multiple media upload route
router.post("/multiple", upload.array("files", 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: "No files provided" });
    }

    console.log("Files received:", req.files.length);

    const uploadPromises = req.files.map(async (file) => {
      try {
        // Determine file type from mimetype
        const fileType = file.mimetype.split("/")[0];

        // Convert buffer to base64
        const fileStr = `data:${file.mimetype};base64,${file.buffer.toString(
          "base64"
        )}`;

        // Base upload options
        const uploadOptions = {
          resource_type: "auto",
          folder: "errorsphere/media",
          public_id: `${Date.now()}-${Math.random().toString(36).substring(7)}`,
        };

        // Add specific options for images and videos if needed
        if (fileType === "image") {
          uploadOptions.transformation = [
            { width: 800, crop: "limit" },
            { quality: "auto" },
          ];
        } else if (fileType === "video") {
          uploadOptions.chunk_size = 6000000;
          uploadOptions.eager = [
            {
              width: 300,
              height: 300,
              crop: "pad",
              audio_codec: "none",
              format: "mp4",
            },
            {
              width: 160,
              height: 100,
              crop: "crop",
              gravity: "south",
              audio_codec: "none",
              format: "mp4",
            },
          ];
          uploadOptions.eager_async = true;
          uploadOptions.format = "mp4";
        }

        console.log(`Uploading ${fileType} file with options:`, uploadOptions);

        const uploadedMedia = await uploads(fileStr, uploadOptions);
        console.log(`Upload response for ${fileType}:`, uploadedMedia);

        return {
          url: uploadedMedia.secure_url || uploadedMedia.url,
          public_id: uploadedMedia.public_id.replace("erossphere/media/", ""),
          asset_id: uploadedMedia.asset_id,
          type: fileType,
          format: uploadedMedia.format,
          duration: uploadedMedia.duration,
          width: uploadedMedia.width,
          height: uploadedMedia.height,
          thumbnail:
            fileType === "video" && uploadedMedia.eager
              ? uploadedMedia.eager[0].secure_url || uploadedMedia.eager[0].url
              : null,
        };
      } catch (err) {
        console.error("Individual file upload error:", err);
        console.error("Error details:", err.message);
        return null;
      }
    });

    const uploadedMedia = await Promise.all(uploadPromises);
    const successfulUploads = uploadedMedia.filter((media) => media !== null);

    if (successfulUploads.length === 0) {
      return res.status(500).json({
        success: false,
        message: "All uploads failed",
        data: [],
      });
    }

    res.status(200).json({
      success: true,
      message:
        successfulUploads.length === req.files.length
          ? "All uploads successful"
          : `${successfulUploads.length} of ${req.files.length} uploads successful`,
      data: successfulUploads,
    });
  } catch (error) {
    console.error("Upload error:", error);
    res.status(500).json({
      success: false,
      message: "Upload failed",
      error: error.message,
    });
  }
});

module.exports = router;
