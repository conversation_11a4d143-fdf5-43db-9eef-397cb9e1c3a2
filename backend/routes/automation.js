const express = require("express");
const router = express.Router();
const authentication = require("../middlewares/authentication");
const {
  createAutomation,
  getCreatorAutomations,
  updateAutomation,
  deleteAutomation,
  toggleAutomation,
} = require("../controllers/automation");

// All routes require authentication
router.use(authentication);

router.route("/").post(createAutomation).get(getCreatorAutomations);

router.route("/:id").patch(updateAutomation).delete(deleteAutomation);

router.patch("/:id/toggle", toggleAutomation);

module.exports = router;
