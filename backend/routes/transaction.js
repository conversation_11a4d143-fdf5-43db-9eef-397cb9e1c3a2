const express = require("express");
const router = express.Router();
const {
  createTip,
  requestWithdrawal,
  processDeposit,
  getTransactionHistory,
  getPendingWithdrawals,
  cancelWithdrawal,
  getWithdrawalAccounts,
  getCreatorEarnings,
} = require("../controllers/transaction");
const authentication = require("../middlewares/authentication");

router.use(authentication);

router.post("/tip", createTip);
router.post("/withdrawal", requestWithdrawal);
router.post("/deposit", processDeposit);
router.get("/history", getTransactionHistory);
router.get("/pending-withdrawals", getPendingWithdrawals);
router.get("/withdrawal-accounts", getWithdrawalAccounts);
router.get("/earnings", getCreatorEarnings);
router.post("/withdrawal/:transactionId/cancel", cancelWithdrawal);

module.exports = router;
