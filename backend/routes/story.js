const express = require("express");
const router = express.Router();
const storyController = require("../controllers/story");
const auth = require("../middlewares/authentication");

// Create a new story
router.post("/", auth, storyController.createStory);

// Get stories for user's feed
router.get("/feed", auth, storyController.getFeedStories);

// Get stories by user ID
router.get("/user/:userId", auth, storyController.getUserStories);

// Mark story as viewed
router.post("/:storyId/view", auth, storyController.viewStory);

// Delete story
router.delete("/:storyId", auth, storyController.deleteStory);

// View story
router.post("/:storyId/view", auth, storyController.viewStory);

module.exports = router;
