const Notification = require("../models/notification");

// Get user's notifications
const getNotifications = async (req, res) => {
  console.log("Query:", req.query);
  console.log("User ID:", req.user._id);

  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const skip = (page - 1) * limit;

    // Build filter query
    const filterQuery = { recipient: req.user._id };

    if (req.query.status === "read") {
      filterQuery.read = true;
    } else if (req.query.status === "unread") {
      filterQuery.read = false;
    }

    if (req.query.type && req.query.type !== "all") {
      filterQuery.type = req.query.type;
    }

    // Debug: Log the filter query
    console.log("Filter Query:", filterQuery);

    // Get total count for pagination
    const totalNotifications = await Notification.countDocuments(filterQuery);
    console.log("Total notifications count:", totalNotifications);

    // Get total unread count without filters
    const totalUnread = await Notification.countDocuments({
      recipient: req.user._id,
      read: false,
    });

    // Get notifications with filters and pagination
    const notifications = await Notification.find(filterQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate("sender", "username avatar")
      .populate("post", "content")
      .populate("comment", "content");

    // Debug: Log the found notifications
    // console.log("Found notifications:", notifications);

    res.json({
      notifications,
      currentPage: page,
      totalPages: Math.ceil(totalNotifications / limit),
      totalNotifications,
      hasMore: page < Math.ceil(totalNotifications / limit),
      totalUnread,
    });
  } catch (error) {
    console.error("Error in getNotifications:", error);
    res
      .status(500)
      .json({ message: "Error fetching notifications", error: error.message });
  }
};

// Mark notification as read
const markNotificationAsRead = async (req, res) => {
  try {
    const notification = await Notification.findOne({
      _id: req.params.notificationId,
      recipient: req.user._id, // Ensure notification belongs to current user
    });

    if (!notification) {
      return res.status(404).json({
        message:
          "Notification not found or you don't have permission to access it",
      });
    }

    notification.read = true;
    await notification.save();

    res.json(notification);
  } catch (error) {
    res.status(500).json({
      message: "Error updating notification",
      error: error.message,
    });
  }
};

module.exports = { getNotifications, markNotificationAsRead };
