const { Message, Conversation } = require("../models/chat");
const { StatusCodes } = require("http-status-codes");
const { NotFoundError } = require("../errors");
const { User } = require("../models/user");

exports.getConversations = async (req, res) => {
  try {
    const conversations = await Conversation.find({
      participants: req.user._id,
    })
      .populate("participants", "username email avatar displayName")
      .populate({
        path: "lastMessage",
        populate: [
          {
            path: "sender",
            select: "username avatar",
          },
          {
            path: "readBy",
            select: "username _id",
          },
        ],
      })
      .sort("-updatedAt");

    // Get unread counts for each conversation
    const conversationsWithCounts = await Promise.all(
      conversations.map(async (conv) => {
        const unreadCount = await Message.countDocuments({
          conversation: conv._id,
          sender: { $ne: req.user._id },
          readBy: { $ne: req.user._id },
        });

        return {
          ...conv.toObject(),
          unreadCount,
        };
      })
    );

    // Calculate total unread messages across all conversations
    const totalUnreadCount = conversationsWithCounts.reduce(
      (total, conv) => total + conv.unreadCount,
      0
    );

    res.json({
      conversations: conversationsWithCounts,
      totalUnreadCount,
    });
  } catch (error) {
    console.error("Error fetching conversations:", error);
    res.status(500).json({ message: "Error fetching conversations" });
  }
};

exports.getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;

    // Verify the conversation exists and user is a participant
    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: req.user._id,
    });

    if (!conversation) {
      return res.status(404).json({ message: "Conversation not found" });
    }

    const messages = await Message.find({
      conversation: conversationId,
    })
      .populate("sender", "username email avatar displayName")
      .populate("readBy", "username _id")
      .sort("createdAt");

    // Get unread count for this conversation
    const unreadCount = await Message.countDocuments({
      conversation: conversationId,
      sender: { $ne: req.user._id },
      readBy: { $ne: req.user._id },
    });

    // Mark messages as read
    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: req.user._id },
        readBy: { $ne: req.user._id },
      },
      { $addToSet: { readBy: req.user._id } }
    );

    // Emit socket event to notify sender that messages were read
    const io = req.app.get("io");
    if (io) {
      const otherParticipant = conversation.participants.find(
        (p) => p.toString() !== req.user._id.toString()
      );
      io.to(otherParticipant.toString()).emit("messages_read", {
        conversationId,
        readBy: req.user._id,
      });
    }

    res.json({
      messages,
      unreadCount,
    });
  } catch (error) {
    console.error("Error fetching messages:", error);
    res.status(500).json({ message: "Error fetching messages" });
  }
};

exports.sendMessage = async (req, res) => {
  const { receiverId, content, messageType, media, conversationId } = req.body;

  // Find existing conversation or create new one
  let conversation = await Conversation.findOne({
    participants: { $all: [req.user._id, receiverId] },
  });

  if (!conversation) {
    conversation = await Conversation.create({
      participants: [req.user._id, receiverId],
      messages: [],
    });
  }

  // Create new message with media support
  const messageData = {
    sender: req.user._id,
    receiver: receiverId,
    content,
    conversation: conversation._id,
    readBy: [req.user._id], // Mark as read by sender
    messageType: messageType || "text",
  };

  // Add media data if present
  if (media && typeof media === "object") {
    messageData.media = {
      url: media.url || "",
      mediaType: media.mediaType || "",
      filename: media.filename || "",
      duration: media.duration || 0,
    };
  }
  // console.log("messageData", messageData);

  const message = await Message.create(messageData);

  // Update conversation's last message and messages array
  conversation.lastMessage = message._id;
  conversation.messages.push(message._id);
  await conversation.save();

  // Populate message with sender info and readBy for the response
  await message.populate([
    {
      path: "sender",
      select: "username email avatar displayName",
    },
    {
      path: "conversation",
      select: "_id",
    },
    {
      path: "readBy",
      select: "username _id",
    },
  ]);

  res.json(message);
};

// Create a new conversation
exports.createConversation = async (req, res) => {
  try {
    const { receiverId } = req.body;

    // Check if conversation already exists
    let conversation = await Conversation.findOne({
      participants: { $all: [req.user._id, receiverId] },
    }).populate("participants", "username email avatar displayName");

    if (conversation) {
      return res.json(conversation);
    }

    // Create new conversation
    conversation = await Conversation.create({
      participants: [req.user._id, receiverId],
      messages: [],
    });

    // Populate the participants information
    await conversation.populate(
      "participants",
      "username email avatar displayName"
    );

    res.status(201).json(conversation);
  } catch (error) {
    console.error("Error creating conversation:", error);
    res.status(500).json({ message: "Error creating conversation" });
  }
};

exports.markConversationAsRead = async (req, res) => {
  const { conversationId } = req.params;
  const userId = req.user._id;

  // Verify conversation exists and user is a participant
  const conversation = await Conversation.findOne({
    _id: conversationId,
    participants: userId,
  });

  if (!conversation) {
    throw new NotFoundError("Conversation not found");
  }

  // Mark all messages in the conversation as read by the user
  await Message.updateMany(
    {
      conversation: conversationId,
      sender: { $ne: userId },
      readBy: { $ne: userId },
    },
    {
      $addToSet: { readBy: userId },
    }
  );

  res.status(StatusCodes.OK).json({ message: "Messages marked as read" });
};
