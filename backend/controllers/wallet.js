const Wallet = require("../models/wallet");
const Transaction = require("../models/transaction");
const { validateWalletTransaction } = require("../validators/walletValidator");
const { sendWalletNotification } = require("../utils/sendEmails");

class WalletController {
  // Initialize or get user wallet
  static async getWallet(req, res) {
    try {
      const userId = req.user.id;

      let wallet = await Wallet.findOne({ userId });

      if (!wallet) {
        // Create new wallet if doesn't exist
        wallet = await Wallet.create({
          userId,
          balance: 0,
          currency: "NGN",
        });
      }

      return res.status(200).json({
        status: "success",
        data: wallet,
      });
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Error fetching wallet",
        error: error.message,
      });
    }
  }

  // Credit wallet
  static async creditWallet(req, res) {
    try {
      const { amount, transactionRef, paymentMethod } = req.body;
      const userId = req.user.id;

      const { error } = validateWalletTransaction({ amount });
      if (error) {
        return res.status(400).json({
          status: "error",
          message: error.details[0].message,
        });
      }

      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        return res.status(404).json({
          status: "error",
          message: "Wallet not found",
        });
      }

      // Create transaction record
      const transaction = await Transaction.create({
        userId,
        type: "credit",
        amount,
        reference: transactionRef,
        paymentMethod,
        status: "success",
        balance: wallet.balance + amount,
      });

      // Update wallet balance
      wallet.balance += amount;
      await wallet.save();

      //   // Send notification
      //   await sendWalletNotification({
      //     email: req.user.email,
      //     type: "credit",
      //     amount,
      //     balance: wallet.balance,
      //   });

      return res.status(200).json({
        status: "success",
        message: "Wallet credited successfully",
        data: { transaction, wallet },
      });
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Error crediting wallet",
        error: error.message,
      });
    }
  }

  // Debit wallet
  static async debitWallet(req, res) {
    try {
      const { amount, purpose } = req.body;
      const userId = req.user.id;

      const { error } = validateWalletTransaction({ amount });
      if (error) {
        return res.status(400).json({
          status: "error",
          message: error.details[0].message,
        });
      }

      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        return res.status(404).json({
          status: "error",
          message: "Wallet not found",
        });
      }

      if (wallet.balance < amount) {
        return res.status(400).json({
          status: "error",
          message: "Insufficient funds",
        });
      }

      // Create transaction record
      const transaction = await Transaction.create({
        userId,
        type: "debit",
        amount,
        purpose,
        status: "success",
        balance: wallet.balance - amount,
      });

      // Update wallet balance
      wallet.balance -= amount;
      await wallet.save();

      // Send notification
      //   await sendWalletNotification({
      //     email: req.user.email,
      //     type: "debit",
      //     amount,
      //     balance: wallet.balance,
      //   });

      return res.status(200).json({
        status: "success",
        message: "Wallet debited successfully",
        data: { transaction, wallet },
      });
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Error debiting wallet",
        error: error.message,
      });
    }
  }

  // Get wallet transactions
  static async getTransactions(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 10 } = req.query;

      const transactions = await Transaction.find({ userId })
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit);

      const total = await Transaction.countDocuments({ userId });

      return res.status(200).json({
        status: "success",
        data: {
          transactions,
          pagination: {
            total,
            page: parseInt(page),
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Error fetching transactions",
        error: error.message,
      });
    }
  }

  // Add pending credit to wallet
  static async addPendingCredit(req, res) {
    try {
      const { amount, transactionRef, paymentMethod } = req.body;
      const userId = req.user.id;

      const { error } = validateWalletTransaction({ amount });
      if (error) {
        return res.status(400).json({
          status: "error",
          message: error.details[0].message,
        });
      }

      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        return res.status(404).json({
          status: "error",
          message: "Wallet not found",
        });
      }

      // Create pending transaction record
      const transaction = await Transaction.create({
        userId,
        type: "credit",
        amount,
        reference: transactionRef,
        paymentMethod,
        status: "pending",
        isPending: true,
        balance: wallet.balance,
        pendingBalance: wallet.pendingBalance + amount,
      });

      // Update wallet pending balance
      wallet.pendingBalance += amount;
      await wallet.save();

      return res.status(200).json({
        status: "success",
        message: "Pending credit added successfully",
        data: { transaction, wallet },
      });
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Error adding pending credit",
        error: error.message,
      });
    }
  }

  // Confirm pending transaction
  static async confirmPendingTransaction(req, res) {
    try {
      const { transactionId } = req.params;
      const userId = req.user.id;

      const transaction = await Transaction.findOne({
        _id: transactionId,
        userId,
        isPending: true,
      });

      if (!transaction) {
        return res.status(404).json({
          status: "error",
          message: "Pending transaction not found",
        });
      }

      const wallet = await Wallet.findOne({ userId });

      // Move amount from pending to available balance
      if (transaction.type === "credit") {
        wallet.pendingBalance -= transaction.amount;
        wallet.balance += transaction.amount;
      }

      // Update transaction status
      transaction.status = "success";
      transaction.isPending = false;
      transaction.balance = wallet.balance;
      transaction.pendingBalance = wallet.pendingBalance;

      await Promise.all([wallet.save(), transaction.save()]);

      // Send notification
      //   await sendWalletNotification({
      //     email: req.user.email,
      //     type: "credit_confirmed",
      //     amount: transaction.amount,
      //     balance: wallet.balance,
      //   });

      return res.status(200).json({
        status: "success",
        message: "Transaction confirmed successfully",
        data: { transaction, wallet },
      });
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Error confirming transaction",
        error: error.message,
      });
    }
  }

  // Cancel pending transaction
  static async cancelPendingTransaction(req, res) {
    try {
      const { transactionId } = req.params;
      const userId = req.user.id;

      const transaction = await Transaction.findOne({
        _id: transactionId,
        userId,
        isPending: true,
      });

      if (!transaction) {
        return res.status(404).json({
          status: "error",
          message: "Pending transaction not found",
        });
      }

      const wallet = await Wallet.findOne({ userId });

      // Remove amount from pending balance
      if (transaction.type === "credit") {
        wallet.pendingBalance -= transaction.amount;
      }

      // Update transaction status
      transaction.status = "failed";
      transaction.isPending = false;
      transaction.pendingBalance = wallet.pendingBalance;

      await Promise.all([wallet.save(), transaction.save()]);

      return res.status(200).json({
        status: "success",
        message: "Transaction cancelled successfully",
        data: { transaction, wallet },
      });
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Error cancelling transaction",
        error: error.message,
      });
    }
  }
}

module.exports = WalletController;
