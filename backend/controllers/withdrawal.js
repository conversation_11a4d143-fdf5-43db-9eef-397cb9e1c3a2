const { Paystack } = require("../utils/paystack");

const addWithdrawalAccount = async (req, res) => {
  try {
    const params = {
      type: "nuban",
      name: "<PERSON><PERSON> <PERSON>",
      account_number: "***********",
      bank_code: "058",
      currency: "NGN",
    };

    const response = await Paystack.transferRecipient(params);
    res.json(response.data);
  } catch (error) {
    res.status(error.response?.status || 500).json({
      error: error.response?.data || error.message,
    });
  }
};

const fetchWithdrawalAccount = async (req, res) => {
  try {
    const { id_or_code } = req.params;
    const response = await Paystack.transferRecipient(id_or_code);
    res.json(response.data);
  } catch (error) {
    res.status(error.response?.status || 500).json({
      error: error.response?.data || error.message,
    });
  }
};

const updateWithdrawalAccount = async (req, res) => {
  try {
    const { id_or_code } = req.params;
    const params = {
      name: "<PERSON>",
    };

    const response = await Paystack.put(
      `/transferrecipient/${id_or_code}`,
      params
    );
    res.json(response.data);
  } catch (error) {
    res.status(error.response?.status || 500).json({
      error: error.response?.data || error.message,
    });
  }
};

const deleteWithdrawalAccount = async (req, res) => {
  try {
    const { id_or_code } = req.params;
    const response = await Paystack.delete(`/transferrecipient/${id_or_code}`);
    res.json(response.data);
  } catch (error) {
    res.status(error.response?.status || 500).json({
      error: error.response?.data || error.message,
    });
  }
};

const makeWithdrawal = async (req, res) => {
  try {
    const params = {
      source: "balance",
      reason: "Calm down",
      amount: 3794800,
      recipient: "RCP_gx2wn530m0i3w3m",
    };

    const response = await Paystack.post("/transfer", params);
    res.json(response.data);
  } catch (error) {
    res.status(error.response?.status || 500).json({
      error: error.response?.data || error.message,
    });
  }
};

module.exports = {
  addWithdrawalAccount,
  fetchWithdrawalAccount,
  updateWithdrawalAccount,
  deleteWithdrawalAccount,
  makeWithdrawal,
};

// const addWithdrawalAccount = async (req, res) => {
//   try {
//     const { accountNumber, bankCode, accountName } = req.body;

//     if (!accountNumber || !bankCode || !accountName) {
//       return res.status(400).json({
//         error: "Account number, bank code, and account name are required"
//       });
//     }

//     const params = {
//       type: "nuban",
//       name: accountName,
//       account_number: accountNumber,
//       bank_code: bankCode,
//       currency: "NGN",
//     };

//     const paystackResponse = await Paystack.post("/transferrecipient", params);

//     const withdrawalAccount = new WithdrawalAccount({
//       userId: req.user._id,
//       recipientCode: paystackResponse.data.data.recipient_code,
//       accountName,
//       accountNumber,
//       bankCode,
//       bankName: paystackResponse.data.data.details.bank_name,
//       currency: "NGN"
//     });

//     await withdrawalAccount.save();

//     res.status(201).json(withdrawalAccount);
//   } catch (error) {
//     res.status(error.response?.status || 500).json({
//       error: error.response?.data || error.message,
//     });
//   }
// };

// const fetchWithdrawalAccount = async (req, res) => {
//   try {
//     const withdrawalAccounts = await WithdrawalAccount.find({
//       userId: req.user._id,
//       isActive: true
//     });

//     res.json(withdrawalAccounts);
//   } catch (error) {
//     res.status(500).json({
//       error: error.message,
//     });
//   }
// };

// const updateWithdrawalAccount = async (req, res) => {
//   try {
//     const { id } = req.params;
//     const { accountName } = req.body;

//     const withdrawalAccount = await WithdrawalAccount.findOne({
//       _id: id,
//       userId: req.user._id
//     });

//     if (!withdrawalAccount) {
//       return res.status(404).json({ error: "Withdrawal account not found" });
//     }

//     await Paystack.put(
//       `/transferrecipient/${withdrawalAccount.recipientCode}`,
//       { name: accountName }
//     );

//     withdrawalAccount.accountName = accountName;
//     await withdrawalAccount.save();

//     res.json(withdrawalAccount);
//   } catch (error) {
//     res.status(error.response?.status || 500).json({
//       error: error.response?.data || error.message,
//     });
//   }
// };

// const deleteWithdrawalAccount = async (req, res) => {
//   try {
//     const { id } = req.params;

//     const withdrawalAccount = await WithdrawalAccount.findOne({
//       _id: id,
//       userId: req.user._id
//     });

//     if (!withdrawalAccount) {
//       return res.status(404).json({ error: "Withdrawal account not found" });
//     }

//     await Paystack.delete(
//       `/transferrecipient/${withdrawalAccount.recipientCode}`
//     );

//     withdrawalAccount.isActive = false;
//     await withdrawalAccount.save();

//     res.json({ message: "Withdrawal account deleted successfully" });
//   } catch (error) {
//     res.status(error.response?.status || 500).json({
//       error: error.response?.data || error.message,
//     });
//   }
// };

// const makeWithdrawal = async (req, res) => {
//   try {
//     const { amount, accountId, reason } = req.body;

//     if (!amount || !accountId) {
//       return res.status(400).json({
//         error: "Amount and account ID are required"
//       });
//     }

//     const withdrawalAccount = await WithdrawalAccount.findOne({
//       _id: accountId,
//       userId: req.user._id,
//       isActive: true
//     });

//     if (!withdrawalAccount) {
//       return res.status(404).json({ error: "Withdrawal account not found" });
//     }

//     const params = {
//       source: "balance",
//       reason: reason || "Withdrawal",
//       amount: amount * 100,
//       recipient: withdrawalAccount.recipientCode
//     };

//     const response = await Paystack.post("/transfer", params);
//     res.json(response.data);
//   } catch (error) {
//     res.status(error.response?.status || 500).json({
//       error: error.response?.data || error.message,
//     });
//   }
// };
