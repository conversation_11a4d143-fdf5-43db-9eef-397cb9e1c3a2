const Story = require("../models/story");
const Subscription = require("../models/subscription");
const { NotFoundError, BadRequestError } = require("../errors");
const { StatusCodes } = require("http-status-codes");

// Create a new story
exports.createStory = async (req, res) => {
  try {
    const {
      mediaUrl: mediaUrl,
      mediaType: mediaType,
      public_id,
      asset_id,
      format,
      duration,
      thumbnail,
      visibility,
    } = req.body;

    if (!mediaUrl || !mediaType) {
      throw new BadRequestError("Media URL and type are required");
    }

    // Validate video duration
    if (mediaType === "video" && duration > 60) {
      throw new BadRequestError("Video duration must be less than 1 minute");
    }

    // Validate visibility
    if (visibility && !["public", "subscribers"].includes(visibility)) {
      throw new BadRequestError("Invalid visibility option");
    }

    const story = new Story({
      creator: req.user._id,
      mediaUrl,
      mediaType,
      public_id,
      asset_id,
      format,
      duration,
      thumbnail,
      visibility: visibility || "public",
    });

    await story.save();

    // Populate creator info before sending response
    await story.populate("creator", "name username avatar");

    res.status(StatusCodes.CREATED).json({ story });
  } catch (error) {
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({ message: error.message });
  }
};

// Get stories for user's feed (stories from subscribed creators)
exports.getFeedStories = async (req, res) => {
  try {
    // Get all active subscriptions for the current user
    const activeSubscriptions = await Subscription.find({
      subscriber: req.user._id,
      status: "active",
      endDate: { $gt: new Date() },
    }).distinct("creator");

    // Build the query conditions
    const queryConditions = {
      createdAt: { $gt: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
      $or: [
        // User's own stories
        { creator: req.user._id },
        // Public stories from anyone
        { visibility: "public" },
        // Subscriber-only stories from creators user is subscribed to
        {
          $and: [
            { creator: { $in: activeSubscriptions } },
            { visibility: "subscribers" },
          ],
        },
      ],
    };

    const stories = await Story.find(queryConditions)
      .populate("creator", "name username avatar")
      .sort("-createdAt");

    res.status(StatusCodes.OK).json({ stories });
  } catch (error) {
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({ message: error.message });
  }
};

// Get stories by user ID
exports.getUserStories = async (req, res) => {
  try {
    // Check if the requesting user is subscribed to the target user
    const isSubscribed = await Subscription.exists({
      subscriber: req.user._id,
      creator: req.params.userId,
      status: "active",
      endDate: { $gt: new Date() },
    });

    // Build query conditions based on subscription status
    const queryConditions = {
      creator: req.params.userId,
      createdAt: { $gt: new Date(Date.now() - 24 * 60 * 60 * 1000) },
      $or: [
        { visibility: "public" },
        { creator: req.user._id }, // User's own stories
      ],
    };

    // Add subscriber stories condition if user is subscribed
    if (isSubscribed) {
      queryConditions.$or.push({ visibility: "subscribers" });
    }

    const stories = await Story.find(queryConditions)
      .populate("creator", "name username avatar")
      .sort("-createdAt")
      .populate("viewers.user", "username avatar");
    if (!stories) {
      throw new NotFoundError("No stories found for this user");
    }
    res.status(StatusCodes.OK).json({ stories });
  } catch (error) {
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({ message: error.message });
  }
};

// Mark story as viewed
exports.viewStory = async (req, res) => {
  try {
    const story = await Story.findById(req.params.storyId);
    if (!story) {
      throw new NotFoundError("Story not found");
    }

    // Check if user has already viewed the story
    const alreadyViewed = story.viewers.some(
      (viewer) => viewer.user.toString() === req.user._id.toString()
    );

    if (!alreadyViewed) {
      story.viewers.push({ user: req.user._id });
      await story.save();
    }

    res.status(StatusCodes.OK).json({ story });
  } catch (error) {
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({ message: error.message });
  }
};

// Delete story
exports.deleteStory = async (req, res) => {
  try {
    const story = await Story.findOneAndDelete({
      _id: req.params.storyId,
      creator: req.user._id,
    });

    if (!story) {
      throw new NotFoundError("Story not found or unauthorized");
    }

    res.status(StatusCodes.OK).json({ message: "Story deleted successfully" });
  } catch (error) {
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({ message: error.message });
  }
};
