const Transaction = require("../models/transaction");
const Wallet = require("../models/wallet");
const User = require("../models/user");
const WithdrawalAccount = require("../models/withdrawalAccount");
const { BadRequestError } = require("../errors");
const {
  createTipNotification,
  sendTipNotification,
  sendWithdrawalRequestEmail,
} = require("../utils/notification");

// Process tip transaction
const createTip = async (req, res) => {
  const { recipientId, amount, paymentMethod, transactionReference, tipType } =
    req.body;
  const userId = req.user._id;

  // Validate required tipType
  if (!tipType) {
    throw new BadRequestError("Tip type is required");
  }

  // Prevent self-tipping
  if (recipientId.toString() === userId.toString()) {
    throw new BadRequestError("You cannot tip yourself");
  }

  let paymentSuccess = false;
  let walletBalance;

  if (paymentMethod === "wallet") {
    const wallet = await Wallet.findOne({ userId });
    if (wallet.balance >= amount) {
      wallet.balance -= amount;
      walletBalance = wallet.balance;
      await wallet.save();
      paymentSuccess = true;
    } else {
      throw new BadRequestError("Insufficient wallet balance");
    }
  } else if (paymentMethod === "paystack" || paymentMethod === "flutterwave") {
    paymentSuccess = true;
  }

  if (!paymentSuccess) {
    throw new BadRequestError("Payment failed");
  }

  // Create debit transaction for sender
  const senderTransaction = await Transaction.create({
    userId,
    recipientId,
    transactionType: "debit",
    amount,
    paymentMethod,
    purpose: "tip",
    tipType,
    status: "success",
    isPending: false,
    balance: walletBalance,
    reference: transactionReference,
  });

  // Create credit transaction for recipient
  const recipientTransaction = await Transaction.create({
    userId: recipientId,
    recipientId: userId,
    transactionType: "credit",
    amount,
    paymentMethod,
    purpose: "tip",
    tipType,
    status: "success",
    isPending: false,
    reference: transactionReference,
  });

  // Credit recipient's wallet
  await Wallet.findOneAndUpdate(
    { userId: recipientId },
    { $inc: { balance: amount } }
  );

  // Create notification for tip
  await createTipNotification(userId, recipientId, amount);

  // Send email notification
  const sender = await User.findById(userId);
  const recipient = await User.findById(recipientId);
  await sendTipNotification(recipient, sender, amount);

  res.status(201).json({
    senderTransaction,
    recipientTransaction,
  });
};

// Process withdrawal request
const requestWithdrawal = async (req, res) => {
  const { amount, withdrawalAccountId } = req.body;
  const userId = req.user._id;

  // Validate amount
  if (!amount || amount <= 0) {
    throw new BadRequestError("Invalid withdrawal amount");
  }

  if (!withdrawalAccountId) {
    throw new BadRequestError("Withdrawal account is required");
  }

  // Get withdrawal account and verify ownership
  const withdrawalAccount = await WithdrawalAccount.findOne({
    _id: withdrawalAccountId,
    user: userId,
    status: "active",
  });

  if (!withdrawalAccount) {
    throw new BadRequestError("Invalid or inactive withdrawal account");
  }

  if (!withdrawalAccount.isVerified) {
    throw new BadRequestError("Withdrawal account is not verified");
  }

  // Check minimum withdrawal amount (e.g., $50)
  const MIN_WITHDRAWAL = 50;
  if (amount < MIN_WITHDRAWAL) {
    throw new BadRequestError(
      `Minimum withdrawal amount is $${MIN_WITHDRAWAL}`
    );
  }

  // Get user's wallet
  const wallet = await Wallet.findOne({ userId });
  if (!wallet) {
    throw new BadRequestError("Wallet not found");
  }

  // Check available balance (excluding pending withdrawals)
  const availableBalance = wallet.balance - (wallet.pendingBalance || 0);
  if (availableBalance < amount) {
    throw new BadRequestError(
      `Insufficient balance. Available balance: $${availableBalance.toFixed(2)}`
    );
  }

  // Prepare withdrawal details based on payment method
  let withdrawalDetails = {};
  if (withdrawalAccount.paymentMethod === "bank") {
    withdrawalDetails = {
      bankDetails: withdrawalAccount.bankDetails,
      paymentMethod: "bank_transfer",
    };
  } else if (withdrawalAccount.paymentMethod === "crypto") {
    withdrawalDetails = {
      cryptoDetails: withdrawalAccount.cryptoDetails,
      paymentMethod: "crypto",
    };
  }

  // Create pending withdrawal transaction
  const transaction = await Transaction.create({
    userId,
    transactionType: "debit",
    amount,
    purpose: "withdrawal",
    status: "pending",
    isPending: true,
    balance: wallet.balance,
    pendingBalance: wallet.balance - amount,
    ...withdrawalDetails,
    metadata: {
      requestDate: new Date(),
      processingFee: 0, // Can be calculated based on your business logic
      estimatedProcessingTime: "2-3 business days",
      withdrawalAccountId: withdrawalAccount._id,
    },
  });

  // Update wallet with pending balance
  wallet.pendingBalance = (wallet.pendingBalance || 0) + amount;
  await wallet.save();

  // Send email notification about withdrawal request
  try {
    const user = await User.findById(userId);
    await sendWithdrawalRequestEmail(
      user,
      amount,
      transaction._id,
      withdrawalAccount
    );
  } catch (error) {
    console.error("Error sending withdrawal request email:", error);
    // Don't throw error here as the withdrawal request is still valid
  }

  res.status(201).json({
    message: "Withdrawal request submitted successfully",
    transaction,
    estimatedProcessingTime: "2-3 business days",
    availableBalance: wallet.balance - wallet.pendingBalance,
    withdrawalMethod: withdrawalAccount.paymentMethod,
  });
};

// Process deposit
const processDeposit = async (req, res) => {
  const { amount, paymentMethod, transactionReference } = req.body;
  const userId = req.user._id;

  const transaction = await Transaction.create({
    userId,
    transactionType: "credit",
    amount,
    paymentMethod,
    purpose: "deposit",
    status: "success",
    isPending: false,
    reference: transactionReference,
  });

  // Update wallet balance
  await Wallet.findOneAndUpdate(
    { userId },
    { $inc: { balance: amount } },
    { new: true }
  );

  res.status(201).json({ transaction });
};

// Get user's transaction history
const getTransactionHistory = async (req, res) => {
  const { page = 1, limit = 10, purpose, status } = req.query;
  const userId = req.user._id;

  const query = { userId };
  if (purpose) query.purpose = purpose;
  if (status) query.status = status;

  const skip = (page - 1) * limit;

  const [transactions, totalCount] = await Promise.all([
    Transaction.find(query)
      .populate("recipientId", "username avatar")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit)),
    Transaction.countDocuments(query),
  ]);

  const totalPages = Math.ceil(totalCount / limit);

  res.status(200).json({
    transactions,
    currentPage: parseInt(page),
    totalPages,
    totalCount,
  });
};

// Get pending withdrawals for a user
const getPendingWithdrawals = async (req, res) => {
  const userId = req.user._id;

  const pendingWithdrawals = await Transaction.find({
    userId,
    purpose: "withdrawal",
    status: "pending",
    isPending: true,
  }).sort({ createdAt: -1 });

  res.status(200).json({
    pendingWithdrawals,
  });
};

// Cancel pending withdrawal request
const cancelWithdrawal = async (req, res) => {
  const { transactionId } = req.params;
  const userId = req.user._id;

  const transaction = await Transaction.findOne({
    _id: transactionId,
    userId,
    purpose: "withdrawal",
    status: "pending",
    isPending: true,
  });

  if (!transaction) {
    throw new BadRequestError(
      "Pending withdrawal not found or already processed"
    );
  }

  // Update transaction status
  transaction.status = "cancelled";
  transaction.isPending = false;
  await transaction.save();

  // Update wallet by removing pending amount
  const wallet = await Wallet.findOne({ userId });
  wallet.pendingBalance -= transaction.amount;
  await wallet.save();

  res.status(200).json({
    message: "Withdrawal request cancelled successfully",
    transaction,
  });
};

// Get user's withdrawal accounts
const getWithdrawalAccounts = async (req, res) => {
  const userId = req.user._id;

  const withdrawalAccounts = await WithdrawalAccount.find({
    user: userId,
    status: { $in: ["active", "pending_verification"] },
  }).sort({ isDefault: -1, createdAt: -1 });

  res.status(200).json({ withdrawalAccounts });
};

// Get creator's earnings statistics
const getCreatorEarnings = async (req, res) => {
  const userId = req.user._id;
  const { period = "all" } = req.query;

  let dateFilter = {};
  const now = new Date();

  // Set date filter based on period
  switch (period) {
    case "today":
      dateFilter = {
        createdAt: {
          $gte: new Date(now.setHours(0, 0, 0, 0)),
          $lte: new Date(now.setHours(23, 59, 59, 999)),
        },
      };
      break;
    case "week":
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - 7);
      dateFilter = { createdAt: { $gte: weekStart } };
      break;
    case "month":
      const monthStart = new Date(now);
      monthStart.setMonth(now.getMonth() - 1);
      dateFilter = { createdAt: { $gte: monthStart } };
      break;
    default:
      dateFilter = {};
  }

  // Get total earnings (all successful credit transactions)
  const totalEarnings = await Transaction.aggregate([
    {
      $match: {
        userId,
        transactionType: "credit",
        status: "success",
        ...dateFilter,
      },
    },
    {
      $group: {
        _id: null,
        total: { $sum: "$amount" },
      },
    },
  ]);

  // Get earnings breakdown by purpose
  const earningsByPurpose = await Transaction.aggregate([
    {
      $match: {
        userId,
        transactionType: "credit",
        status: "success",
        ...dateFilter,
      },
    },
    {
      $group: {
        _id: "$purpose",
        total: { $sum: "$amount" },
        count: { $sum: 1 },
      },
    },
  ]);

  // Get pending withdrawals total
  const pendingWithdrawals = await Transaction.aggregate([
    {
      $match: {
        userId,
        purpose: "withdrawal",
        status: "pending",
        isPending: true,
      },
    },
    {
      $group: {
        _id: null,
        total: { $sum: "$amount" },
      },
    },
  ]);

  // Get recent successful transactions
  const recentTransactions = await Transaction.find({
    userId,
    transactionType: "credit",
    status: "success",
    ...dateFilter,
  })
    .sort({ createdAt: -1 })
    .limit(5)
    .populate("recipientId", "username avatar");

  res.status(200).json({
    totalEarnings: totalEarnings[0]?.total || 0,
    earningsByPurpose,
    pendingWithdrawalsTotal: pendingWithdrawals[0]?.total || 0,
    recentTransactions,
  });
};

module.exports = {
  createTip,
  requestWithdrawal,
  processDeposit,
  getTransactionHistory,
  getPendingWithdrawals,
  cancelWithdrawal,
  getWithdrawalAccounts,
  getCreatorEarnings,
};
