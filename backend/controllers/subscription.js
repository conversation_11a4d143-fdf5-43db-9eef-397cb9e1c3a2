const { BadRequestError } = require("../errors");
const Subscription = require("../models/subscription");
const User = require("../models/user");
const Wallet = require("../models/wallet");
const { sendSubscriptionConfirmation } = require("../utils/sendEmails");
const Transaction = require("../models/transaction");
const { createSubscribeNotification } = require("../utils/notification");
const Automation = require("../models/automation");
const { Message, Conversation } = require("../models/chat");
const welcomeMessageQueue = require("../queues/welcomeMessageProcessor");

const subscribeToCreator = async (req, res) => {
  console.log("req.body", req.body);

  const { creatorId, planId, paymentMethod, transactionReference, reference } = req.body;
  const subscriber = req.user?._id;

  // Use either transactionReference or reference field
  const paymentRef = transactionReference || reference;

  // Prevent self-subscription
  if (creatorId.toString() === subscriber.toString()) {
    throw new BadRequestError("You cannot subscribe to yourself");
  }

  // Validate transaction reference for external payments
  if (
    (paymentMethod === "paystack" || paymentMethod === "flutterwave") &&
    (!paymentRef ||
      !paymentRef.paymentStatus ||
      !(paymentRef.paymentReference || paymentRef.reference) ||
      !paymentRef.transactionId)
  ) {
    console.log("Invalid transaction reference:", paymentRef);
    throw new BadRequestError(
      "Invalid transaction reference. Required fields missing."
    );
  }

  // Check if user is already subscribed
  const existingSubscription = await Subscription.findOne({
    subscriber,
    creator: creatorId,
    endDate: { $gt: new Date() }, // Check if subscription is still active
  });

  if (existingSubscription) {
    throw new BadRequestError("You are already subscribed to this creator");
  }

  // Get creator and their pricing plan
  const creator = await User.findById(creatorId);
  const selectedPlan = creator.pricingPlans.id(planId);

  if (!selectedPlan || selectedPlan.status !== "active") {
    throw new BadRequestError("Invalid subscription plan");
  }

  // Calculate dates
  const startDate = new Date();
  const endDate = new Date();
  endDate.setMonth(endDate.getMonth() + selectedPlan.noOfMonths);

  // Handle payment based on method
  let paymentSuccess = false;
  let subscription;
  let initialTransaction;

  if (paymentMethod === "wallet") {
    const wallet = await Wallet.findOne({ userId: subscriber });
    const creatorWallet = await Wallet.findOne({ userId: creatorId });

    if (wallet.balance >= selectedPlan.amount) {
      // Create initial transaction
      initialTransaction = await Transaction.create({
        userId: subscriber,
        recipientId: creatorId,
        transactionType: "debit",
        amount: selectedPlan.amount,
        status: "success",
        paymentMethod,
        purpose: "subscription",
        balance: wallet.balance - selectedPlan.amount,
        subscriptionType: "initial",
      });

      // Create subscription with transaction reference
      subscription = await Subscription.create({
        subscriber,
        creator: creatorId,
        pricingPlanId: planId,
        startDate,
        endDate,
        paymentMethod,
        currentTransaction: initialTransaction._id,
      });

      // Update transaction with subscription ID
      initialTransaction.subscriptionId = subscription._id;
      await initialTransaction.save();

      // Deduct from subscriber's wallet
      wallet.balance -= selectedPlan.amount;
      await wallet.save();

      // Credit creator's wallet and create credit transaction
      creatorWallet.balance += selectedPlan.amount;
      await creatorWallet.save();

      await Transaction.create({
        userId: creatorId,
        recipientId: subscriber,
        transactionType: "credit",
        amount: selectedPlan.amount,
        status: "success",
        paymentMethod,
        purpose: "subscription",
        balance: creatorWallet.balance,
        subscriptionId: subscription._id,
        subscriptionType: "initial",
      });

      paymentSuccess = true;
    } else {
      throw new BadRequestError("Insufficient balance");
    }
  } else if (paymentMethod === "paystack" || paymentMethod === "flutterwave") {
    const creatorWallet = await Wallet.findOne({ userId: creatorId });

    // Use either transactionReference or reference field
    const paymentRef = transactionReference || reference;

    // Get the reference value from either paymentReference or reference field
    const refValue = paymentRef.paymentReference || paymentRef.reference;

    console.log("Creating transaction with reference:", {
      paymentStatus: paymentRef.paymentStatus,
      paymentReference: refValue,
      transactionId: paymentRef.transactionId,
    });

    // Create initial transaction with properly structured reference object
    try {
      // Create a reference object with all the necessary fields
      const referenceObject = {
        paymentStatus: paymentRef.paymentStatus,
        paymentReference: refValue,
        transactionId: paymentRef.transactionId,
        paymentGatewayResponse: paymentRef.paymentGatewayResponse || null,
        metadata: paymentRef.metadata || {},
      };

      console.log("Final reference object:", JSON.stringify(referenceObject));

      initialTransaction = await Transaction.create({
        userId: subscriber,
        recipientId: creatorId,
        transactionType: "debit",
        amount: selectedPlan.amount,
        status: "success",
        paymentMethod,
        purpose: "subscription",
        reference: referenceObject,
        subscriptionType: "initial",
      });
    } catch (error) {
      console.error("Transaction creation error:", error);
      throw new BadRequestError(error.message);
    }

    // Create subscription with transaction reference
    subscription = await Subscription.create({
      subscriber,
      creator: creatorId,
      pricingPlanId: planId,
      startDate,
      endDate,
      paymentMethod,
      currentTransaction: initialTransaction._id,
    });

    // Update transaction with subscription ID
    initialTransaction.subscriptionId = subscription._id;
    await initialTransaction.save();

    // Credit creator's wallet and create credit transaction
    creatorWallet.balance += selectedPlan.amount;
    await creatorWallet.save();

    await Transaction.create({
      userId: creatorId,
      recipientId: subscriber,
      transactionType: "credit",
      amount: selectedPlan.amount,
      status: "success",
      paymentMethod,
      purpose: "subscription",
      balance: creatorWallet.balance,
      subscriptionId: subscription._id,
      subscriptionType: "initial",
    });

    paymentSuccess = true;
  }

  if (!paymentSuccess) {
    // If subscription was created but payment failed, delete it and related transactions
    if (subscription) {
      await Promise.all([
        Subscription.findByIdAndDelete(subscription._id),
        Transaction.deleteMany({ subscriptionId: subscription._id }),
      ]);
    }
    throw new BadRequestError("Payment failed");
  }

  // Update user's subscriptions
  await User.findByIdAndUpdate(subscriber, {
    $push: { subscriptions: subscription._id },
    $inc: { subscriptionCount: 1 },
  });

  // Update creator's subscriber count
  await User.findByIdAndUpdate(creatorId, {
    $inc: { subscriberCount: 1 },
  });

  // Create subscription notification
  await createSubscribeNotification(
    subscriber,
    creatorId,
    `${req.user.username} subscribed to your content`
  );

  // Handle welcome message automation
  const welcomeAutomation = await Automation.findOne({
    creatorId,
    triggerEvent: "WELCOME_MESSAGE",
    isActive: true,
  });

  if (welcomeAutomation) {
    // Process the message template
    const processedMessage = welcomeAutomation.message.replace(
      "{subscriberName}",
      req.user.username
    );

    if (welcomeAutomation.timing.type === "immediately") {
      // Send message immediately
      await sendWelcomeMessage(req, {
        creatorId,
        subscriber,
        processedMessage,
      });
    } else if (welcomeAutomation.timing.type === "delay") {
      // Add delayed welcome message to queue with metadata
      const job = await welcomeMessageQueue.add(
        {
          creatorId,
          subscriber,
          processedMessage,
          metadata: {
            subscriberName: req.user.username,
            creatorName: creator.username,
            subscriptionId: subscription._id,
            delayDuration: welcomeAutomation.timing.delayDuration,
          },
        },
        {
          delay: welcomeAutomation.timing.delayDuration * 60 * 1000, // Convert minutes to milliseconds
          jobId: `welcome-${subscription._id}-${Date.now()}`, // Unique job ID
          removeOnComplete: true, // Clean up completed jobs
          attempts: 3, // Retry up to 3 times
          backoff: {
            type: "exponential",
            delay: 5000, // Start with 5 seconds delay between retries
          },
        }
      );

      // Log the scheduled job
      console.log(
        `Scheduled welcome message job ${job.id} for subscriber ${subscriber} with ${welcomeAutomation.timing.delayDuration} minutes delay`
      );
    }
  }

  // Send confirmation email with the new template
  await sendSubscriptionConfirmation({
    email: req.user.email,
    subscriberName: req.user.username,
    creatorName: creator.username,
    planDuration: selectedPlan.noOfMonths,
    amount: selectedPlan.amount,
    startDate,
    endDate,
    creatorProfileUrl: `${process.env.APP_URL}/creator/${creator.username}`,
  });

  // Populate the subscription with its transactions before sending response
  const populatedSubscription = await Subscription.findById(subscription._id)
    .populate("transactions")
    .populate("currentTransaction");

  res.status(201).json({ subscription: populatedSubscription });
};

const getSubscribers = async (req, res) => {
  const creatorId = req.user._id;
  const { status = "active", page = 1, limit = 9 } = req.query;
  const skip = (page - 1) * limit;

  let dateFilter = {};
  if (status === "active") {
    dateFilter = { endDate: { $gt: new Date() } };
  } else if (status === "expired") {
    dateFilter = { endDate: { $lte: new Date() } };
  }

  const query = {
    creator: creatorId,
    ...dateFilter,
  };

  const [subscriptions, totalCount] = await Promise.all([
    Subscription.find(query)
      .populate("subscriber", "username email avatar bio")
      .populate("currentTransaction")
      .populate("transactions")
      .sort({ startDate: -1 })
      .skip(skip)
      .limit(parseInt(limit)),
    Subscription.countDocuments(query),
  ]);

  const totalPages = Math.ceil(totalCount / limit);

  res.status(200).json({
    subscriptions,
    currentPage: parseInt(page),
    totalPages,
    totalCount,
  });
};

const getMySubscriptions = async (req, res) => {
  const subscriber = req.user._id;
  const { status = "active", page = 1, limit = 10 } = req.query;
  const skip = (page - 1) * limit;

  const query = {
    subscriber,
    status,
  };

  const [subscriptions, totalCount] = await Promise.all([
    Subscription.find(query)
      .populate("creator", "username email avatar displayName")
      .populate("currentTransaction")
      .populate("transactions")
      .sort({ startDate: -1 })
      .skip(skip)
      .limit(parseInt(limit)),
    Subscription.countDocuments(query),
  ]);

  const totalPages = Math.ceil(totalCount / limit);

  res.status(200).json({
    subscriptions,
    currentPage: parseInt(page),
    totalPages,
    totalCount,
  });
};

const checkExistingSubscription = async (req, res) => {
  try {
    const { creatorId } = req.params;
    const subscriber = req.user?._id;

    // Validate inputs
    if (!creatorId) {
      throw new BadRequestError("Creator ID is required");
    }

    if (!subscriber) {
      throw new BadRequestError("User must be authenticated");
    }

    // Prevent checking self-subscription
    if (creatorId.toString() === subscriber.toString()) {
      return res.status(200).json({
        subscription: null,
        canRetry: false,
        message: "You cannot subscribe to yourself",
      });
    }

    // Check if creator exists
    const creator = await User.findById(creatorId);
    if (!creator) {
      throw new BadRequestError("Creator not found");
    }

    // Get subscription status
    const subscriptionStatus = await Subscription.checkExistingSubscription(
      subscriber,
      creatorId
    );

    // If no subscription exists
    if (!subscriptionStatus) {
      return res.status(200).json({
        subscription: null,
        canRetry: false,
        message: null,
      });
    }

    // Return subscription status with additional details
    return res.status(200).json({
      ...subscriptionStatus,
      creator: {
        username: creator.username,
        displayName: creator.displayName,
        avatar: creator.avatar,
      },
    });
  } catch (error) {
    // If it's our custom error, use its status code
    if (error instanceof BadRequestError) {
      return res.status(400).json({ message: error.message });
    }
    // For unexpected errors
    console.error("Error checking subscription:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

const sendWelcomeMessage = async (
  req,
  { creatorId, subscriber, processedMessage }
) => {
  // Find or create conversation
  let conversation = await Conversation.findOne({
    participants: { $all: [subscriber, creatorId] },
  });

  if (!conversation) {
    conversation = await Conversation.create({
      participants: [subscriber, creatorId],
      messages: [],
    });
  }

  // Create message data
  const messageData = {
    sender: creatorId,
    receiver: subscriber,
    content: processedMessage,
    conversation: conversation._id,
    readBy: [creatorId],
    messageType: "text",
  };

  const sendWelcomeMessage = async () => {
    try {
      const message = await Message.create(messageData);

      // Update conversation with new message
      conversation.lastMessage = message._id;
      conversation.messages.push(message._id);
      await conversation.save();

      // Emit socket event if available
      const io = req.app.get("io");
      if (io) {
        io.to(subscriber.toString()).emit("new_message", {
          message: await message.populate([
            {
              path: "sender",
              select: "username email avatar displayName",
            },
            {
              path: "conversation",
              select: "_id",
            },
            {
              path: "readBy",
              select: "username _id",
            },
          ]),
          conversation: conversation._id,
        });
      }
    } catch (error) {
      console.error("Error sending welcome message:", error);
    }
  };

  await sendWelcomeMessage();
};

module.exports = {
  subscribeToCreator,
  getSubscribers,
  getMySubscriptions,
  checkExistingSubscription,
};
