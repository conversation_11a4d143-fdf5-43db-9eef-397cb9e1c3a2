const { Paystack } = require("../utils/paystack");

const createCard = async (req, res) => {
  const { cardNumber, expiryDate, cvv, nameOnCard } = req.body;

  const data = {
    email: req.user.email,
    amount: "50",
    metadata: {
      custom_fields: [
        {
          value: "makurdi",
          display_name: "Donation for",
          variable_name: "donation_for",
        },
      ],
    },
    bank: {
      code: "057",
      account_number: "**********",
    },
    birthday: "1995-12-23",
  };

  const response = await Paystack.post("/charge", data);
  console.log(response.data);
};

module.exports = { createCard };
