const User = require("../models/user");
const PageView = require("../models/pageView");
const { StatusCodes } = require("http-status-codes");
const mongoose = require("mongoose");

// exports.getUserProfile = async (req, res) => {
//   const { username } = req.params;
//   const user = await User.findOne({ username }).select("-password");

//   res.status(StatusCodes.OK).json({ user });
// };
exports.getUserProfile = async (req, res) => {
  try {
    const { username } = req.params;

    const user = await User.findOne({ username });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Base user profile without sensitive information
    const userProfile = {
      ...user.toObject(),
      password: undefined,
    };

    // Add subscription status if user is authenticated
    if (req.user && req.user._id) {
      const currentUser = await User.findById(req.user._id);
      const isSubscribed = await currentUser.isSubscribedTo(user._id);
      userProfile.isSubscribed = isSubscribed;
    }

    res.status(200).json({ user: userProfile });
  } catch (error) {
    res.status(500).json({
      message: "Error fetching user profile",
      error: error.message,
    });
  }
};

exports.getUsers = async (req, res) => {
  const users = await User.find().select("-password");
  res.status(StatusCodes.OK).json({ users });
};

exports.updateUser = async (req, res) => {
  const { id } = req.params;
  const { name, bio, avatar } = req.body;
  const user = await User.findByIdAndUpdate(id, { name, bio, avatar });
  res.status(StatusCodes.OK).json({ user });
};

exports.deleteUser = async (req, res) => {
  const { id } = req.params;
  await User.findByIdAndDelete(id);
  res.status(StatusCodes.NO_CONTENT).send();
};

exports.getUserPosts = async (req, res) => {
  const { username } = req.params;
  const posts = await Post.find({ creator: username });
  res.status(StatusCodes.OK).json({ posts });
};

exports.getUserFollowers = async (req, res) => {
  const { username } = req.params;
  const followers = await User.find({ following: username }).select(
    "-password"
  );
  res.status(StatusCodes.OK).json({ followers });
};

exports.getUserFollowing = async (req, res) => {
  const { username } = req.params;
  const following = await User.find({ followers: username }).select(
    "-password"
  );
  res.status(StatusCodes.OK).json({ following });
};

exports.getRecommendedUsers = async (req, res) => {
  const recommendedUsers = await User.aggregate([
    { $match: { _id: { $ne: req.user._id } } },
    { $project: { password: 0 } },
    { $sample: { size: 3 } },
  ]);
  res.status(StatusCodes.OK).json({ recommendedUsers });
};

exports.searchUsers = async (req, res) => {
  const { q } = req.query;

  const users = await User.find({
    username: { $regex: q.trim(), $options: "i" },
  })
    .limit(5)
    .select("username avatar");
  console.log("users", users);

  // If no users found, return empty array instead of null
  if (!users || users.length === 0) {
    return res.status(StatusCodes.OK).json({ users: [] });
  }

  res.status(StatusCodes.OK).json({ users });
};

exports.getSuggestedCreators = async (req, res) => {
  try {
    // Create base match condition
    const matchCondition = {
      isCreator: true, // Add condition to only match creators
    };

    // If user is logged in, exclude them from results
    if (req.user && req.user._id) {
      matchCondition._id = { $ne: req.user._id };
    }

    const suggestedCreators = await User.aggregate([
      // Match creators and conditionally exclude current user
      {
        $match: matchCondition,
      },
      // Lookup posts to count total posts, photos, and videos
      {
        $lookup: {
          from: "posts",
          localField: "_id",
          foreignField: "creator",
          as: "posts",
        },
      },
      // Lookup subscribers
      {
        $lookup: {
          from: "subscriptions",
          localField: "_id",
          foreignField: "creator",
          as: "subscribers",
        },
      },
      // Project necessary fields and calculate counts
      {
        $project: {
          username: 1,
          name: 1,
          avatar: 1,
          bio: 1,
          followers: 1,
          createdAt: 1,
          coverImage: 1,
          displayName: 1,
          about: 1,
          isCreator: 1, // Include isCreator field in projection
          totalPosts: { $size: "$posts" },
          totalPhotos: {
            $reduce: {
              input: "$posts",
              initialValue: 0,
              in: {
                $add: [
                  "$$value",
                  {
                    $size: {
                      $filter: {
                        input: { $ifNull: ["$$this.mediaUrls", []] },
                        as: "media",
                        cond: { $eq: ["$$media.type", "image"] },
                      },
                    },
                  },
                ],
              },
            },
          },
          totalVideos: {
            $reduce: {
              input: "$posts",
              initialValue: 0,
              in: {
                $add: [
                  "$$value",
                  {
                    $size: {
                      $filter: {
                        input: { $ifNull: ["$$this.mediaUrls", []] },
                        as: "media",
                        cond: { $eq: ["$$media.type", "video"] },
                      },
                    },
                  },
                ],
              },
            },
          },
          totalSubscribers: { $size: "$subscribers" },
        },
      },
      // Sort by total subscribers to show most popular creators first
      { $sort: { totalSubscribers: -1 } },
      // Get random 3 creators from top creators
      { $sample: { size: 3 } },
    ]);

    if (!suggestedCreators || suggestedCreators.length === 0) {
      return res.status(StatusCodes.OK).json({ creators: [] });
    }

    res.status(StatusCodes.OK).json({ creators: suggestedCreators });
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: "Error fetching suggested creators",
      error: error.message,
    });
  }
};

exports.checkUsernameAvailability = async (req, res) => {
  const { username } = req.query;
  console.log(username);
  // If no username provided
  if (!username) {
    return res.status(400).json({ message: "Username is required" });
  }

  try {
    // Check if username exists, excluding the current user
    const existingUser = await User.findOne({
      username,
      _id: { $ne: req.user._id }, // Exclude current user
    });
    console.log(existingUser);

    res.status(200).json({
      available: !existingUser,
      message: existingUser
        ? "Username is already taken"
        : "Username is available",
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: "Error checking username availability" });
  }
};

exports.checkUsernameAvailabilityPublic = async (req, res) => {
  const { username } = req.query;
  console.log(username);
  // If no username provided
  if (!username) {
    return res.status(400).json({ message: "Name is required" });
  }

  try {
    // Check if username exists
    const existingUser = await User.findOne({
      username,
    });
    console.log(existingUser);

    res.status(200).json({
      available: !existingUser,
      message: existingUser ? "Name is already taken" : "Name is available",
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: "Error checking name availability" });
  }
};

exports.getCreatorStats = async (req, res) => {
  try {
    const { username } = req.params;
    const creator = await User.findOne({ username });

    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Get current date and start of current week
    const now = new Date();
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    startOfWeek.setHours(0, 0, 0, 0);

    // Get active subscribers (not expired subscriptions)
    const activeSubscriptions = await mongoose
      .model("Subscription")
      .countDocuments({
        creator: creator._id,
        status: "active",
        endDate: { $gte: new Date() },
      });

    // Get new subscribers this week
    const newSubscribersThisWeek = await mongoose
      .model("Subscription")
      .countDocuments({
        creator: creator._id,
        status: "active",
        startDate: { $gte: startOfWeek },
      });

    // Get expired subscribers this week
    const expiredSubscribersThisWeek = await mongoose
      .model("Subscription")
      .countDocuments({
        creator: creator._id,
        status: "expired",
        endDate: { $gte: startOfWeek, $lte: new Date() },
      });

    // Get total earnings (all successful transactions)
    const totalEarnings = await mongoose.model("Transaction").aggregate([
      {
        $match: {
          userId: creator._id,
          type: "credit",
          status: "success",
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$amount" },
        },
      },
    ]);

    // Get pending payout (successful transactions not yet withdrawn)
    const pendingPayout = await mongoose.model("Transaction").aggregate([
      {
        $match: {
          userId: creator._id,
          type: "credit",
          status: "success",
          isPending: true,
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$amount" },
        },
      },
    ]);

    // Get total posts count
    const totalPosts = await mongoose.model("Post").countDocuments({
      creator: creator._id,
    });

    // Get sales stats
    const salesStats = {
      joins: await mongoose.model("Subscription").countDocuments({
        creator: creator._id,
        status: "active",
      }),
      renews: await mongoose.model("Subscription").countDocuments({
        creator: creator._id,
        status: "active",
        autoRenew: true,
      }),
      chatTips: await mongoose.model("Transaction").countDocuments({
        userId: creator._id,
        type: "credit",
        status: "success",
        purpose: "chat_tip",
      }),
      tips: await mongoose.model("Transaction").countDocuments({
        userId: creator._id,
        type: "credit",
        status: "success",
        purpose: "tip",
      }),
    };

    // Get subscribers currently online (this would need a separate online tracking system)
    // For now, we'll return 0 or implement later
    const subscribersOnline = 0;

    // Get page opens statistics
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);

    // Get total page opens (unique views in last 24 hours)
    const pageOpens = await PageView.countDocuments({
      profileId: creator._id,
      createdAt: { $gte: startOfDay },
    });

    // Get authenticated vs unauthenticated views ratio
    const viewStats = await PageView.aggregate([
      {
        $match: {
          profileId: creator._id,
          createdAt: { $gte: startOfDay },
        },
      },
      {
        $group: {
          _id: "$isAuthenticated",
          count: { $sum: 1 },
        },
      },
    ]);

    // Format view stats
    const viewsBreakdown = {
      authenticated: 0,
      unauthenticated: 0,
    };
    viewStats.forEach((stat) => {
      if (stat._id === true) {
        viewsBreakdown.authenticated = stat.count;
      } else {
        viewsBreakdown.unauthenticated = stat.count;
      }
    });

    const stats = {
      directLink: `https://errorsphere.com/${creator.username}`,
      subscribersOnline,
      revenue: {
        allTime: totalEarnings[0]?.total || 0,
        nextPayout: pendingPayout[0]?.total || 0,
      },
      subscribers: {
        active: activeSubscriptions,
        followers: creator.followersCount,
        thisWeek: {
          new: newSubscribersThisWeek,
          expired: expiredSubscribersThisWeek,
        },
      },
      pageOpens: {
        total: pageOpens,
        ...viewsBreakdown,
        trend: {
          today: pageOpens,
          yesterday: await PageView.countDocuments({
            profileId: creator._id,
            createdAt: {
              $gte: new Date(startOfDay.getTime() - 24 * 60 * 60 * 1000),
              $lt: startOfDay,
            },
          }),
        },
      },
      salesStats,
      totalPosts,
    };

    res.status(200).json({ stats });
  } catch (error) {
    console.error("Error in getCreatorStats:", error);
    res
      .status(500)
      .json({ message: "Error fetching creator stats", error: error.message });
  }
};

exports.trackPageView = async (req, res) => {
  try {
    const { username } = req.params;
    const { sessionId } = req.body; // For unauthorized users

    // Get the profile owner's ID
    const profileOwner = await User.findOne({ username });
    if (!profileOwner) {
      return res.status(404).json({ message: "User not found" });
    }

    // Prepare view data
    const viewData = {
      profileId: profileOwner._id,
      ipAddress: req.ip,
      userAgent: req.get("user-agent"),
      isAuthenticated: !!req.user,
    };

    // Handle authenticated vs unauthenticated views
    if (req.user) {
      // Don't track if user is viewing their own profile
      if (profileOwner._id.toString() === req.user._id.toString()) {
        return res.status(200).json({ message: "Own profile view" });
      }
      viewData.viewerId = req.user._id;
    } else {
      // For unauthorized users, use sessionId
      if (!sessionId) {
        return res
          .status(400)
          .json({ message: "Session ID is required for unauthorized views" });
      }
      viewData.sessionId = sessionId;
    }

    // Create new page view
    await PageView.create(viewData);

    // Get total views for this profile
    const totalViews = await PageView.countDocuments({
      profileId: profileOwner._id,
      createdAt: { $gt: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
    });

    res.status(200).json({
      message: "Page view tracked",
      totalViews,
    });
  } catch (error) {
    // If duplicate key error, it means the user/session already viewed within timeframe
    if (error.code === 11000) {
      return res.status(200).json({ message: "View already tracked" });
    }

    res.status(500).json({
      message: "Error tracking page view",
      error: error.message,
    });
  }
};

exports.getCreatorSubscribers = async (req, res) => {
  try {
    const { username } = req.params;
    const {
      page = 1,
      limit = 10,
      status,
      sort = "-createdAt",
      search,
    } = req.query;

    // Find creator
    const creator = await User.findOne({ username });
    if (!creator) {
      return res.status(404).json({ message: "Creator not found" });
    }

    // Build query
    const query = {
      creator: creator._id,
    };

    // Add status filter if provided
    if (status) {
      query.status = status;
    }

    // Get subscribers with pagination and search
    let subscribersQuery = mongoose
      .model("Subscription")
      .find(query)
      .sort(sort)
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit));

    // If search parameter is provided, use aggregation to filter by subscriber username
    let subscribers;
    if (search) {
      subscribers = await mongoose.model("Subscription").aggregate([
        { $match: query },
        {
          $lookup: {
            from: "users",
            localField: "subscriber",
            foreignField: "_id",
            as: "subscriberInfo",
          },
        },
        { $unwind: "$subscriberInfo" },
        {
          $match: {
            "subscriberInfo.username": { $regex: search, $options: "i" },
          },
        },
        {
          $sort: { [sort.replace("-", "")]: sort.startsWith("-") ? -1 : 1 },
        },
        { $skip: (parseInt(page) - 1) * parseInt(limit) },
        { $limit: parseInt(limit) },
        {
          $project: {
            _id: 1,
            subscriber: "$subscriberInfo",
            creator: 1,
            plan: 1,
            startDate: 1,
            endDate: 1,
            autoRenew: 1,
            status: 1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
      ]);
    } else {
      subscribers = await subscribersQuery
        .populate("subscriber", "username avatar name bio")
        .lean();
    }

    // Get total count for pagination (considering search filter if present)
    let totalQuery = query;
    if (search) {
      totalQuery = {
        ...query,
        $expr: {
          $regexMatch: {
            input: {
              $getField: {
                field: "username",
                input: { $getField: { field: "subscriber", input: "$$ROOT" } },
              },
            },
            regex: search,
            options: "i",
          },
        },
      };
    }
    const total = await mongoose
      .model("Subscription")
      .countDocuments(totalQuery);

    // Calculate pagination info
    const totalPages = Math.ceil(total / parseInt(limit));
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.status(200).json({
      subscribers,
      pagination: {
        total,
        page: parseInt(page),
        totalPages,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit),
      },
    });
  } catch (error) {
    console.error("Error in getCreatorSubscribers:", error);
    res.status(500).json({
      message: "Error fetching creator subscribers",
      error: error.message,
    });
  }
};
