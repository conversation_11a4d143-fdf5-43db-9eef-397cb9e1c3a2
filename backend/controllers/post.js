const Post = require("../models/post");
const User = require("../models/user");
const { StatusCodes } = require("http-status-codes");
const {
  NotFoundError,
  BadRequestError,
  UnauthenticatedError,
} = require("../errors");
const Notification = require("../models/notification");
const {
  createMentionNotification,
  createCommentNotification,
  createLikeNotification,
  createNewPostNotification,
  createSubscribeNotification,
} = require("../utils/notification");
const { sendNewPostNotification } = require("../utils/sendEmails");
const mongoose = require("mongoose");

const createPost = async (req, res) => {
  try {
    const { content, mentions, mediaUrls, isPublic } = req.body;
    const userId = req.user._id;

    // Create the post
    const post = await Post.create({
      content,
      creator: userId,
      isPublic,
      mediaUrls,
      mentions: mentions?.filter(Boolean) || [], // Filter out null/undefined values
    });

    // Create notifications for mentioned users
    if (mentions && mentions.length > 0) {
      const notifications = mentions
        .filter(Boolean) // Filter out null values
        .map((mentionedUserId) => ({
          sender: userId,
          recipient: mentionedUserId,
          type: "MENTION",
          post: post._id,
          read: false,
          message: `${req.user.username} mentioned you in a post`,
        }));

      if (notifications.length > 0) {
        await Notification.insertMany(notifications);
      }
    }

    // Get creator details
    const creator = await User.findById(userId);

    // If the post creator is a creator, send notifications to all active subscribers
    if (creator.isCreator) {
      const Subscription = mongoose.model("Subscription");

      // Get all active subscribers
      const activeSubscriptions = await Subscription.find({
        creator: userId,
        status: "active",
        endDate: { $gte: new Date() },
      }).populate("subscriber");

      // Send email notifications to all subscribers
      const emailPromises = activeSubscriptions.map((subscription) =>
        sendNewPostNotification(subscription.subscriber, creator, post)
      );

      // Send emails in parallel
      await Promise.all(emailPromises).catch((error) => {
        console.error("Error sending subscriber notifications:", error);
      });
    }

    // Populate the post with author details
    const populatedPost = await Post.findById(post._id)
      .populate("creator", "username avatar")
      .populate("mentions", "username avatar");

    res.status(201).json({
      success: true,
      message: "Post created successfully",
      post: populatedPost,
    });
  } catch (error) {
    console.error("Create post error:", error);
    res.status(500).json({
      success: false,
      message: error.message || "Something went wrong",
    });
  }
};

const getAllPosts = async (req, res) => {
  try {
    const query = {};

    // If looking at a specific creator's posts
    if (req.query.creator) {
      const creator = await User.findById(req.query.creator);
      if (!creator) {
        return res.status(404).json({ message: "Creator not found" });
      }

      // Check if post is from a creator and requires subscription
      if (creator.role === "creator") {
        const currentUser = await User.findById(req.user._id);
        const isSubscribed = currentUser.isSubscribedTo(creator._id);

        if (!isSubscribed) {
          // Only show public posts or preview posts
          query.isPublic = true;
        }
      }

      query.creator = req.query.creator;
    }

    const posts = await Post.find(query)
      .sort({ createdAt: -1 })
      .populate("creator", "username displayName avatar")
      .populate("mentions", "username avatar");
    res.status(200).json({ posts });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
const getUserPosts = async (req, res) => {
  try {
    const creatorId = req.params.userId;
    let query = { creator: creatorId };

    // Check if creator exists
    const creator = await User.findById(creatorId);
    if (!creator) {
      return res.status(404).json({
        success: false,
        message: "Creator not found",
      });
    }

    // If user is not authenticated, only show public posts
    if (!req.user) {
      query.isPublic = true;
    } else {
      const currentUser = await User.findById(req.user._id);
      const Subscription = mongoose.model("Subscription");

      // Check if user is subscribed to the creator
      const activeSubscription = await Subscription.findOne({
        subscriber: currentUser._id,
        creator: creatorId,
        status: "active",
        endDate: { $gte: new Date() },
      });

      // If not subscribed and not the creator themselves, only show public posts
      if (!activeSubscription && currentUser._id.toString() !== creatorId) {
        query.isPublic = true;
      }
    }

    const posts = await Post.find(query)
      .sort({ createdAt: -1 })
      .populate("creator", "username displayName avatar")
      .populate("mentions", "username avatar");

    // Check subscription status
    let isSubscribed = false;
    if (req.user) {
      const Subscription = mongoose.model("Subscription");
      const activeSubscription = await Subscription.findOne({
        subscriber: req.user._id,
        creator: creatorId,
        status: "active",
        endDate: { $gte: new Date() },
      });
      isSubscribed = !!activeSubscription;
    }

    res.status(200).json({
      success: true,
      posts,
      isSubscribed,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};
const getPostsSubscribedTo = async (req, res) => {
  try {
    const currentUser = await User.findById(req.user._id);
    const Subscription = mongoose.model("Subscription");

    // Get active subscriptions for the current user
    const activeSubscriptions = await Subscription.find({
      subscriber: currentUser._id,
      status: "active",
      endDate: { $gte: new Date() },
    });

    // Get list of creators the user is subscribed to
    const subscribedCreators = activeSubscriptions.map((sub) =>
      sub.creator.toString()
    );

    let subscribedPosts = [];
    let suggestedPosts = [];

    // If user has subscriptions, get their subscribed posts first
    if (subscribedCreators.length > 0) {
      subscribedPosts = await Post.find({
        creator: { $in: subscribedCreators },
      })
        .sort({ createdAt: -1 })
        .populate("creator", "username displayName avatar")
        .populate("mentions", "username avatar")
        .limit(10); // Limit to 10 subscribed posts
    }

    // Get suggested public posts from creators user is not subscribed to
    suggestedPosts = await Post.find({
      creator: { $nin: subscribedCreators },
      isPublic: true,
    })
      .sort({ createdAt: -1 })
      .populate("creator", "username displayName avatar")
      .populate("mentions", "username avatar")
      .limit(10); // Limit to 10 suggested posts

    res.status(200).json({
      subscribedPosts,
      suggestedPosts,
      hasSubscriptions: subscribedCreators.length > 0,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getSinglePost = async (req, res) => {
  const { id: postId } = req.params;

  const post = await Post.findOne({ _id: postId })
    .populate("creator", "username avatar name")
    .populate("mentions", "username avatar")
    .populate({
      path: "comments",
      populate: {
        path: "user",
        select: "username avatar",
      },
    });

  if (!post) {
    throw new NotFoundError(`No post with id: ${postId}`);
  }

  // Check if premium content is accessible
  if (post.type === "premium") {
    const subscription = await Subscription.findOne({
      subscriber: req.user._id,
      creator: post.creator._id,
      status: "active",
    });

    if (!subscription && req.user._id !== post.creator._id.toString()) {
      throw new UnauthenticatedError("Subscribe to access premium content");
    }
  }

  res.status(StatusCodes.OK).json({ post });
};

const updatePost = async (req, res) => {
  const { id: postId } = req.params;
  const { title, content, type, mediaUrls, tags } = req.body;

  const post = await Post.findOne({ _id: postId });

  if (!post) {
    throw new NotFoundError(`No post with id: ${postId}`);
  }

  // Check ownership
  if (post.creator.toString() !== req.user._id) {
    throw new UnauthenticatedError("Not authorized to update this post");
  }

  post.title = title;
  post.content = content;
  post.type = type;
  post.mediaUrls = mediaUrls;
  post.tags = tags;

  await post.save();

  res.status(StatusCodes.OK).json({ post });
};

const deletePost = async (req, res) => {
  const { id: postId } = req.params;

  const post = await Post.findOne({ _id: postId });

  if (!post) {
    throw new NotFoundError(`No post with id: ${postId}`);
  }

  // Check ownership
  if (post.creator.toString() !== req.user._id.toString()) {
    throw new UnauthenticatedError("Not authorized to delete this post");
  }

  await Post.findOneAndDelete({ _id: postId });

  // Update creator's post count
  await User.findByIdAndUpdate(req.user._id, {
    $inc: { totalPosts: -1 },
  });

  res.status(StatusCodes.OK).json({ msg: "Post removed" });
};

const likePost = async (req, res) => {
  const { id: postId } = req.params;

  // Verify user is authenticated
  if (!req.user || !req.user._id) {
    throw new UnauthenticatedError("Authentication required");
  }

  const post = await Post.findOne({ _id: postId });

  if (!post) {
    throw new NotFoundError(`No post with id: ${postId}`);
  }

  // Filter out any null values and ensure likes is an array
  post.likes = Array.isArray(post.likes) ? post.likes.filter(Boolean) : [];

  const isLiked = post.likes.some(
    (like) => like && like.toString() === req.user._id.toString()
  );

  if (isLiked) {
    // Remove like and decrease count
    post.likes = post.likes.filter(
      (like) => like && like.toString() !== req.user._id.toString()
    );
    post.likesCount = Math.max(0, post.likes.length);
  } else {
    // Add like and increase count
    post.likes.push(req.user._id);
    post.likesCount = post.likes.length;
    if (req.user._id.toString() === post.creator.toString()) return; // Don't notify if user likes their own post

    createLikeNotification(
      postId,
      req.user._id,
      post.creator,
      `${req.user.username} liked your post`
    );
  }

  await post.save();

  res.status(StatusCodes.OK).json({ post });
};

const dislikePost = async (req, res) => {
  const { id: postId } = req.params;

  // Verify user is authenticated
  if (!req.user || !req.user._id) {
    throw new UnauthenticatedError("Authentication required");
  }

  const post = await Post.findOne({ _id: postId });

  if (!post) {
    throw new NotFoundError(`No post with id: ${postId}`);
  }

  // Filter out any null values and ensure likes is an array
  post.likes = Array.isArray(post.likes) ? post.likes.filter(Boolean) : [];

  const isLiked = post.likes.some(
    (like) => like && like.toString() === req.user._id.toString()
  );

  if (isLiked) {
    // Remove like and decrease count
    post.likes = post.likes.filter(
      (like) => like && like.toString() !== req.user._id.toString()
    );
    post.likesCount = post.likes.length;
    await post.save();
  }

  res.status(StatusCodes.OK).json({ post });
};

const getUserMedia = async (req, res) => {
  try {
    const userId = req.params.userId;
    let isSubscribed = false;

    // Check if creator exists
    const creator = await User.findById(userId);
    if (!creator) {
      return res.status(404).json({
        success: false,
        message: "Creator not found",
      });
    }

    // Get total active subscribers count
    const Subscription = mongoose.model("Subscription");
    const activeSubscribersAgg = await Subscription.aggregate([
      {
        $match: {
          creator: new mongoose.Types.ObjectId(userId),
          status: "active",
          endDate: { $gte: new Date() },
        },
      },
      {
        $group: {
          _id: "$subscriber",
          latestSubscription: { $last: "$$ROOT" },
        },
      },
      {
        $count: "totalUniqueSubscribers",
      },
    ]);

    const totalActiveSubscribers =
      activeSubscribersAgg[0]?.totalUniqueSubscribers || 0;

    // Get total counts regardless of subscription
    const totalPostCount = await Post.countDocuments({ creator: userId });
    const mediaCounts = await Post.aggregate([
      { $match: { creator: new mongoose.Types.ObjectId(userId) } },
      {
        $project: {
          mediaUrls: 1,
          totalImages: {
            $size: {
              $filter: {
                input: { $ifNull: ["$mediaUrls", []] },
                as: "media",
                cond: { $eq: ["$$media.type", "image"] },
              },
            },
          },
          totalVideos: {
            $size: {
              $filter: {
                input: { $ifNull: ["$mediaUrls", []] },
                as: "media",
                cond: { $eq: ["$$media.type", "video"] },
              },
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          totalImageCount: { $sum: "$totalImages" },
          totalVideoCount: { $sum: "$totalVideos" },
          totalMediaCount: { $sum: { $size: "$mediaUrls" } },
        },
      },
    ]);

    const counts = mediaCounts[0] || {
      totalImageCount: 0,
      totalVideoCount: 0,
      totalMediaCount: 0,
    };

    // Check subscription status - matching getUserPosts implementation
    if (req.user) {
      const activeSubscription = await Subscription.findOne({
        subscriber: req.user._id,
        creator: userId,
        status: "active",
        endDate: { $gte: new Date() },
      });

      // Set isSubscribed if user has active subscription or is the creator
      isSubscribed = !!activeSubscription || req.user._id.toString() === userId;
    }

    // If not subscribed or public access, return only counts
    if (!isSubscribed) {
      return res.status(StatusCodes.OK).json({
        success: true,
        isSubscribed,
        totalPostCount,
        totalMediaCount: counts.totalMediaCount,
        totalVideoCount: counts.totalVideoCount,
        totalImageCount: counts.totalImageCount,
        totalActiveSubscribers,
        message:
          creator.role === "creator"
            ? "Please subscribe to view creator's media"
            : null,
        media: [],
      });
    }

    // If subscribed, fetch media
    const posts = await Post.find({
      creator: userId,
      mediaUrls: { $exists: true, $ne: [] },
    })
      .select("mediaUrls createdAt")
      .sort({ createdAt: -1 });

    const media = posts.reduce((acc, post) => {
      return [
        ...acc,
        ...post.mediaUrls.map((url) => ({
          url,
          createdAt: post.createdAt,
          postId: post._id,
        })),
      ];
    }, []);

    res.status(StatusCodes.OK).json({
      success: true,
      isSubscribed,
      totalPostCount,
      totalMediaCount: counts.totalMediaCount,
      totalVideoCount: counts.totalVideoCount,
      totalImageCount: counts.totalImageCount,
      totalActiveSubscribers,
      count: media.length,
      media,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message || "Error fetching user media",
    });
  }
};

const getPostMedia = async (req, res) => {
  try {
    const { id: postId } = req.params;

    const post = await Post.findById(postId).select("mediaUrls creator");

    if (!post) {
      throw new NotFoundError(`No post with id: ${postId}`);
    }

    res.status(StatusCodes.OK).json({
      success: true,
      count: post.mediaUrls.length,
      media: post.mediaUrls.map((url) => ({
        url,
        postId: post._id,
      })),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message || "Error fetching post media",
    });
  }
};

module.exports = {
  createPost,
  getAllPosts,
  getSinglePost,
  updatePost,
  deletePost,
  likePost,
  dislikePost,
  getPostsSubscribedTo,
  getUserMedia,
  getPostMedia,
  getUserPosts,
};
