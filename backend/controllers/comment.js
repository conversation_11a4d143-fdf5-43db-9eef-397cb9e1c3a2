const Comment = require("../models/comment");
const Post = require("../models/post");
const { StatusCodes } = require("http-status-codes");
const CustomError = require("../errors");
const {
  createCommentNotification,
  createLikeNotification,
  createCommentLikeNotification,
  createReplyNotification,
} = require("../utils/notification");

const createComment = async (req, res) => {
  const { postId } = req.params;
  const { content } = req.body;

  // Check if post exists
  const post = await Post.findOne({ _id: postId });
  if (!post) {
    throw new CustomError.NotFoundError(`No post with id: ${postId}`);
  }

  // Create comment
  const comment = await Comment.create({
    post: postId,
    user: req.user._id,
    content,
  });

  // Increment post's comment count
  await Post.findByIdAndUpdate(postId, {
    $inc: { commentsCount: 1 },
    $push: { comments: comment._id },
  });

  // Notify creator for comment
  await createCommentNotification(
    postId,
    comment._id,
    req.user._id,
    post.creator,
    `${req.user.username} commented on your post`
  );

  // Populate user info
  await comment.populate("user", "username avatar name");

  res.status(StatusCodes.CREATED).json({ comment });
};

const getPostComments = async (req, res) => {
  const { postId } = req.params;
  const { sort = "-createdAt" } = req.query;

  // Check if post exists
  const post = await Post.findOne({ _id: postId });
  if (!post) {
    throw new CustomError.NotFoundError(`No post with id: ${postId}`);
  }

  // Get comments with pagination
  const page = Number(req.query.page) || 1;
  const limit = Number(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const comments = await Comment.find({ post: postId })
    .populate("user", "username avatar name")
    .sort(sort)
    .skip(skip)
    .limit(limit);

  const totalComments = await Comment.countDocuments({ post: postId });

  res.status(StatusCodes.OK).json({
    comments,
    currentPage: page,
    totalPages: Math.ceil(totalComments / limit),
    totalComments,
  });
};

const updateComment = async (req, res) => {
  const { id: commentId } = req.params;
  const { content } = req.body;

  const comment = await Comment.findOne({ _id: commentId });

  if (!comment) {
    throw new CustomError.NotFoundError(`No comment with id: ${commentId}`);
  }

  // Check if user owns the comment
  if (comment.user.toString() !== req.user._id) {
    throw new CustomError.UnauthorizedError(
      "Not authorized to update this comment"
    );
  }

  comment.content = content;
  await comment.save();

  await comment.populate("user", "username avatar name");

  res.status(StatusCodes.OK).json({ comment });
};

const deleteComment = async (req, res) => {
  const { id: commentId } = req.params;

  const comment = await Comment.findOne({ _id: commentId });

  if (!comment) {
    throw new CustomError.NotFoundError(`No comment with id: ${commentId}`);
  }

  // Check if user owns the comment
  if (comment.user.toString() !== req.user._id) {
    throw new CustomError.UnauthorizedError(
      "Not authorized to delete this comment"
    );
  }

  // Decrement post's comment count
  await Post.findByIdAndUpdate(comment.post, {
    $inc: { commentsCount: -1 },
  });

  await comment.remove();

  res.status(StatusCodes.OK).json({ message: "Comment removed successfully" });
};

const likeComment = async (req, res) => {
  const { id: commentId } = req.params;

  const comment = await Comment.findOne({ _id: commentId }).populate("post");

  if (!comment) {
    throw new CustomError.NotFoundError(`No comment with id: ${commentId}`);
  }

  const isLiked = comment.likes.includes(req.user._id);

  if (isLiked) {
    // Unlike the comment
    comment.likes = comment.likes.filter(
      (like) => like.toString() !== req.user._id
    );
    comment.likesCount -= 1;
  } else {
    // Like the comment
    comment.likes.push(req.user._id);
    comment.likesCount += 1;

    // Only notify on like, not unlike
    if (req.user._id.toString() !== comment.user.toString()) {
      // Notify user for comment like
      await createCommentLikeNotification(
        comment.post._id, // postId
        commentId, // commentId
        req.user._id, // likerId
        comment.user, // commentAuthorId
        `${req.user.username} liked your comment`
      );
    }
  }

  await comment.save();
  await comment.populate("user", "username avatar name");

  res.status(StatusCodes.OK).json({ comment });
};

const createReply = async (req, res) => {
  const { commentId } = req.params;
  const { content } = req.body;

  const parentComment = await Comment.findOne({ _id: commentId });
  if (!parentComment) {
    throw new CustomError.NotFoundError(`No comment with id: ${commentId}`);
  }

  // Create reply as a new comment
  const reply = await Comment.create({
    post: parentComment.post,
    user: req.user._id,
    content,
    parentComment: commentId,
    isReply: true,
  });

  // Add reply to parent comment
  await Comment.findByIdAndUpdate(commentId, {
    $push: { replies: reply._id },
  });

  // Increment post's comment count
  await Post.findByIdAndUpdate(parentComment.post, {
    $inc: { commentsCount: 1 },
  });

  // Notify comment author for reply
  await createReplyNotification(
    parentComment.post,
    commentId,
    req.user._id,
    parentComment.user,
    `${req.user.username} replied to your comment`
  );

  await reply.populate("user", "username avatar name");

  res.status(StatusCodes.CREATED).json({ reply });
};

const getCommentReplies = async (req, res) => {
  const { commentId } = req.params;
  const { sort = "-createdAt" } = req.query;

  const parentComment = await Comment.findOne({ _id: commentId });
  if (!parentComment) {
    throw new CustomError.NotFoundError(`No comment with id: ${commentId}`);
  }

  // Get replies with pagination
  const page = Number(req.query.page) || 1;
  const limit = Number(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const replies = await Comment.find({ parentComment: commentId })
    .populate("user", "username avatar name")
    .sort(sort)
    .skip(skip)
    .limit(limit);

  const totalReplies = await Comment.countDocuments({
    parentComment: commentId,
  });

  res.status(StatusCodes.OK).json({
    replies,
    currentPage: page,
    totalPages: Math.ceil(totalReplies / limit),
    totalReplies,
  });
};

module.exports = {
  createComment,
  getPostComments,
  updateComment,
  deleteComment,
  likeComment,
  createReply,
  getCommentReplies,
};
