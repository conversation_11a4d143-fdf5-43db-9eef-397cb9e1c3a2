const Automation = require("../models/automation");
const { NotFoundError } = require("../errors");

// Create a new automation rule
exports.createAutomation = async (req, res) => {
  const { triggerEvent, message, timing, delayDuration } = req.body;
  const creatorId = req.user._id; // Assuming user is attached by auth middleware

  const automation = await Automation.create({
    creatorId,
    triggerEvent,
    message,
    timing: {
      type: timing,
      delayDuration,
    },
  });

  res.status(201).json({
    status: "success",
    data: automation,
  });
};

// Get all automations for a creator
exports.getCreatorAutomations = async (req, res) => {
  const creatorId = req.user._id;
  const automations = await Automation.find({ creatorId });

  res.status(200).json({
    status: "success",
    data: automations,
  });
};

// Update an automation
exports.updateAutomation = async (req, res) => {
  const { id } = req.params;
  const { triggerEvent, message, timing, isActive, delayDuration } = req.body;
  const creatorId = req.user._id;

  const automation = await Automation.findOne({ _id: id, creatorId });

  if (!automation) {
    throw new NotFoundError("Automation not found", 404);
  }

  const updatedAutomation = await Automation.findByIdAndUpdate(
    id,
    {
      triggerEvent,
      message,
      timing: { type: timing, delayDuration },
      isActive,
    },
    { new: true, runValidators: true }
  ).sort({ createdAt: -1 });

  res.status(200).json({
    status: "success",
    data: updatedAutomation,
  });
};

// Delete an automation
exports.deleteAutomation = async (req, res) => {
  const { id } = req.params;
  const creatorId = req.user._id;

  const automation = await Automation.findOne({ _id: id, creatorId });

  if (!automation) {
    throw new NotFoundError("Automation not found", 404);
  }

  await Automation.findByIdAndDelete(id);

  res.status(204).json({
    status: "success",
    data: null,
  });
};

// Toggle automation status
exports.toggleAutomation = async (req, res) => {
  const { id } = req.params;
  const creatorId = req.user._id;

  const automation = await Automation.findOne({ _id: id, creatorId });

  if (!automation) {
    throw new NotFoundError("Automation not found", 404);
  }

  automation.isActive = !automation.isActive;
  await automation.save();

  res.status(200).json({
    status: "success",
    data: automation,
  });
};
