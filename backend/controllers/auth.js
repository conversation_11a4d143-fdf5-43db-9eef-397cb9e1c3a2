const User = require("../models/user");
const crypto = require("crypto");
const { StatusCodes } = require("http-status-codes");
const {
  BadRequestError,
  UnauthenticatedError,
  NotFoundError,
} = require("../errors");
const { sendVerificationEmail } = require("../utils/sendEmails");
const generateUniqueUsername = require("../utils/generateUsername");
const path = require("path");
const ejs = require("ejs");
const { OAuth2Client } = require("google-auth-library");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");
const Post = require("../models/post");

const register = async (req, res) => {
  // Check if user already exists
  const { email, role, displayName } = req.body;
  const oldUser = await User.findOne({ email });
  if (oldUser) {
    throw new BadRequestError("Another user with this email already exists.");
  }

  // Generate unique username based on display name or email
  const baseString = displayName || email.split("@")[0];
  const username = await generateUniqueUsername(baseString);

  // Generate 6-digit verification code
  const verificationCode = Math.floor(
    100000 + Math.random() * 900000
  ).toString();
  const verificationCodeExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

  const result = await User.create({
    ...req.body,
    username,
    verificationCode,
    verificationCodeExpires,
    isVerified: false,
  });

  // Send verification email
  await sendVerificationEmail(email, verificationCode);

  const token = result.createJWT();
  res.status(StatusCodes.CREATED).json({
    user: result,
    token,
    role,
    message: "Verification code sent to your email",
  });
};

const login = async (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    throw new BadRequestError("Please provide email and password");
  }
  const user = await User.findOne({ email });
  if (!user) {
    throw new UnauthenticatedError(
      "Sorry, we couldn't find an account with that email."
    );
  }

  const isPasswordCorrect = await user.comparePassword(password);
  // compare password

  if (!isPasswordCorrect) {
    throw new UnauthenticatedError(`Sorry, that password isn't right`);
  }

  // Check if email is verified
  if (!user.isVerified) {
    return res.status(StatusCodes.UNAUTHORIZED).json({
      error: true,
      code: "EMAIL_NOT_VERIFIED",
      message: "Please verify your email to continue",
      email: user.email,
    });
  }

  const token = user.createJWT();

  res.status(StatusCodes.OK).json({
    user,
    token,
  });
};

const forgotPassword = async (req, res) => {
  const { email } = req.body;
  if (!email) {
    throw new BadRequestError("Please provide an email address");
  }

  const user = await User.findOne({ email });
  if (!user) {
    throw new NotFoundError("No user found with this email address");
  }

  // Generate reset token
  const resetToken = crypto.randomBytes(32).toString("hex");
  user.resetPasswordToken = crypto
    .createHash("sha256")
    .update(resetToken)
    .digest("hex");
  user.resetPasswordExpire = Date.now() + 10 * 60 * 1000; // 10 minutes

  await user.save();

  // Send reset password email
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

  try {
    await ejs.renderFile(
      path.join(__dirname, "../views/emails/resetPassword.ejs"),
      { resetUrl },
      async (err, data) => {
        if (err) {
          console.log(err);
        } else {
          await sendEmail({
            to: user.email,
            subject: "Password Reset Request",
            html: data,
          });
        }
      }
    );

    res.status(StatusCodes.OK).json({
      message: "Password reset link sent to email",
    });
  } catch (error) {
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save();
    throw new BadRequestError("Email could not be sent");
  }
};

const resetPassword = async (req, res) => {
  const { token } = req.params;
  const { password } = req.body;

  if (!password) {
    throw new BadRequestError("Please provide a new password");
  }

  const resetPasswordToken = crypto
    .createHash("sha256")
    .update(token)
    .digest("hex");

  const user = await User.findOne({
    resetPasswordToken,
    resetPasswordExpire: { $gt: Date.now() },
  });

  if (!user) {
    throw new BadRequestError("Invalid or expired reset token");
  }

  user.password = password;
  user.resetPasswordToken = undefined;
  user.resetPasswordExpire = undefined;
  await user.save();

  res.status(StatusCodes.OK).json({
    message: "Password reset successful",
  });
};

const changePassword = async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  if (!currentPassword || !newPassword) {
    throw new BadRequestError("Please provide both current and new password");
  }

  const user = await User.findById(req.user._id);
  const isPasswordCorrect = await user.comparePassword(currentPassword);

  if (!isPasswordCorrect) {
    throw new BadRequestError("Current password is incorrect");
  }

  user.password = newPassword;
  await user.save();

  res.status(StatusCodes.OK).json({
    message: "Password updated successfully",
  });
};

const getProfile = async (req, res) => {
  const user = await User.findById(req.user._id).select("-password");

  // get user post counts
  const userPosts = await Post.find({ creator: req.user._id });
  const postCount = userPosts.length;

  // Get total active subscribers count
  const Subscription = mongoose.model("Subscription");
  const activeSubscribersAgg = await Subscription.aggregate([
    {
      $match: {
        creator: new mongoose.Types.ObjectId(req.user._id),
        status: "active",
        endDate: { $gte: new Date() },
      },
    },
    {
      $group: {
        _id: "$subscriber",
        latestSubscription: { $last: "$$ROOT" },
      },
    },
    {
      $count: "totalUniqueSubscribers",
    },
  ]);

  if (!user) {
    throw new NotFoundError("User not found");
  }

  const totalActiveSubscribers =
    activeSubscribersAgg[0]?.totalUniqueSubscribers || 0;

  // Destructure and clean up user data
  const {
    _id,
    username,
    email,
    role,
    isEmailVerified,
    isCreator,
    subscriptionCount,
    followersCount,
    followingCount,
    verified,
    subscriptions,
    about,
    avatar,
    displayName,
    coverImage,
    socialLinks,
    paymentInfo,
    pricingPlans,
    pricing,
    createdAt,
    updatedAt,
    kycStatus,
  } = user.toObject();

  const cleanUserData = {
    _id,
    username,
    email,
    role,
    isEmailVerified,
    isCreator,
    subscriberCount: totalActiveSubscribers,
    subscriptionCount: subscriptionCount || 0,
    followersCount: followersCount || 0,
    followingCount: followingCount || 0,
    verified: verified || false,
    subscriptions: subscriptions || [],
    about: about || "",
    avatar: avatar || "",
    displayName: displayName || "",
    coverImage: coverImage || "",
    socialLinks: {
      instagram: socialLinks?.instagram || "",
      facebook: socialLinks?.facebook || "",
      twitter: socialLinks?.twitter || "",
    },
    paymentInfo: {
      verified: paymentInfo?.verified || false,
    },
    pricingPlans: pricingPlans || [],
    pricing: pricing || [],
    createdAt,
    updatedAt,
    totalPosts: postCount,
    kycStatus,
  };

  res.status(StatusCodes.OK).json({
    user: cleanUserData,
  });
};

const updateProfile = async (req, res) => {
  const { email, username, ...updateData } = req.body;

  // Prevent email and username updates through this endpoint
  if (email) {
    throw new BadRequestError("Email cannot be updated through this endpoint");
  }
  // if (username) {
  //   throw new BadRequestError(
  //     "Username cannot be updated through this endpoint"
  //   );
  // }

  const user = await User.findByIdAndUpdate(
    req.user._id,
    { ...updateData },
    { new: true, runValidators: true }
  ).select("-password");

  if (!user) {
    throw new NotFoundError("User not found");
  }

  res.status(StatusCodes.OK).json({ user });
};

const verifyEmail = async (req, res) => {
  const { email, verificationCode } = req.body;

  const user = await User.findOne({ email }).select(
    "+verificationCode +verificationCodeExpires"
  );
  if (!user) {
    throw new BadRequestError("User not found");
  }

  // Add debug logging
  console.log("User verification status:", {
    isVerified: user.isVerified,
    verificationCode: user.verificationCode,
    verificationCodeExpires: user.verificationCodeExpires,
    providedCode: verificationCode,
  });

  if (user.isVerified) {
    throw new BadRequestError("Email already verified");
  }

  if (!user.verificationCode || !user.verificationCodeExpires) {
    throw new BadRequestError("No verification code found");
  }

  if (user.verificationCode !== verificationCode) {
    throw new BadRequestError("Invalid verification code");
  }

  if (user.verificationCodeExpires < new Date()) {
    throw new BadRequestError("Verification code has expired");
  }

  // Update user verification status
  user.isVerified = true;
  user.verificationCode = undefined;
  user.verificationCodeExpires = undefined;
  await user.save();

  const token = user.createJWT();

  res.status(StatusCodes.OK).json({
    message: "Email verified successfully",
    user,
    token,
  });
};

const resendVerificationCode = async (req, res) => {
  const { email } = req.body;

  const user = await User.findOne({ email }).select(
    "+verificationCode +verificationCodeExpires"
  );
  if (!user) {
    throw new BadRequestError("User not found");
  }

  if (user.isVerified) {
    throw new BadRequestError("Email already verified");
  }

  // Generate new verification code
  const verificationCode = Math.floor(
    100000 + Math.random() * 900000
  ).toString();
  const verificationCodeExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

  user.verificationCode = verificationCode;
  user.verificationCodeExpires = verificationCodeExpires;
  await user.save();

  // Log verification status for debugging
  console.log("Resending verification code:", {
    email,
    verificationCode,
    expiresAt: verificationCodeExpires,
  });

  // Send new verification email
  await sendVerificationEmail(email, verificationCode);

  res.status(StatusCodes.OK).json({
    message: "New verification code sent to your email",
  });
};

const googleAuth = async (req, res, next) => {
  try {
    const allowedOrigins = [
      process.env.APP_URL,
      "http://localhost:5173",
      "http://localhost:3000",
      "https://erossphere.com",
      "https://www.erossphere.com",
    ];

    const origin = req.headers.origin;
    if (allowedOrigins.includes(origin)) {
      res.header("Access-Control-Allow-Origin", origin);
    }

    res.header("Referrer-Policy", "no-referrer-when-downgrade");
    res.header("Access-Control-Allow-Credentials", "true");
    res.header(
      "Access-Control-Allow-Methods",
      "GET,HEAD,PUT,PATCH,POST,DELETE"
    );
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    );

    const redirectUrl = `${process.env.API_URL}/api/v1/auth/google/callback`;

    const oauth2Client = new OAuth2Client(
      process.env.CLIENT_ID,
      process.env.CLIENT_SECRET,
      redirectUrl
    );

    const url = oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: "https://www.googleapis.com/auth/userinfo.profile openid email",
      prompt: "consent",
    });

    res.json({ url });
  } catch (error) {
    next(error);
  }
};

const googleCallback = async (req, res, next) => {
  try {
    const allowedOrigins = [
      process.env.APP_URL,
      "http://localhost:5173",
      "http://localhost:3000",
      "https://erossphere.com",
      "https://www.erossphere.com",
    ];

    const origin = req.headers.origin;
    if (allowedOrigins.includes(origin)) {
      res.header("Access-Control-Allow-Origin", origin);
    }

    res.header("Access-Control-Allow-Credentials", "true");
    res.header(
      "Access-Control-Allow-Methods",
      "GET,HEAD,PUT,PATCH,POST,DELETE"
    );
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    );

    const { code } = req.body;
    const redirectUrl = `${process.env.API_URL}/api/v1/auth/google/callback`;

    const oauth2Client = new OAuth2Client(
      process.env.CLIENT_ID,
      process.env.CLIENT_SECRET,
      redirectUrl
    );

    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    const userinfo = await oauth2Client.request({
      url: "https://www.googleapis.com/oauth2/v3/userinfo",
    });

    const { email, name, picture } = userinfo.data;

    // Check if user exists
    let user = await User.findOne({ email });

    if (!user) {
      // Create new user if doesn't exist
      user = await User.create({
        email,
        username: name,
        avatar: picture,
        isEmailVerified: true, // Since Google email is verified
        authProvider: "google",
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    res.json({
      token,
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        avatar: user.avatar,
      },
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  register,
  login,
  forgotPassword,
  resetPassword,
  changePassword,
  getProfile,
  updateProfile,
  verifyEmail,
  resendVerificationCode,
  googleAuth,
  googleCallback,
};
