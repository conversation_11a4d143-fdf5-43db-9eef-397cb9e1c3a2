/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#E91E63",
          100: "#FCE4EC",
          200: "#F8BBD0",
          300: "#F48FB1",
          400: "#F06292",
          500: "#EC407A",
        },
        secondary: {
          DEFAULT: "#16001E",
          100: "#2D0039",
          200: "#450055",
          300: "#5D0071",
          400: "#75008D",
          500: "#8D00A9",
        },
        success: {
          DEFAULT: "#4CAF50",
          100: "#E8F5E9",
          500: "#2E7D32",
        },
        warning: {
          DEFAULT: "#FFC107",
          100: "#FFF8E1",
          500: "#FFA000",
        },
        error: {
          DEFAULT: "#F44336",
          100: "#FFEBEE",
          500: "#C62828",
        },
      },
      fontFamily: {
        sans: ["DM Sans", "sans-serif"],
        heading: ["DM Serif Display", "sans-serif"],
      },
      keyframes: {
        shimmer: {
          "100%": {
            transform: "translateX(100%)",
          },
        },
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideIn: {
          "0%": { transform: "translateY(100%)" },
          "100%": { transform: "translateY(0)" },
        },
        pulse: {
          "0%, 100%": { opacity: "1" },
          "50%": { opacity: ".5" },
        },
        bounce: {
          "0%, 100%": {
            transform: "translateY(-25%)",
            animationTimingFunction: "cubic-bezier(0.8,0,1,1)",
          },
          "50%": {
            transform: "none",
            animationTimingFunction: "cubic-bezier(0,0,0.2,1)",
          },
        },
        "modal-enter": {
          "0%": {
            opacity: "0",
            transform: "scale(0.95)",
          },
          "100%": {
            opacity: "1",
            transform: "scale(1)",
          },
        },
      },
      animation: {
        shimmer: "shimmer 1.5s infinite",
        fadeIn: "fadeIn 0.5s ease-in",
        slideIn: "slideIn 0.5s ease-out",
        pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        bounce: "bounce 1s infinite",
        "modal-enter": "modal-enter 0.3s ease-out",
      },
      spacing: {
        128: "32rem",
        144: "36rem",
      },
      borderRadius: {
        "4xl": "2rem",
        "5xl": "2.5rem",
      },
      boxShadow: {
        soft: "0 2px 15px -3px rgba(0,0,0,0.07), 0 10px 20px -2px rgba(0,0,0,0.04)",
        hard: "0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04)",
      },
      backdropBlur: {
        xs: "2px",
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: "65ch",
            color: "inherit",
            a: {
              color: "#E91E63",
              "&:hover": {
                color: "#EC407A",
              },
            },
          },
        },
      },
    },
  },
  plugins: [require("tailwind-scrollbar-hide")],
};
