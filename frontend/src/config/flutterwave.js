import PropTypes from "prop-types";

export const flutterwaveConfig = (config) => {
  return {
    public_key: "FLWPUBK_TEST-bda917bfc5791e72ec69d2cd86177432-X",
    tx_ref: Date.now(),
    amount: config.amount,
    currency: "NGN",
    payment_options: "card,mobilemoney,ussd",
    customer: {
      email: config.email,
      phone_number: "09071499826",
      name: config.displayName,
    },
    customizations: {
      title: config.title,
      description: config.description,
      logo: "https://erossphere.com/logo.png",
    },
  };
};
