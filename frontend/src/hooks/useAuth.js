import React, { useContext } from "react";
import { AuthContext } from "../context/AuthContext";
import api from "../utils/axios";

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  const googleSignIn = async () => {
    return api.post("/auth/google");
  };

  const handleGoogleCallback = async (code) => {
    const response = await api.post("/auth/google/callback", { code });
    if (response.data.token) {
      // Update auth context with the new token and user data
      context.login(response.data.token, response.data.user);
    }
    return response;
  };

  return {
    ...context,
    googleSignIn,
    handleGoogleCallback,
  };
};
