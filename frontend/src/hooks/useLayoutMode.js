import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "./useAuth";

/**
 * Custom hook to manage layout mode switching between fan and creator modes
 * Provides intelligent mode detection and smooth transitions
 */
export const useLayoutMode = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Determine current mode based on URL and user state
  const getCurrentMode = () => {
    // If user is not a creator, always fan mode
    if (!user?.isCreator) {
      return "fan";
    }

    // Check if we're on creator-specific routes
    if (location.pathname.startsWith("/creator")) {
      return "creator";
    }

    // Check if we're on fan-specific routes
    const fanRoutes = ["/subscriptions", "/wallet"];
    if (fanRoutes.some((route) => location.pathname.startsWith(route))) {
      return "fan";
    }

    // Define neutral routes that can be accessed in both modes
    const neutralRoutes = [
      "/",
      "/profile",
      "/settings",
      "/notifications",
      "/post",
    ];
    const isNeutralRoute = neutralRoutes.some(
      (route) =>
        location.pathname === route || location.pathname.startsWith(route + "/")
    );

    if (isNeutralRoute) {
      // For neutral routes, check localStorage preference or default to creator mode for creators
      const savedMode = localStorage.getItem("preferredMode");
      if (savedMode && ["fan", "creator"].includes(savedMode)) {
        return savedMode;
      }
      // Default to creator mode for creators on neutral routes
      return "creator";
    }

    // For other routes, check localStorage preference or default to fan
    const savedMode = localStorage.getItem("preferredMode");
    if (savedMode && ["fan", "creator"].includes(savedMode)) {
      return savedMode;
    }

    // Default to fan mode for other routes
    return "fan";
  };

  const [currentMode, setCurrentMode] = useState(getCurrentMode);

  // Update mode when location changes
  useEffect(() => {
    const newMode = getCurrentMode();
    if (newMode !== currentMode) {
      setCurrentMode(newMode);
    }
  }, [location.pathname, user?.isCreator]);

  // Save mode preference to localStorage
  useEffect(() => {
    if (user?.isCreator) {
      localStorage.setItem("preferredMode", currentMode);
    }
  }, [currentMode, user?.isCreator]);

  /**
   * Switch between fan and creator modes
   * @param {string} targetMode - 'fan' or 'creator'
   * @param {object} options - Additional options for the switch
   */
  const switchMode = async (targetMode, options = {}) => {
    const {
      preserveRoute = false,
      redirectTo = null,
      showTransition = true,
    } = options;

    // Validate target mode
    if (!["fan", "creator"].includes(targetMode)) {
      console.error("Invalid target mode:", targetMode);
      return;
    }

    // Check if user can switch to creator mode
    if (targetMode === "creator" && !user?.isCreator) {
      // Show onboarding to become creator
      localStorage.removeItem(`onboarding_completed_${user._id}`);
      localStorage.removeItem(`onboarding_skipped_${user._id}`);
      window.location.reload();
      return;
    }

    // Show transition if enabled
    if (showTransition) {
      setIsTransitioning(true);
    }

    try {
      // Determine target route
      let targetRoute;

      if (redirectTo) {
        targetRoute = redirectTo;
      } else if (preserveRoute && targetMode === "creator") {
        // Try to preserve the current route in creator mode
        const currentPath = location.pathname;
        if (currentPath === "/" || currentPath === "/feed") {
          targetRoute = "/creator";
        } else if (currentPath.startsWith("/notifications")) {
          targetRoute = "/creator/notifications";
        } else if (
          currentPath.startsWith("/chats") ||
          currentPath.startsWith("/messages")
        ) {
          targetRoute = "/creator/messages";
        } else {
          targetRoute = "/creator";
        }
      } else if (preserveRoute && targetMode === "fan") {
        // Try to preserve the current route in fan mode
        const currentPath = location.pathname;
        if (currentPath.startsWith("/creator/notifications")) {
          targetRoute = "/notifications";
        } else if (currentPath.startsWith("/creator/messages")) {
          targetRoute = "/chats";
        } else if (currentPath.startsWith("/creator")) {
          targetRoute = "/";
        } else {
          targetRoute = currentPath;
        }
      } else {
        // Default routes for each mode
        targetRoute = targetMode === "creator" ? "/creator" : "/";
      }

      // Update mode state
      setCurrentMode(targetMode);

      // Navigate to target route
      navigate(targetRoute);

      // Small delay to ensure smooth transition
      if (showTransition) {
        setTimeout(() => {
          setIsTransitioning(false);
        }, 300);
      }
    } catch (error) {
      console.error("Error switching layout mode:", error);
      setIsTransitioning(false);
    }
  };

  /**
   * Get the appropriate navigation items for current mode
   */
  const getNavigationConfig = () => {
    const baseItems = [
      {
        name: "Feed",
        icon: "solar:home-angle-broken",
        path: currentMode === "creator" ? "/creator" : "/",
        showOnMobile: true,
      },
      {
        name: "Notifications",
        icon: "solar:bell-line-duotone",
        path:
          currentMode === "creator"
            ? "/creator/notifications"
            : "/notifications",
        showOnMobile: true,
      },
      {
        name: "Messages",
        icon: "solar:chat-dots-line-duotone",
        path: currentMode === "creator" ? "/creator/messages" : "/chats",
        showOnMobile: true,
      },
    ];

    const fanItems = [
      {
        name: "Subscriptions",
        icon: "solar:user-plus-linear",
        path: "/subscriptions",
        showOnMobile: false,
      },
      {
        name: "Wallet",
        icon: "solar:wallet-broken",
        path: "/wallet",
        showOnMobile: false,
      },
    ];

    const creatorItems = [
      {
        name: "Posts",
        icon: "solar:posts-carousel-vertical-line-duotone",
        path: "/creator/posts",
        showOnMobile: false,
      },
      {
        name: "Add post",
        icon: "basil:add-outline",
        path: "/creator/add-post",
        showOnMobile: false,
      },
      {
        name: "Stories",
        icon: "iconoir:lens-plus",
        path: "/creator/stories",
        showOnMobile: false,
      },
      {
        name: "Subscribers",
        icon: "solar:user-plus-linear",
        path: "/creator/subscribers",
        showOnMobile: false,
      },
      {
        name: "Earnings",
        icon: "solar:wallet-broken",
        path: "/creator/transactions",
        showOnMobile: false,
      },
      {
        name: "Manage subscription",
        icon: "solar:card-2-linear",
        path: "/creator/manage-subscription",
        showOnMobile: false,
      },
    ];

    const modeSpecificItems =
      currentMode === "creator" ? creatorItems : fanItems;

    const commonItems = [
      {
        name: "My profile",
        icon: "solar:user-outline",
        path: "/profile",
        showOnMobile: false,
      },
      {
        name: "Settings",
        icon: "solar:settings-minimalistic-outline",
        path: currentMode === "creator" ? "/creator/account" : "/settings",
        showOnMobile: false,
      },
    ];

    return [...baseItems, ...modeSpecificItems, ...commonItems];
  };

  /**
   * Check if current route is valid for the current mode
   */
  const isValidRoute = () => {
    const currentPath = location.pathname;

    // Define neutral routes that can be accessed in both modes
    const neutralRoutes = [
      "/",
      "/profile",
      "/settings",
      "/notifications",
      "/post",
    ];
    const isNeutralRoute = neutralRoutes.some(
      (route) => currentPath === route || currentPath.startsWith(route + "/")
    );

    if (isNeutralRoute) {
      // Neutral routes are valid for both modes
      return true;
    }

    if (currentMode === "creator") {
      // Creator mode should be on creator routes or neutral routes
      return currentPath.startsWith("/creator");
    } else {
      // Fan mode should not be on creator-specific routes
      return !currentPath.startsWith("/creator");
    }
  };

  /**
   * Get mode-specific styling
   */
  const getModeStyles = () => {
    return {
      sidebarBg: currentMode === "creator" ? "bg-gray-100" : "bg-white",
      accentColor:
        currentMode === "creator" ? "text-purple-600" : "text-blue-600",
      borderColor:
        currentMode === "creator" ? "border-purple-200" : "border-blue-200",
    };
  };

  return {
    currentMode,
    isCreatorMode: currentMode === "creator",
    isFanMode: currentMode === "fan",
    isTransitioning,
    canSwitchToCreator: user?.isCreator,
    switchMode,
    getNavigationConfig,
    isValidRoute,
    getModeStyles,
  };
};
