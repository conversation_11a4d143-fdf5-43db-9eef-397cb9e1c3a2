import { useEffect } from "react";
import { io } from "socket.io-client";
import { useQueryClient } from "@tanstack/react-query";
import { chatKeys } from "../services/chatQueries";
import useChatStore from "../store/chatStore";

const SOCKET_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

export const useSocket = (userId) => {
  const queryClient = useQueryClient();
  const { setSocketConnected, updateUserStatus } = useChatStore();

  useEffect(() => {
    if (!userId) return;

    const socket = io(SOCKET_URL, {
      query: { userId },
    });

    socket.on("connect", () => {
      console.log("Socket connected");
      setSocketConnected(true);

      // When connected, emit user activity to update online status
      socket.emit("user_activity");

      // Join the socket room with the user's ID
      socket.emit("join", userId);

      // Request status of all users in conversations
      socket.emit("get_user_status", userId);
    });

    socket.on("disconnect", () => {
      console.log("Socket disconnected");
      setSocketConnected(false);
    });

    socket.on("new_message", (message) => {
      // Update messages in the cache
      queryClient.setQueryData(
        chatKeys.messages(message.conversation),
        (old) => {
          if (!old) return { messages: [message] };
          return {
            ...old,
            messages: [...old.messages, message],
          };
        }
      );

      // Update conversations list to show latest message
      queryClient.setQueryData(chatKeys.conversations(), (old) => {
        if (!old) return old;

        const updatedConversations = old.conversations.map((conv) => {
          if (conv._id === message.conversation) {
            return {
              ...conv,
              lastMessage: message,
              unreadCount:
                message.sender._id !== userId
                  ? (conv.unreadCount || 0) + 1
                  : conv.unreadCount,
            };
          }
          return conv;
        });

        // Calculate new total unread count
        const totalUnread = updatedConversations.reduce(
          (sum, conv) => sum + (conv.unreadCount || 0),
          0
        );

        return {
          ...old,
          conversations: updatedConversations,
          totalUnreadCount: totalUnread,
        };
      });
    });

    socket.on("message_read", ({ conversationId, readBy }) => {
      // Update messages to show read status
      queryClient.setQueryData(chatKeys.messages(conversationId), (old) => {
        if (!old) return old;
        return {
          ...old,
          messages: old.messages.map((msg) => ({
            ...msg,
            readBy: [...new Set([...msg.readBy, readBy])],
          })),
        };
      });
    });

    // Handle user status updates
    socket.on("user_status", ({ userId, online, lastSeen }) => {
      updateUserStatus(userId, online, lastSeen);
    });

    socket.on("user_status_change", ({ userId, online, lastSeen }) => {
      updateUserStatus(userId, online, lastSeen);

      // Also update the UI for any open conversations
      queryClient.invalidateQueries(chatKeys.conversations());
    });

    // Set up a periodic ping to keep the connection alive and update activity status
    const activityInterval = setInterval(() => {
      if (socket.connected) {
        socket.emit("user_activity");
      }
    }, 60000); // Every minute

    return () => {
      clearInterval(activityInterval);
      socket.disconnect();
    };
  }, [userId, queryClient, setSocketConnected, updateUserStatus]);
};
