import { Outlet } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import AdaptiveLayout from "./AdaptiveLayout";
import DashboardHeader from "../components/dashboard/DashboardHeader";
import { useLayoutMode } from "../hooks/useLayoutMode";
import { useAuth } from "../hooks/useAuth";

/**
 * Main layout component that adapts to user type and current mode
 * Replaces the separate UserLayout and CreatorLayout components
 */
export default function MainLayout() {
  const { user } = useAuth();
  const { isCreatorMode, isTransitioning } = useLayoutMode();

  return (
    <>
      <Helmet>
        <title>
          {isCreatorMode ? "Creator Dashboard" : "Erossphere"} - 
          {user?.displayName || user?.username || "Welcome"}
        </title>
      </Helmet>
      
      <div className="min-h-screen bg-gray-50">
        <div className="flex">
          {/* Adaptive Sidebar */}
          <AdaptiveLayout />

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col min-h-screen">
            {/* Header */}
            <div className={`sticky top-0 z-40 ${isTransitioning ? 'opacity-75' : ''}`}>
              <DashboardHeader />
            </div>

            {/* Page Content */}
            <main className={`flex-1 p-4 sm:p-6 pb-20 sm:pb-6 ${isTransitioning ? 'opacity-75 transition-opacity duration-300' : ''}`}>
              <div className="max-w-7xl mx-auto">
                <Outlet />
              </div>
            </main>
          </div>
        </div>

        {/* Mode Transition Overlay */}
        {isTransitioning && (
          <div className="fixed inset-0 bg-black bg-opacity-10 z-50 flex items-center justify-center">
            <div className="bg-white rounded-2xl p-6 shadow-xl flex items-center gap-3">
              <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              <span className="text-gray-700 font-medium">
                Switching to {isCreatorMode ? 'Creator' : 'Fan'} mode...
              </span>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
