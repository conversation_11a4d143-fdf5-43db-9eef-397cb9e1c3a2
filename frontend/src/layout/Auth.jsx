import { Link, Outlet } from "react-router-dom";
import { useState, useEffect } from "react";

export default function AuthLayout() {
  const images = [
    "/images/slide1.jpeg",
    "/images/slide2.webp",
    "/images/slide3.jpeg",
  ];
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex min-h-screen flex-col lg:flex-row justify-center items-center lg:justify-start lg:items-stretch">
      <div className="hidden lg:flex lg:w-1/2 relative bg-primary overflow-hidden">
        {images.map((image, index) => (
          <div
            key={image}
            className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
              index === currentImageIndex ? "opacity-100" : "opacity-0"
            }`}
          >
            <img
              src={image}
              alt={`Slide ${index + 1}`}
              className="object-cover w-full h-full"
            />
          </div>
        ))}
        <div className="relative z-10 p-12 flex flex-col justify-between">
          <div>
            <img src="/logo-text.png" alt="AMU Logo" className="w-48" />
          </div>
          <div className="text-white">
            <h2 className="text-4xl font-medium mb-4">
              Discover and Support Your Cherished Content Creators
            </h2>
            <p className="text-lg">
              Explore and follow your favorite creators while enjoying their
              exclusive content and building meaningful connections.
            </p>
            <div className="flex gap-2 mt-4">
              {images.map((_, index) => (
                <div
                  key={index}
                  className={`w-8 h-1 rounded transition-colors duration-300 ${
                    index === currentImageIndex ? "bg-white" : "bg-gray-400"
                  }`}
                ></div>
              ))}
            </div>
          </div>
        </div>
        <div className="absolute top-4 right-4">
          <Link
            to="/"
            className="text-white px-4 py-2 rounded-full border border-white/30 backdrop-blur-sm"
          >
            Back to website →
          </Link>
        </div>
      </div>
      <div className="flex lg:hidden justify-center items-center">
        <img src="/logo-text.png" alt="AMU Logo" className="w-36" />
      </div>
      <Outlet />
    </div>
  );
}
