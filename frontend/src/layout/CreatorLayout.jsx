import { Outlet, <PERSON> } from "react-router-dom";
import Modal from "../components/Modal";
import { useLocation, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useAuth } from "../hooks/useAuth";
import useNotificationStore from "../store/notificationStore";
import { useConversations } from "../services/chatQueries";
import { Icon } from "@iconify/react";
import Avatar from "../components/Avatar";
import InteractiveButton from "../components/InteractiveButton";
import { FaPlus, FaPen, FaVideo } from "react-icons/fa";
import { BsCameraVideo } from "react-icons/bs";

const navItems = [
  {
    name: "Dashboard",
    icon: <Icon icon="solar:home-angle-broken" fontSize={30} />,
    path: "/creator/",
    showOnMobile: true,
  },
  {
    name: "Add post",
    icon: <Icon fontSize={30} icon="basil:add-outline" />,
    path: "/creator/add-post",
    showOnMobile: false,
  },
  {
    name: "Posts",
    icon: (
      <Icon fontSize={30} icon="solar:posts-carousel-vertical-line-duotone" />
    ),
    path: "/creator/posts",
    showOnMobile: false,
  },
  {
    name: "Live stream",
    icon: <Icon fontSize={30} icon="solar:camera-line-duotone" />,
    path: "/creator/live-stream",
    showOnMobile: false,
    comingSoon: true,
  },
  {
    name: "Stories",
    icon: <Icon fontSize={30} icon="iconoir:lens-plus" />,
    path: "/creator/stories",
    showOnMobile: false,
  },
  {
    name: "Messages",
    icon: <Icon fontSize={30} icon="solar:chat-dots-line-duotone" />,
    path: "/creator/messages",
    showOnMobile: true,
  },
  {
    name: "Notifications",
    icon: <Icon fontSize={30} icon="solar:bell-line-duotone" />,
    path: "/creator/notifications",
    showOnMobile: true,
  },

  // {
  //   name: "Collections",
  //   icon: <Icon fontSize={30} icon="fluent:collections-30-regular" />,
  //   path: "/collections",
  //   showOnMobile: true,
  // },
  {
    name: "Subscribers",
    icon: <Icon fontSize={30} icon="solar:user-plus-linear" />,
    path: "/creator/subscribers",
    showOnMobile: false,
  },
  {
    name: "Earnings",
    icon: <Icon fontSize={30} icon="solar:wallet-broken" />,
    path: "/creator/transactions",
    showOnMobile: false,
  },

  {
    name: "Manage subscription",
    icon: <Icon fontSize={30} icon="solar:card-2-linear" />,
    path: "/creator/manage-subscription",
    showOnMobile: true,
  },

  // {
  //   name: "Wallet",
  //   icon: <Icon icon="solar:wallet-broken" fontSize={30} />,
  //   path: "/wallet",
  //   showOnMobile: false,
  // },
  // {
  //   name: "My profile",
  //   icon: <Icon fontSize={30} icon="solar:user-outline" />,
  //   path: "/creator/profile",
  //   showOnMobile: false,
  // },
  {
    name: "Settings",
    icon: <Icon fontSize={30} icon="solar:settings-minimalistic-outline" />,
    path: "/creator/account",
    showOnMobile: false,
  },
  {
    name: "More",
    icon: <Icon fontSize={30} icon="ri:more-fill" />,
    path: "/more",
    showOnMobile: true,
  },

  {
    name: "Logout",
    icon: <Icon fontSize={30} icon="solar:logout-linear" />,
    path: "/logout",
    showOnMobile: false,
  },
];
export default function CreatorLayout() {
  const location = useLocation();
  const { user, logout } = useAuth();
  const [showAddContent, setShowAddContent] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { fetchNotifications, notifications, loading, totalUnread } =
    useNotificationStore();

  const { data: chatData, isLoading: chatLoading } = useConversations();
  const totalUnreadCount = chatData?.totalUnreadCount || 0;
  const navigate = useNavigate();
  return (
    <div className="flex gap-4 mt-0 max-w-[90rem] w-full h-full mx-auto">
      {/* Floating Add Content Button */}
      <div className="fixed bottom-20 md:bottom-8 right-8 z-50">
        <div className="relative">
          {/* <button
            onClick={() => setShowAddContent(!showAddContent)}
            className="bg-primary hover:bg-primary-400 text-white px-6 py-3 rounded-full font-medium flex items-center gap-2 shadow-lg transition-all duration-200"
          >
            <FaPlus className="w-5 h-5" />
            <span className="hidden md:block">Add content</span>
          </button> */}

          {/* Dropdown Menu */}
          {showAddContent && (
            <div className="absolute bottom-full right-0 mb-2 w-64 bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="p-2 space-y-1">
                <button
                  onClick={() => navigate("/creator/add-post")}
                  className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200"
                >
                  <FaPen className="w-5 h-5 text-primary" />
                  <span className="font-medium">Add new post</span>
                </button>
                <button className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200">
                  <FaPlus className="w-5 h-5 text-primary" />
                  <span className="font-medium">Add to story</span>
                </button>
                <button className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200">
                  <FaVideo className="w-5 h-5 text-primary" />
                  <span className="font-medium">Add new clip</span>
                </button>
                <button
                  onClick={() => navigate("/creator/live-stream")}
                  className="w-full flex items-center gap-3 px-4 py-3 text-left text-secondary hover:bg-gray-50 rounded-xl transition-colors duration-200"
                >
                  <BsCameraVideo className="w-5 h-5 text-primary" />
                  <span className="font-medium">Go live</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Click Away Layer */}
      {showAddContent && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowAddContent(false)}
        ></div>
      )}
      <>
        {/* Desktop and Tablet View */}
        <div className="hidden sm:block ">
          <div className="w-72 lg:w-72 sm:w-16 h-screen flex-shrink-0 sticky top-0">
            <div className="bg-gray-100  h-full p-4">
              {/* User Profile Section - Hide on small tablet */}
              <div className="hidden lg:block">
                <div className="flex items-center gap-3 mb-4">
                  <Avatar avatar={user?.avatar} />
                  <div>
                    <h3 className="font-semibold">
                      {user?.displayName || user?.username}
                    </h3>
                    <p className="text-gray-500 text-sm">@{user?.username}</p>
                  </div>
                </div>

                <div className="flex justify-between text-sm text-gray-500 mb-6">
                  <div>
                    <div className="font-semibold text-black">
                      {user?.subscriberCount}
                    </div>
                    Subscribers
                  </div>
                  <div>
                    <div className="font-semibold text-black">
                      {user?.followingCount}
                    </div>
                    Following
                  </div>
                  <div>
                    <div className="font-semibold text-black">
                      {user?.totalPosts}
                    </div>
                    Post
                  </div>
                </div>
              </div>

              {/* Navigation Items */}
              <nav className="space-y-2">
                {navItems
                  .filter((item) => !item.showOnMobile || item.name !== "More")
                  .map((item, index) => (
                    <Link
                      to={item.comingSoon ? "#" : item.path}
                      className={`flex items-center gap-3 p-2 relative rounded-lg transition-colors ${
                        location.pathname === item.path
                          ? "bg-blue-50 text-primary"
                          : "text-gray-600 hover:bg-gray-50"
                      } ${
                        item.comingSoon
                          ? "opacity-50 cursor-not-allowed pointer-events-none"
                          : ""
                      }`}
                      key={index}
                      onClick={(e) => {
                        if (item.path === "/logout") {
                          e.preventDefault();
                          logout();
                        }
                        if (item.comingSoon) {
                          e.preventDefault();
                        }
                      }}
                    >
                      <div className="flex items-center gap-3">
                        {item.icon}
                        <span className="hidden lg:inline">
                          {item.name}
                          {item.comingSoon && (
                            <span className="text-xs"> (Coming Soon)</span>
                          )}
                        </span>
                      </div>
                      {item.path === "/notifications" && totalUnread > 0 && (
                        <div className="!w-5 h-5 absolute top-[2px] right-[-10px] bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                          {totalUnread}
                        </div>
                      )}
                      {item.path === "/chats" && totalUnreadCount > 0 && (
                        <div className="!w-5 h-5 absolute top-[2px] right-[-10px] bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                          {totalUnreadCount}
                        </div>
                      )}
                    </Link>
                  ))}
              </nav>
            </div>
          </div>
        </div>

        {/* Mobile Bottom Navigation */}
        <div className="sm:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-50">
          <nav className="flex justify-around py-2">
            {navItems
              .filter((item) => item.showOnMobile)
              .map((item, index) => (
                <Link
                  to={item.comingSoon ? "#" : item.path}
                  className={`p-2 rounded-lg transition-colors relative ${
                    location.pathname === item.path
                      ? "text-primary"
                      : "text-gray-600"
                  } ${
                    item.comingSoon
                      ? "opacity-50 cursor-not-allowed pointer-events-none"
                      : ""
                  }`}
                  key={index}
                  onClick={(e) => {
                    if (item.name === "More") {
                      e.preventDefault();
                      setIsMobileMenuOpen(true);
                    }
                    if (item.comingSoon) {
                      e.preventDefault();
                    }
                  }}
                >
                  {item.icon}
                  {item.comingSoon && (
                    <div className="absolute -top-1 -right-1 text-[10px] bg-gray-200 px-1 rounded">
                      Soon
                    </div>
                  )}
                  {item.path === "/notifications" && totalUnread > 0 && (
                    <div className="!w-5 h-5 absolute top-[2px] right-1 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                      {totalUnread}
                    </div>
                  )}
                  {item.path === "/messages" && totalUnreadCount > 0 && (
                    <div className="!w-5 h-5 absolute top-[2px] right-1 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                      {totalUnreadCount}
                    </div>
                  )}
                </Link>
              ))}
          </nav>
        </div>

        {/* Mobile Menu Modal */}
        <Modal
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
          // title="Menu"
          className="sm:hidden"
        >
          <div className="flex flex-col space-y-2">
            {/* User Profile Section */}
            <div className="flex items-center gap-3 p-4 border-b border-gray-100">
              <Avatar avatar={user?.avatar} />
              <div>
                <h3 className="font-semibold">
                  {user?.displayName || user?.username}
                </h3>
                <p className="text-gray-500 text-sm">@{user?.username}</p>
              </div>
            </div>

            {/* Stats Section */}
            <div className="flex justify-between text-sm text-gray-500 p-4 border-b border-gray-100">
              <div>
                <div className="font-semibold text-black">
                  {user?.subscriberCount}
                </div>
                Subscribers
              </div>
              <div>
                <div className="font-semibold text-black">
                  {user?.followingCount}
                </div>
                Following
              </div>
              <div>
                <div className="font-semibold text-black">
                  {user?.totalPosts}
                </div>
                Post
              </div>
            </div>

            {/* Navigation Items */}
            <div className="p-2">
              {navItems
                .filter(
                  (item) => item.path !== "/more" && item.path !== "/add-post"
                )
                .map((item, index) => (
                  <Link
                    to={item.comingSoon ? "#" : item.path}
                    className={`flex relative items-center gap-3 p-3 rounded-lg transition-colors ${
                      location.pathname === item.path
                        ? "bg-blue-50 text-primary"
                        : "text-gray-600 hover:bg-gray-50"
                    } ${
                      item.comingSoon
                        ? "opacity-50 cursor-not-allowed pointer-events-none"
                        : ""
                    }`}
                    key={index}
                    onClick={(e) => {
                      if (item.path === "/logout") {
                        e.preventDefault();
                        logout();
                      }
                      if (item.comingSoon) {
                        e.preventDefault();
                      }
                      setIsMobileMenuOpen(false);
                    }}
                  >
                    {item.icon}
                    <span>
                      {item.name}
                      {item.comingSoon && (
                        <span className="text-xs"> (Coming Soon)</span>
                      )}
                    </span>
                    {item.path === "/notifications" && totalUnread > 0 && (
                      <div className="ml-auto w-5 h-5 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                        {totalUnread}
                      </div>
                    )}
                    {item.path === "/messages" && totalUnreadCount > 0 && (
                      <div className="ml-auto w-5 h-5 bg-red-500 rounded-full text-white flex items-center justify-center text-xs">
                        {totalUnreadCount}
                      </div>
                    )}
                  </Link>
                ))}
            </div>

            {/* Add Post Button */}
            <div className="p-4">
              <InteractiveButton
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  navigate("/add-post");
                }}
                className="w-full"
              >
                Add post
              </InteractiveButton>
            </div>
          </div>
        </Modal>
      </>
      <div className="w-full">
        <Outlet />
      </div>
    </div>
  );
}
