import { useEffect, useState } from "react";
import { useAuth } from "../hooks/useAuth";
import OnboardingFlow from "../components/auth/OnboardingFlow";

/**
 * Guard component that shows onboarding flow for new users
 * Checks if user needs onboarding and shows appropriate flow
 */
export default function OnboardingGuard({ children }) {
  const { user, isLoading } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (isLoading) return;

    if (!user) {
      setIsChecking(false);
      return;
    }

    // Check if user needs onboarding
    const needsOnboarding = checkIfNeedsOnboarding(user);
    setShowOnboarding(needsOnboarding);
    setIsChecking(false);
  }, [user, isLoading]);

  /**
   * Determine if user needs onboarding based on their profile completeness
   */
  const checkIfNeedsOnboarding = (user) => {
    // Skip onboarding if user has explicitly dismissed it
    const hasSkippedOnboarding = localStorage.getItem(`onboarding_skipped_${user._id}`);
    if (hasSkippedOnboarding) {
      return false;
    }

    // Check if user has completed basic onboarding steps
    const hasCompletedOnboarding = localStorage.getItem(`onboarding_completed_${user._id}`);
    if (hasCompletedOnboarding) {
      return false;
    }

    // New users (registered within last 24 hours) should see onboarding
    const userCreatedAt = new Date(user.createdAt);
    const now = new Date();
    const hoursSinceRegistration = (now - userCreatedAt) / (1000 * 60 * 60);
    
    // Show onboarding for users registered within last 24 hours
    // and who haven't set a user type preference
    if (hoursSinceRegistration < 24 && !user.userType && !user.hasCompletedOnboarding) {
      return true;
    }

    return false;
  };

  // Show loading state while checking
  if (isChecking || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show onboarding flow if needed
  if (showOnboarding) {
    return <OnboardingFlow />;
  }

  // Render children if no onboarding needed
  return children;
}
