import { Icon } from "@iconify/react";
import { useState, useEffect } from "react";
import { useAuth } from "../hooks/useAuth";
import { FiSend, FiSearch, FiMessageSquare, FiUsers } from "react-icons/fi";
import { useNavigate, useParams } from "react-router-dom";
import Avatar from "../components/Avatar";
import { BsCheckAll } from "react-icons/bs";
import {
  useConversations,
  useStartNewConversation,
} from "../services/chatQueries";
import { useSocket } from "../hooks/useSocket";

export default function Messages() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [subscribedCreators, setSubscribedCreators] = useState([]);
  const [activeTab, setActiveTab] = useState("conversations");
  const [activeConversation, setActiveConversation] = useState(null);
  const { id: conversationId } = useParams();

  // Use TanStack Query hooks
  const { data, isLoading } = useConversations();
  const startNewConversationMutation = useStartNewConversation();

  // Set up socket connection
  useSocket(user?._id);

  // Start a new conversation with a creator
  const handleStartNewConversation = async (creator) => {
    // First check if conversation already exists
    const existingConv = data?.conversations?.find((conv) =>
      conv.participants.some((p) => p._id === creator._id)
    );

    if (existingConv) {
      setActiveConversation(existingConv._id);
      setActiveTab("conversations");
      return;
    }

    // If no existing conversation, create a new one
    try {
      const newConversation = await startNewConversationMutation.mutateAsync(
        creator._id
      );
      if (newConversation) {
        setActiveTab("conversations");
        setActiveConversation(newConversation._id);
      }
    } catch (error) {
      console.error("Error starting new conversation:", error);
    }
  };

  useEffect(() => {
    if (conversationId) {
      setActiveConversation(conversationId);
    }
  }, [conversationId]);

  // Filter conversations and creators based on search
  const filteredConversations =
    data?.conversations?.filter((conv) =>
      conv.participants.some(
        (p) =>
          p.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          p.username?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    ) || [];

  const filteredCreators = subscribedCreators.filter(
    (creator) =>
      creator.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      creator.displayName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="md:w-[400px] w-full border-r border-gray-200 bg-white h-full flex flex-col">
      <div className="p-4 border-b">
        <h2 className="text-xl font-bold flex items-center justify-between">
          Messages
          {data?.totalUnreadCount > 0 && (
            <span className="bg-primary-500 text-white text-xs rounded-full px-2 py-1">
              {data.totalUnreadCount}
            </span>
          )}
        </h2>
        <div className="relative mt-4">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder={
              activeTab === "conversations"
                ? "Search conversations..."
                : "Search subscribed creators..."
            }
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 p-2 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
      </div>

      <div className="flex border-b">
        <button
          onClick={() => setActiveTab("conversations")}
          className={`flex items-center justify-center gap-2 flex-1 px-4 py-3 transition-colors ${
            activeTab === "conversations"
              ? "border-b-2 border-primary text-primary"
              : "text-gray-500 hover:bg-gray-50"
          }`}
        >
          <FiMessageSquare />
          <span>Chats</span>
        </button>
        <button
          onClick={() => setActiveTab("creators")}
          className={`flex items-center justify-center gap-2 flex-1 px-4 py-3 transition-colors ${
            activeTab === "creators"
              ? "border-b-2 border-primary text-primary"
              : "text-gray-500 hover:bg-gray-50"
          }`}
        >
          <FiUsers />
          <span>Subscribed</span>
        </button>
      </div>

      <div className="flex-1 overflow-y-auto">
        {activeTab === "conversations" ? (
          isLoading ? (
            <div className="flex flex-col space-y-4 p-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-gray-200 animate-pulse" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          ) : filteredConversations.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500 p-4">
              <FiMessageSquare className="text-4xl mb-2" />
              <p className="text-center">
                No conversations yet. Start one with a subscribed creator!
              </p>
            </div>
          ) : (
            <div className="divide-y p-2">
              {filteredConversations.map((conv) => (
                <div
                  key={conv._id}
                  onClick={() => navigate(`/chats/chat/${conv._id}`)}
                  className={`p-4 cursor-pointer rounded-xl hover:bg-primary/10 transition-colors ${
                    activeConversation === conv._id ? "bg-primary/10" : ""
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Avatar
                      avatar={
                        conv.participants.find((p) => p._id !== user._id)
                          ?.avatar
                      }
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center">
                        <span className="font-semibold truncate">
                          {
                            conv.participants.find((p) => p._id !== user._id)
                              ?.username
                          }
                        </span>
                        {data.unreadCounts[conv._id] > 0 && (
                          <span className="bg-primary text-white text-xs rounded-full px-2 py-1 ml-2">
                            {data.unreadCounts[conv._id]}
                          </span>
                        )}
                      </div>
                      {conv.lastMessage && (
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm text-gray-500 truncate max-w-[200px]">
                              {conv.lastMessage.content}
                            </p>
                            <div className="flex items-center space-x-1">
                              {conv.lastMessage.readBy?.length > 1 && (
                                <span className="text-blue-500">
                                  <BsCheckAll size={16} />
                                </span>
                              )}
                              <span className="text-xs text-gray-400">
                                {new Date(
                                  conv.lastMessage.createdAt
                                ).toLocaleTimeString([], {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )
        ) : (
          <div className="divide-y">
            {filteredCreators.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500 p-4">
                <FiUsers className="text-4xl mb-2" />
                <p className="text-center">No subscribed creators found</p>
              </div>
            ) : (
              filteredCreators.map((creator) => (
                <div
                  key={creator._id}
                  onClick={() => handleStartNewConversation(creator)}
                  className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    {creator.avatar ? (
                      <img
                        src={creator.avatar}
                        alt={creator.username}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                        {creator.username[0].toUpperCase()}
                      </div>
                    )}
                    <div>
                      <div className="font-semibold">{creator.username}</div>
                      {creator.displayName && (
                        <div className="text-sm text-gray-500">
                          {creator.displayName}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
}
