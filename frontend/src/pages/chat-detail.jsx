import { Icon } from "@iconify/react";
import { useState, useEffect, useRef } from "react";
import { useAuth } from "../hooks/useAuth";
import {
  FiSend,
  FiSearch,
  FiMessageSquare,
  FiUsers,
  FiCheck,
  FiArrowDown,
} from "react-icons/fi";
import { Link, useNavigate, useParams } from "react-router-dom";
import Avatar from "../components/Avatar";
import LoadingSpinner from "../components/LoadingSpinner";
import BackButton from "../components/BackButton";
import {
  useMessages,
  useConversations,
  useSendMessage,
} from "../services/chatQueries";
import { useSocket } from "../hooks/useSocket";

export default function ChatDetail() {
  const { id: conversationId } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [lastReadMessageId, setLastReadMessageId] = useState(null);

  // Use TanStack Query hooks
  const { data: conversationsData } = useConversations();
  const { data: messagesData, isLoading } = useMessages(conversationId);
  const sendMessageMutation = useSendMessage();

  // Set up socket connection
  useSocket(user?._id);

  const currentConversation = conversationsData?.conversations?.find(
    (conv) => conv._id === conversationId
  );

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!inputValue.trim() || !currentConversation) return;

    const receiverId = currentConversation.participants.find(
      (p) => p._id !== user._id
    )?._id;

    try {
      await sendMessageMutation.mutateAsync({
        receiverId,
        content: inputValue.trim(),
        conversationId: currentConversation._id,
      });
      setInputValue("");
      scrollToBottom();
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messagesData?.messages]);

  const formatMessageDate = (date) => {
    const messageDate = new Date(date);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (messageDate.toDateString() === today.toDateString()) {
      return "Today";
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return messageDate.toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      });
    }
  };

  // Add scroll handler to show/hide scroll button
  const handleScroll = (e) => {
    const bottom =
      e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    setShowScrollButton(!bottom);
  };

  const findFirstUnreadMessageIndex = (messages) => {
    return messages.findIndex(
      (message) =>
        message.sender._id !== user._id &&
        (!message.readBy ||
          !message.readBy.some((reader) => reader._id === user._id))
    );
  };

  // Modify the groupMessagesByDate function to handle unread messages
  const groupMessagesByDate = (messages) => {
    const groups = {};
    let hasUnreadMessages = false;
    const firstUnreadIndex = findFirstUnreadMessageIndex(messages);

    messages.forEach((message, index) => {
      const date = new Date(message.createdAt).toDateString();
      if (!groups[date]) {
        groups[date] = {
          messages: [],
          hasUnread: false,
        };
      }

      // Check if this is the first unread message in the group
      if (index === firstUnreadIndex) {
        groups[date].hasUnread = true;
        hasUnreadMessages = true;
      }

      groups[date].messages.push(message);
    });

    return { groups, hasUnreadMessages };
  };

  if (isLoading) {
    return (
      <div className="flex-1 h-full flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!currentConversation) {
    return (
      <div className="flex-1 h-full flex items-center justify-center text-gray-500">
        <div className="text-center">
          <FiMessageSquare className="mx-auto text-4xl mb-2" />
          <p>Conversation not found</p>
          <button
            onClick={() => navigate("/chats")}
            className="mt-4 text-primary hover:underline"
          >
            Return to chats
          </button>
        </div>
      </div>
    );
  }

  const otherParticipant = currentConversation.participants.find(
    (p) => p._id !== user._id
  );

  return (
    <div className="flex-1 h-full">
      <div className="h-full bg-white flex flex-col">
        <div className="flex sticky top-0 w-full bg-white z-10 justify-between items-center p-4 border-b">
          <div className="flex items-center gap-3">
            <BackButton />
            <Link
              to={`/${otherParticipant?.username}`}
              className="flex items-center gap-3"
            >
              <Avatar avatar={otherParticipant?.avatar} className="w-10 h-10" />
              <div>
                <h2 className="text-lg font-semibold">
                  @{otherParticipant?.username}
                </h2>
                {otherParticipant?.displayName && (
                  <p className="text-sm text-gray-500">
                    {otherParticipant.displayName}
                  </p>
                )}
              </div>
            </Link>
          </div>
          <button className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100">
            <Icon icon="ri:more-fill" className="text-xl" />
          </button>
        </div>
        <div
          className="flex-1 overflow-y-auto p-4 space-y-4"
          onScroll={handleScroll}
        >
          {(() => {
            const { groups, hasUnreadMessages } = groupMessagesByDate(
              messagesData?.messages
            );
            return Object.entries(groups).map(
              ([date, { messages: dateMessages, hasUnread }]) => (
                <div key={date} className="space-y-4">
                  <div className="relative">
                    <div className="border-b" />
                    <div className="flex items-center justify-center absolute top-[-11px] left-0 w-full">
                      <div className="bg-white text-gray-600 text-xs px-3 py-1 rounded-full">
                        {formatMessageDate(date)}
                      </div>
                    </div>
                  </div>
                  {dateMessages.map((message, index) => (
                    <>
                      {hasUnread && index === 0 && (
                        <div className="relative my-4">
                          <div className="border-b border-red-400" />
                          <div className="flex items-center justify-center absolute top-[-11px] left-0 w-full">
                            <div className="bg-white text-red-500 text-xs px-3 py-1 rounded-full border border-red-400">
                              Unread messages
                            </div>
                          </div>
                        </div>
                      )}
                      <div
                        key={message._id || index}
                        className={`flex ${
                          message.sender._id === user._id
                            ? "justify-end"
                            : "justify-start"
                        }`}
                      >
                        <div
                          className={`max-w-[70%] rounded-lg p-3 ${
                            message.sender._id === user._id
                              ? "bg-primary text-white"
                              : message.readBy?.some(
                                  (reader) => reader._id === user._id
                                )
                              ? "bg-gray-100"
                              : "bg-red-50"
                          }`}
                        >
                          <p className="break-words">{message.content}</p>
                          <div className="flex items-center justify-end gap-1 mt-1">
                            <span className="text-xs opacity-70">
                              {new Date(message.createdAt).toLocaleTimeString(
                                [],
                                {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                }
                              )}
                            </span>
                            {message.sender._id === user._id && (
                              <div className="flex">
                                <FiCheck
                                  className={`${
                                    message.readBy?.some(
                                      (reader) => reader._id !== user._id
                                    )
                                      ? "text-blue-400"
                                      : "opacity-70"
                                  } w-3 h-3`}
                                />
                                <FiCheck
                                  className={`-ml-1 ${
                                    message.readBy?.some(
                                      (reader) => reader._id !== user._id
                                    )
                                      ? "text-blue-400"
                                      : "opacity-70"
                                  } w-3 h-3`}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </>
                  ))}
                </div>
              )
            );
          })()}
          <div ref={messagesEndRef} />
        </div>
        {showScrollButton && (
          <button
            onClick={scrollToBottom}
            className="fixed bottom-24 right-8 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary/90 transition-colors"
          >
            <FiArrowDown className="w-5 h-5" />
          </button>
        )}
        <form onSubmit={handleSendMessage} className="p-4 border-t">
          <div className="flex items-center gap-2 bg-gray-50 rounded-lg p-2">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 bg-transparent p-2 focus:outline-none"
            />
            <button
              type="submit"
              disabled={!inputValue.trim() || sendMessageMutation.isPending}
              className="p-2 rounded-lg bg-primary text-white hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiSend className="text-xl" />
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
