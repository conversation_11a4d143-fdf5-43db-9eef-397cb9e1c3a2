import { useEffect, useRef, useCallback, useState } from "react";
import useNotificationStore, {
  useNotificationsQuery,
  useMarkNotificationAsRead,
} from "../store/notificationStore";
import { Icon } from "@iconify/react";
import { fDateTime } from "../utils/formatTime";
import Pagination from "../components/Pagination";
import _ from "lodash";
import { Link } from "react-router-dom";
import LoadingSpinner from "../components/LoadingSpinner";

export default function Notifications() {
  const { filters, setFilter, currentPage, setPage } = useNotificationStore();
  const { data, isLoading } = useNotificationsQuery(currentPage, filters);
  const markAsRead = useMarkNotificationAsRead();

  const observerRef = useRef(null);
  const notificationRefs = useRef({});
  const pendingReads = useRef(new Set());
  const [newNotifications, setNewNotifications] = useState(new Set());

  const notifications = data?.notifications || [];
  const totalPages = data?.totalPages || 1;

  // Function to batch mark notifications as read
  const batchMarkAsRead = useCallback(async () => {
    if (pendingReads.current.size === 0) return;

    const notificationsToMark = Array.from(pendingReads.current);
    pendingReads.current.clear();

    for (const notificationId of notificationsToMark) {
      try {
        await markAsRead(notificationId);
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    }
  }, [markAsRead]);

  // Initialize new notifications on first load
  useEffect(() => {
    if (notifications) {
      const unreadIds = notifications.filter((n) => !n.read).map((n) => n._id);
      setNewNotifications(new Set(unreadIds));
    }
  }, [notifications]);

  // Setup intersection observer
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const notificationId = entry.target.dataset.notificationId;
            const notification = notifications?.find(
              (n) => n._id === notificationId
            );

            if (notification && !notification.read) {
              pendingReads.current.add(notificationId);
              // Remove from new notifications after 2 seconds of being visible
              setTimeout(() => {
                setNewNotifications((prev) => {
                  const updated = new Set(prev);
                  updated.delete(notificationId);
                  return updated;
                });
              }, 2000);
            }
          }
        });
      },
      { threshold: 0.5 }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [notifications]);

  // Observe new notifications
  useEffect(() => {
    if (!notifications) return;

    notifications.forEach((notification) => {
      const element = notificationRefs.current[notification._id];
      if (element && observerRef.current) {
        observerRef.current.observe(element);
      }
    });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [notifications]);

  // Mark notifications as read when leaving the page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        batchMarkAsRead();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("beforeunload", batchMarkAsRead);

    return () => {
      batchMarkAsRead();
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("beforeunload", batchMarkAsRead);
    };
  }, [batchMarkAsRead]);

  if (isLoading) {
    return <LoadingSpinner size="xl" />;
  }

  const renderFilterControls = () => (
    <div className="mb-6 flex gap-4">
      <button
        onClick={() => setFilter("status", "all")}
        className={`px-3 py-2 border rounded-lg text-sm transition-colors ${
          filters.status === "all"
            ? "bg-primary text-white border-primary"
            : "bg-white text-gray-500 hover:bg-gray-50"
        }`}
      >
        All
      </button>
      <button
        onClick={() => setFilter("status", "read")}
        className={`px-3 py-2 border rounded-lg text-sm transition-colors ${
          filters.status === "read"
            ? "bg-primary text-white border-primary"
            : "bg-white text-gray-500 hover:bg-gray-50"
        }`}
      >
        Read
      </button>
      <button
        onClick={() => setFilter("status", "unread")}
        className={`px-3 py-2 border rounded-lg text-sm transition-colors ${
          filters.status === "unread"
            ? "bg-primary text-white border-primary"
            : "bg-white text-gray-500 hover:bg-gray-50"
        }`}
      >
        Unread
      </button>

      {/* <select
        value={filters.type}
        onChange={(e) => setFilter("type", e.target.value)}
        className="px-3 py-2 border rounded-lg bg-white text-sm text-gray-500"
      >
        <option value="all">All Types</option>
        <option value="LIKE">Likes</option>
        <option value="COMMENT">Comments</option>
        <option value="FOLLOW">Follows</option>
      </select> */}
    </div>
  );

  return (
    <div className="p-4">
      <div className="mb-5">
        <h3 className="text-xl font-medium mb-2">
          Notifications ({data?.totalUnread || 0})
        </h3>
        {renderFilterControls()}
      </div>

      {notifications?.length === 0 ? (
        <p className="text-center text-gray-500">No notifications yet</p>
      ) : (
        <div className="space-y-2">
          {notifications?.map((notification) => {
            const isNew = newNotifications.has(notification._id);
            return (
              <div
                key={notification._id}
                ref={(el) => (notificationRefs.current[notification._id] = el)}
                data-notification-id={notification._id}
                className={`p-3 rounded-xl border relative transition-all duration-300 hover:shadow-md ${
                  notification.read ? "bg-gray-50" : "bg-white"
                } ${isNew ? "animate-pulse-subtle border-primary-200" : ""}`}
              >
                <div className="flex items-center gap-3">
                  {notification?.sender?.avatar ? (
                    <img
                      src={notification.sender.avatar}
                      alt="Profile"
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                      <Icon fontSize={24} icon="ph:user" />
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p className="">
                        <span className="text-primary">
                          @{notification.sender.username}
                        </span>{" "}
                        {notification.type === "LIKE" && (
                          <div>
                            <span className="text-gray-500">
                              liked your post{" "}
                            </span>
                            <Link
                              to={`/post/${notification.post._id}`}
                              className="text-secondary"
                            >
                              {_.truncate(notification.post.content, {
                                length: 50,
                              })}
                            </Link>
                          </div>
                        )}
                        {notification.type === "COMMENT" && (
                          <div>
                            <span className="text-gray-500">
                              commented on your post
                            </span>{" "}
                            <Link
                              to={`/post/${notification.post._id}`}
                              className="text-secondary"
                            >
                              {_.truncate(notification.post.content, {
                                length: 50,
                              })}
                            </Link>
                          </div>
                        )}
                        {notification.type === "FOLLOW" && "followed you"}
                        {notification.type === "COMMENT_LIKE" && (
                          <div>
                            <span className="text-gray-500">
                              liked your comment in{" "}
                            </span>
                            <Link
                              to={`/post/${notification.post._id}`}
                              className="text-secondary"
                            >
                              {_.truncate(notification.post.content, {
                                length: 50,
                              })}
                            </Link>
                          </div>
                        )}
                        {notification.type === "MENTION" && (
                          <div>
                            <span className="text-gray-500">
                              mentioned you in a post
                            </span>{" "}
                            <span className="">
                              {_.truncate(notification.post.content, {
                                length: 50,
                              })}
                            </span>
                          </div>
                        )}
                        {notification.type === "REPLY" && (
                          <div>
                            <span className="text-gray-500">
                              replied to your comment in{" "}
                            </span>
                            <span className="">
                              {_.truncate(notification.post.content, {
                                length: 50,
                              })}
                            </span>
                          </div>
                        )}
                        {notification.type === "SUBSCRIBE" && (
                          <div>
                            <span className="text-gray-500">
                              subscribed to your content
                            </span>{" "}
                          </div>
                        )}
                        {notification.type === "TIP" && (
                          <div>
                            <span className="text-gray-500">
                              {notification.message}
                            </span>{" "}
                          </div>
                        )}
                      </p>
                      {isNew && (
                        <span className="px-2 py-0.5 bg-primary-100 text-primary text-xs font-medium rounded-full">
                          New
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">
                      {notification?.createdAt &&
                        fDateTime(notification?.createdAt)}
                    </p>
                  </div>
                </div>
                {!notification.read && (
                  <div className="absolute bg-primary w-2 h-2 rounded-full top-1/2 -translate-y-1/2 right-5"></div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {notifications?.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setPage(page)}
        />
      )}
    </div>
  );
}
