import { useState, useEffect } from "react";
import { FiUser } from "react-icons/fi";
import { useQuery } from "@tanstack/react-query";
import { subscriptionService } from "../../services/api";
import Pagination from "../../components/Pagination";
import Table from "../../components/Table";
import { fDateTime } from "../../utils/formatTime";

export default function UserSubscriptions() {
  const [activeTab, setActiveTab] = useState("active");
  const [currentPage, setCurrentPage] = useState(1);

  const tabs = [
    { id: "active", label: "Active" },
    { id: "expired", label: "Expired" },
    // { id: "all", label: "All" },
  ];

  const columns = [
    { name: "Creator", accessor: "username" },
    { name: "Amount", accessor: "amount" },
    { name: "Duration", accessor: "duration" },
    { name: "Start Date", accessor: "startDate" },
    { name: "End Date", accessor: "endDate" },
    { name: "Status", accessor: "status" },
  ];

  // Reset to first page when tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab]);

  const { data, isLoading, error } = useQuery({
    queryKey: ["my-subscriptions", activeTab, currentPage],
    queryFn: async () => {
      const response = await subscriptionService.getMySubscriptions({
        ...(activeTab !== "all" && { status: activeTab }),
        page: currentPage,
        limit: 10,
      });

      // Transform the data for table display
      const transformedData = response.subscriptions.map((sub) => ({
        username: `@${sub.creator.username}`,
        amount: sub.planSnapshot.amount,
        duration: `${sub.planSnapshot.noOfMonths} ${
          sub.planSnapshot.noOfMonths > 1 ? "months" : "month"
        }`,
        startDate: fDateTime(sub.startDate),
        endDate: fDateTime(sub.endDate),
        status: sub.status.charAt(0).toUpperCase() + sub.status.slice(1),
      }));

      return {
        subscribers: transformedData,
        totalPages: response.totalPages,
      };
    },
    keepPreviousData: true,
  });

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  return (
    <div className="flex flex-col w-full h-full p-6 bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-medium text-gray-900 mb-6">
          Subscriptions
        </h1>
      </div>

      {/* Tabs Navigation */}
      <div className="flex space-x-2 border-b border-gray-200 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-all duration-200 relative
              ${
                activeTab === tab.id
                  ? "text-primary border-b-2 border-primary"
                  : "text-gray-500 hover:text-primary-400"
              }
              before:absolute before:bottom-0 before:left-0 before:w-full before:h-0.5 before:transition-all
              hover:before:bg-primary-100`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="flex-1 bg-white rounded-lg min-w-0">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-10 w-10 border-4 border-primary border-t-transparent"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 p-6 bg-red-50 rounded-lg">
            <p className="font-medium">
              {error.message || "Failed to fetch subscribers"}
            </p>
          </div>
        ) : !data?.subscribers?.length ? (
          <div className="flex flex-col justify-center items-center h-64 bg-gray-50 rounded-lg">
            <FiUser className="w-16 h-16 text-gray-400 mb-4" />
            <p className="text-center text-gray-600 font-medium">
              You don&apos;t have
              {activeTab === "all" && "any"} subscriptions yet.
            </p>
            <p className="text-center text-gray-400 text-sm mt-2">
              Your subscriptions will appear here once you subscribe to a
              creator.
            </p>
          </div>
        ) : (
          <div className="flex flex-col min-w-0">
            <div className="mb-6">
              <Table columns={columns} data={data.subscribers} />
            </div>
            <div className="flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={data.totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
