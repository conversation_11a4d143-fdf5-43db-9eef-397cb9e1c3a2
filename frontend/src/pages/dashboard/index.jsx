import { Link } from "react-router-dom";
import React from "react";
import { useAuth } from "../../hooks/useAuth";
import { Icon } from "@iconify/react";
import DashboardHeader from "../../components/dashboard/DashboardHeader";
import usePostStore from "../../store/postStore";
import CreatePost, {
  CreatePostModal,
} from "../../components/dashboard/CreatePost";
import PostFeed from "../../components/PostFeed";
import Stories from "../../components/Stories";
import { postService } from "../../services/api";
import { useQuery } from "@tanstack/react-query";

export default function Dashboard() {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const { user } = useAuth();

  const { data: post, isLoading } = useQuery({
    queryKey: ["posts"],
    queryFn: async () => {
      const result = await postService.getSubscribedPosts();
      return result;
    },
  });

  console.log("POSTS", post);

  const renderSectionHeader = (title, description) => (
    <div className="mb-6 p-4 bg-gradient-to-r from-primary/10 to-pink-100 rounded-xl">
      <h2 className="text-lg font-semibold text-gray-800 mb-2">{title}</h2>
      <p className="text-gray-600">{description}</p>
    </div>
  );

  return (
    <div>
      <div className="max-w-4xl mx-auto px-4">
        <div className="space-y-6">
          {/* Header */}
          <Stories />

          {/* Main Content */}
          <div className="space-y-8">
            {/* Subscribed Posts Section */}
            {post?.hasSubscriptions && post?.subscribedPosts?.length > 0 && (
              <div>
                {renderSectionHeader(
                  "Subscribed Creators",
                  "Latest posts from creators you subscribed to"
                )}
                <PostFeed
                  posts={post?.subscribedPosts}
                  isSuggestedFeed={false}
                />
              </div>
            )}

            {/* Suggested Posts Section */}
            {post?.suggestedPosts?.length > 0 && (
              <div>
                {renderSectionHeader(
                  "Discover Creators",
                  "Explore popular posts from our community"
                )}
                <PostFeed posts={post?.suggestedPosts} isSuggestedFeed={true} />
              </div>
            )}

            {/* No Posts Message */}
            {!isLoading &&
              !post?.subscribedPosts?.length &&
              !post?.suggestedPosts?.length && (
                <div className="text-center py-12">
                  <p className="text-gray-500">
                    No posts available at the moment
                  </p>
                </div>
              )}
          </div>
        </div>
      </div>

      {isModalOpen && (
        <CreatePostModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} />
      )}
    </div>
  );
}
