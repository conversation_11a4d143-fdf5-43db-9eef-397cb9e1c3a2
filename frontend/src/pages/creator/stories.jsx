import React, { useState, useEffect } from "react";
import { BsPlusLg } from "react-icons/bs";
import { AiOutlineQuestionCircle } from "react-icons/ai";
import { FaEye } from "react-icons/fa";
import StoryViewer from "../../components/stories/StoryViewer";
import StoryUploader from "../../components/stories/StoryUploader";
import useStoryStore from "../../store/storyStore";
import { useAuth } from "../../hooks/useAuth";
import LoadingSpinner from "../../components/LoadingSpinner";
import { IoCloseOutline } from "react-icons/io5";
import Avatar from "../../components/Avatar";

export default function Stories() {
  const { stories, isLoading, error, loadUserStories } = useStoryStore();
  const [showUploader, setShowUploader] = useState(false);
  const [selectedStoryIndex, setSelectedStoryIndex] = useState(null);
  const [showViewers, setShowViewers] = useState(false);
  const [selectedStory, setSelectedStory] = useState(null);
  const { user } = useAuth();

  const handleStoryClick = (index) => {
    setSelectedStoryIndex(index);
  };

  const handleViewersClick = (e, story) => {
    e.stopPropagation();
    setSelectedStory(story);
    setShowViewers(true);
  };

  const handleNextStory = () => {
    if (selectedStoryIndex < stories.length - 1) {
      setSelectedStoryIndex(selectedStoryIndex + 1);
    } else {
      setSelectedStoryIndex(null);
    }
  };

  const handlePreviousStory = () => {
    if (selectedStoryIndex > 0) {
      setSelectedStoryIndex(selectedStoryIndex - 1);
    }
  };

  useEffect(() => {
    loadUserStories(user._id);
  }, []);

  const totalViews = stories?.reduce(
    (acc, story) => acc + (story.viewers?.length || 0),
    0
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold">Stories</h1>
          <div className="flex items-center space-x-2 text-gray-600">
            <FaEye className="text-lg" />
            <span className="font-medium">{totalViews} total views</span>
          </div>
          <button className="text-gray-500 hover:text-gray-700">
            <AiOutlineQuestionCircle size={20} />
          </button>
        </div>
        <button
          onClick={() => setShowUploader(true)}
          className="flex items-center space-x-2 bg-secondary text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
        >
          <BsPlusLg />
          <span>Add to story</span>
        </button>
      </div>

      {isLoading ? (
        <LoadingSpinner size="xl" />
      ) : stories.length === 0 ? (
        <div className="text-center py-12">
          <div className="bg-gray-100 rounded-full w-24 h-24 mx-auto mb-4 flex items-center justify-center">
            <BsPlusLg size={32} className="text-gray-400" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Create a Story</h2>
          <p className="text-gray-500 mb-4">
            Share photos and videos that will disappear after 24 hours.
          </p>
          <button
            onClick={() => setShowUploader(true)}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Story
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {/* Add story button */}
          <div
            onClick={() => setShowUploader(true)}
            className="relative aspect-[9/16] bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
          >
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mb-2">
                <BsPlusLg className="text-white text-xl" />
              </div>
              <span className="text-sm font-medium">Create Story</span>
            </div>
          </div>

          {/* Story previews */}
          {stories?.length > 0 &&
            stories.map((story, index) => (
              <div
                key={story._id}
                onClick={() => handleStoryClick(index)}
                className="relative aspect-[9/16] rounded-lg overflow-hidden cursor-pointer group"
              >
                {story.mediaType === "image" ? (
                  <img
                    src={story.mediaUrl}
                    alt={`Story by ${story.creator.username}`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <video
                    src={story.mediaUrl}
                    className="w-full h-full object-cover"
                    muted
                  />
                )}
                <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-transparent">
                  <div className="p-4">
                    {story.creator.avatar ? (
                      <img
                        src={story.creator.avatar}
                        alt={story.creator.username}
                        className="w-10 h-10 rounded-full border-2 border-blue-600"
                      />
                    ) : (
                      <Avatar
                        className={"h-10 w-10 border-2 border-blue-600"}
                      />
                    )}

                    <p className="text-white text-sm mt-1">
                      {story.creator.username}
                    </p>
                    <button
                      onClick={(e) => handleViewersClick(e, story)}
                      className="flex items-center space-x-1 text-white text-sm mt-2 bg-black/30 px-2 py-1 rounded-full"
                    >
                      <FaEye />
                      <span>{story.viewers?.length || 0} views</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}

      {/* Story viewer modal */}
      {selectedStoryIndex !== null && (
        <StoryViewer
          stories={stories}
          currentIndex={selectedStoryIndex}
          onClose={() => setSelectedStoryIndex(null)}
          onNext={handleNextStory}
          onPrevious={handlePreviousStory}
        />
      )}

      {/* Story uploader modal */}
      {showUploader && (
        <StoryUploader
          onClose={() => setShowUploader(false)}
          onSuccess={() => {
            setShowUploader(false);
            loadUserStories(user._id);
          }}
        />
      )}

      {/* Viewers Modal */}
      {showViewers && selectedStory && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Story Viewers</h3>
              <button
                onClick={() => setShowViewers(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <IoCloseOutline size={24} />
              </button>
            </div>
            <div className="max-h-96 overflow-y-auto">
              {selectedStory.viewers?.length > 0 ? (
                selectedStory.viewers.map((viewer) => (
                  <div
                    key={viewer._id}
                    className="flex items-center space-x-3 py-2 border-b last:border-b-0"
                  >
                    <img
                      src={viewer.user.avatar}
                      alt={viewer.user.username}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <p className="font-medium">{viewer.user.username}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(viewer.viewedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-gray-500 py-4">No viewers yet</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
