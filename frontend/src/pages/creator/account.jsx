import { useNavigate } from "react-router-dom";
import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useAuth } from "../../hooks/useAuth";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import InteractiveButton from "../../components/InteractiveButton";
import { authService } from "../../services/api";
import { set } from "lodash";
import { uploadService } from "../../services/api";
import debounce from "lodash/debounce";
import BackButton from "../../components/BackButton";

export default function Account() {
  const navigate = useNavigate();
  const { user, updateProfile } = useAuth();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [coverImage, setCoverImage] = useState(null);
  const [coverImagePreview, setCoverImagePreview] = useState(
    user?.coverImage || null
  );
  const [avatar, setAvatar] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(user?.avatar || null);
  const [usernameError, setUsernameError] = useState("");
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [usernameSuccess, setUsernameSuccess] = useState(false);

  const { register, handleSubmit, watch, setValue } = useForm({
    defaultValues: {
      username: user?.username || "",
      displayName: user?.displayName || "",
      about: user?.about || "",
      location: user?.location || "",
      websiteUrl: user?.websiteUrl || "",
      instagram: user?.socialLinks?.instagram || "",
      facebook: user?.socialLinks?.facebook || "",
      twitter: user?.socialLinks?.twitter || "",
      youtube: user?.socialLinks?.youtube || "",
      avatar: user?.avatar || "",
      coverImage: user?.coverImage || "",
      email: user?.email || "",
    },
  });

  const watchedFields = watch();
  // console.log(user);

  // Debounced username check
  const checkUsername = debounce(async (username) => {
    if (!username || username === user?.username) {
      setUsernameError("");
      setUsernameSuccess(false);
      setIsCheckingUsername(false);
      return;
    }

    setIsCheckingUsername(true);
    setUsernameSuccess(false);
    try {
      const response = await authService.checkUsername(username);
      if (!response.data.available) {
        setUsernameError(response.data.message);
        setUsernameSuccess(false);
      } else {
        setUsernameError("");
        setUsernameSuccess(true);
      }
    } catch (error) {
      if (error.response?.status === 401) {
        setUsernameError("Please log in to check username availability");
      } else {
        setUsernameError(
          error.response?.data?.message ||
            "Error checking username availability"
        );
      }
      setUsernameSuccess(false);
    }
    setIsCheckingUsername(false);
  }, 500);

  // Watch username changes
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === "username") {
        checkUsername(value.username);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  const onSubmit = async (data) => {
    if (usernameError || isCheckingUsername) {
      toast.error("Please fix the username error before submitting");
      return;
    }

    setSubmitLoading(true);
    let avatarUrl = null;
    let coverImageUrl = null;
    if (coverImage) {
      const formData = new FormData();
      formData.append("file", coverImage);
      const coverImageResponse = await uploadService.uploadSingle(formData);
      coverImageUrl = coverImageResponse.data.secure_url;
    }
    if (avatar) {
      const formData = new FormData();
      formData.append("file", avatar);
      const avatarResponse = await uploadService.uploadSingle(formData);
      avatarUrl = avatarResponse.data.secure_url || avatarResponse.data.url;
    }
    await updateProfile({
      about: data.about,
      socialLinks: {
        instagram: data.instagram,
        facebook: data.facebook,
        twitter: data.twitter,
        youtube: data.youtube,
      },
      displayName: data.displayName,
      location: data.location,
      websiteUrl: data.websiteUrl,
      avatar: avatarUrl || user?.avatar,
      coverImage: coverImageUrl || user?.coverImage,
      username: data.username,
    })
      .then((res) => {
        toast.success("Profile updated successfully");
        setSubmitLoading(false);
      })
      .catch((err) => {
        console.log(err);
        setSubmitLoading(false);
        if (err?.response?.data?.message) {
          toast.error(err.response.data.message);
        } else {
          toast.error("Something went wrong");
        }
      });
  };

  const handleCoverImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setCoverImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setCoverImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAvatar(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-4">
          <BackButton />
          <h1 className=" font-semibold">Edit Profile</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Cover Image */}
        <div className="relative h-48 bg-gray-200 rounded">
          {coverImagePreview && (
            <img
              src={coverImagePreview}
              alt="Cover"
              className="w-full h-full object-cover rounded"
            />
          )}
          <label className="absolute inset-0 cursor-pointer flex items-center justify-center hover:bg-black/20 transition-colors">
            <input
              onChange={handleCoverImageChange}
              type="file"
              accept="image/*"
              className="hidden"
            />
            <div className="p-2 bg-black/50 rounded-full text-white">
              <Icon
                icon="ph:camera-light"
                fontSize={24}
                className="text-white"
              />
            </div>
          </label>
        </div>

        {/* Profile Image */}
        <div className="relative mb-16">
          <div className="absolute -bottom-12 left-4">
            <div className="relative">
              {avatarPreview ? (
                <img
                  src={avatarPreview}
                  alt="Profile"
                  className="w-20 h-20 rounded-full object-cover border-4 border-white"
                />
              ) : (
                <div className="w-20 h-20 border-4 border-white rounded-full bg-gray-200 flex items-center justify-center">
                  <Icon icon="ph:user" fontSize={40} className="text-white" />
                </div>
              )}
              <label className="absolute inset-0 flex items-center justify-center cursor-pointer bg-black/30 rounded-full opacity-0 hover:opacity-100 transition-opacity">
                <input
                  onChange={handleAvatarChange}
                  type="file"
                  accept="image/*"
                  className="hidden"
                />
                <Icon
                  icon="ph:camera-light"
                  fontSize={30}
                  className="text-white"
                />
              </label>
            </div>
            {/* <div className="relative w-24 h-24 rounded-full border-4 border-white bg-gray-200">
              <img
                src="/path-to-profile-image.jpg"
                alt="Profile"
                className="w-full h-full rounded-full object-cover"
              />
            </div> */}
          </div>
        </div>

        {/* Form Fields */}
        <div className="px-4 py-8 space-y-6">
          <div className="space-y-2">
            <div className="relative">
              <input
                type="text"
                name="username"
                {...register("username")}
                placeholder="Username"
                className={`w-full px-4 py-2 border ${
                  usernameError
                    ? "border-red-500"
                    : usernameSuccess
                    ? "border-green-500"
                    : "border-gray-300"
                } rounded-md focus:outline-none focus:ring-2 ${
                  usernameError
                    ? "focus:ring-red-500"
                    : usernameSuccess
                    ? "focus:ring-green-500"
                    : "focus:ring-blue-500"
                } disabled:bg-gray-100 disabled:text-gray-400 focus:ring-primary-500`}
              />
              {isCheckingUsername && (
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  <Icon
                    icon="eos-icons:loading"
                    className="text-gray-400 animate-spin"
                  />
                </div>
              )}
              {usernameError && (
                <p className="mt-1 text-sm text-red-500">{usernameError}</p>
              )}
              {usernameSuccess && !usernameError && !isCheckingUsername && (
                <p className="mt-1 text-sm text-green-500">
                  Username is available!
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="relative">
              <input
                type="text"
                name="displayName"
                {...register("displayName")}
                placeholder="Display name"
                maxLength={40}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <span className="absolute right-2 bottom-2 text-sm text-gray-500">
                {watchedFields.displayName?.length || 0}/40
              </span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="relative">
              <input
                type="email"
                name="email"
                {...register("email")}
                placeholder="Email"
                disabled
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-100 disabled:text-gray-400"
              />
            </div>
          </div>

          <div className="space-y-2">
            <div className="relative">
              <textarea
                name="about"
                {...register("about")}
                placeholder="about"
                rows={4}
                maxLength={1000}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <span className="absolute right-2 bottom-2 text-sm text-gray-500">
                {watchedFields.about?.length || 0}/1000
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="relative">
              <input
                type="text"
                name="location"
                {...register("location")}
                placeholder="Location"
                maxLength={64}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <span className="absolute right-2 bottom-2 text-sm text-gray-500">
                {watchedFields.location?.length || 0}/64
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="relative">
              <input
                type="url"
                name="websiteUrl"
                {...register("websiteUrl")}
                placeholder="Website URL"
                maxLength={100}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <span className="absolute right-2 bottom-2 text-sm text-gray-500">
                {watchedFields.websiteUrl?.length || 0}/100
              </span>
            </div>
          </div>

          {/* Social Media Links Section */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Social links</h2>

            {/* Instagram */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <div className="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center">
                <Icon icon="mdi:instagram" className="text-white text-xl" />
              </div>
              <div className="flex-1">
                <div className="flex items-center">
                  <span className="text-gray-400">@</span>
                  <input
                    type="text"
                    {...register("instagram")}
                    placeholder="Enter instagram username"
                    className="flex-1 px-2 py-1 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Facebook */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <Icon icon="mdi:facebook" className="text-white text-xl" />
              </div>
              <div className="flex-1">
                <div className="flex items-center">
                  <span className="text-gray-400">@</span>
                  <input
                    type="text"
                    {...register("facebook")}
                    placeholder="Enter facebook username"
                    className="flex-1 px-2 py-1 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Twitter */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <div className="w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center">
                <Icon icon="mdi:twitter" className="text-white text-xl" />
              </div>
              <div className="flex-1">
                <div className="flex items-center">
                  <span className="text-gray-400">@</span>
                  <input
                    type="text"
                    {...register("twitter")}
                    placeholder="Enter twitter username"
                    className="flex-1 px-2 py-1 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* YouTube */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                <Icon icon="mdi:youtube" className="text-white text-xl" />
              </div>
              <div className="flex-1">
                <div className="flex items-center">
                  <span className="text-gray-400">@</span>
                  <input
                    type="text"
                    {...register("youtube")}
                    placeholder="Enter youtube username"
                    className="flex-1 px-2 py-1 focus:outline-none"
                  />
                </div>
              </div>
            </div>
          </div>
          <InteractiveButton
            isLoading={submitLoading}
            className=""
            type="submit"
            variant="primary"
          >
            Update
          </InteractiveButton>
        </div>
      </form>
    </div>
  );
}
