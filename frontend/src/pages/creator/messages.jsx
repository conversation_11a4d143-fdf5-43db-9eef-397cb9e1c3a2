import { useState, useEffect } from "react";
import { IoArrowBack } from "react-icons/io5";
import CreatorChats from "../../components/CreatorMessages/CreatorChats";
import CreatorAutomation from "../../components/CreatorMessages/CreatorAutomation";
import CreateAutomation from "../../components/CreatorMessages/CreateAutomation";
import CreatorChatArea from "../../components/CreatorMessages/CreatorChatArea";
import AccountSetupStatus from "../../components/AccountSetupStatus";
import { useAuth } from "../../hooks/useAuth";
import { useSocket } from "../../hooks/useSocket";

export default function CreatorMessages() {
  const [activeTab, setActiveTab] = useState("chats");
  const [showCreateAutomation, setShowCreateAutomation] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [editingAutomation, setEditingAutomation] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [showMobileChat, setShowMobileChat] = useState(false);
  const { user } = useAuth();

  // Set up socket connection
  useSocket(user?._id);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setShowMobileChat(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const tabs = [
    { id: "chats", label: "Chats" },
    // { id: "massDms", label: "Mass DMs" },
    { id: "automations", label: "Automations", comingSoon: true },
  ];

  const handleChatSelect = (conversation) => {
    setSelectedConversation(conversation);

    // On mobile, show the chat area when a conversation is selected
    if (isMobile) {
      setShowMobileChat(true);
    }
  };

  const handleBackToList = () => {
    setShowMobileChat(false);
  };

  const renderContent = () => {
    switch (activeTab) {
      case "chats":
        return (
          <div className="flex h-full">
            {/* Conversation list - hidden on mobile when a chat is selected */}
            <div
              className={`md:w-[350px] w-full border-r border-gray-200 ${
                isMobile && showMobileChat ? "hidden" : "block"
              }`}
            >
              <CreatorChats onSelectChat={handleChatSelect} />
            </div>

            {/* Chat area - full width on mobile, flex-1 on desktop */}
            <div
              className={`${
                isMobile
                  ? showMobileChat
                    ? "block w-full"
                    : "hidden"
                  : "flex-1"
              }`}
            >
              {isMobile && showMobileChat && (
                <div className="p-2 border-b">
                  <button
                    onClick={handleBackToList}
                    className="flex items-center text-gray-600 hover:text-gray-900"
                  >
                    <IoArrowBack className="mr-1" /> Back to conversations
                  </button>
                </div>
              )}
              <CreatorChatArea conversation={selectedConversation} />
            </div>
          </div>
        );
      // case "massDms":
      //   return <CreatorMassDMs />;
      case "automations":
        return (
          <div className="flex h-full">
            <div
              className={`md:max-w-[350px] w-full border-r lg:flex h-full justify-center items-center border-gray-200 ${
                showCreateAutomation ? "hidden md:flex" : "flex"
              }`}
            >
              <CreatorAutomation
                onCreateClick={() => {
                  setShowCreateAutomation(true);
                  setEditingAutomation(null);
                }}
                onEditClick={(automation) => {
                  setEditingAutomation(automation);
                  setShowCreateAutomation(true);
                }}
              />
            </div>
            <div
              className={`flex-1 h-full ${
                showCreateAutomation ? "block" : "md:hidden block"
              }`}
            >
              <CreateAutomation
                onClose={() => setShowCreateAutomation(false)}
                editMode={!!editingAutomation}
                automation={editingAutomation}
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col bg-white max-h-screen">
      <div className="py-2 border-b border-gray-200">
        <div className="flex space-x-1 p-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => !tab.comingSoon && setActiveTab(tab.id)}
              disabled={tab.comingSoon}
              className={`px-4 text-sm font-medium transition-colors relative ${
                activeTab === tab.id
                  ? "border-b-2 border-primary"
                  : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
              } ${tab.comingSoon ? "cursor-not-allowed opacity-50" : ""}`}
            >
              {tab.label}
              {tab.comingSoon && (
                <span className="absolute -top-1 -right-1 bg-gray-100 text-gray-600 text-[10px] px-1 rounded-full">
                  Soon
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      <div className="flex-1 overflow-hidden">{renderContent()}</div>
    </div>
  );
}
