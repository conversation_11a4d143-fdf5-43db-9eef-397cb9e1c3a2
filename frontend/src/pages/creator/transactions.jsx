import Table from "../../components/Table";
import { RiEmotionSadLine } from "react-icons/ri";
import { fCurrency } from "../../utils/formatNumber";
import { Icon } from "@iconify/react";
import { getTipDescription } from "../../utils/utils";
import useWalletStore from "../../store/walletStore";
import { useEffect, useState } from "react";
import InteractiveButton from "../../components/InteractiveButton";
import Modal from "../../components/Modal";
import { useAuth } from "../../hooks/useAuth";
import CustomInput from "../../components/CustomInput";
import { flutterwaveConfig } from "../../config/flutterwave";
import { useFlutterwave, closePaymentModal } from "flutterwave-react-v3";
import { transactionService, walletService } from "../../services/api";
import { AiOutlineInfoCircle } from "react-icons/ai";
import { useQuery } from "@tanstack/react-query";
import { Bs<PERSON>ashCoin, BsWallet2 } from "react-icons/bs";
import { MdPendingActions } from "react-icons/md";

const QUICK_AMOUNTS = [20000, 40000, 80000, 120000, 140000, 160000];
const PERIOD_OPTIONS = [
  { label: "All Time", value: "all" },
  { label: "Today", value: "today" },
  { label: "Last 7 Days", value: "week" },
  { label: "Last 30 Days", value: "month" },
];

const columns = [
  {
    name: "Transaction ID",
    accessor: "_id",
  },
  {
    name: "Amount",
    accessor: "amount",
    cell: (row) => {
      return <span>{fCurrency(row?.amount)}</span>;
    },
  },
  {
    name: "Status",
    accessor: "status",
  },

  {
    name: "Date",
    accessor: "createdAt",
    cell: (row) => {
      return <span>{new Date(row.createdAt).toLocaleDateString()}</span>;
    },
  },
  {
    name: "Purpose",
    accessor: "purpose",
    cell: (row) => {
      return (
        <div className="flex items-center gap-2">
          <span>
            {row?.purpose === "tip" &&
              row?.transactionType === "credit" &&
              `${getTipDescription(row?.tipType)} from ${
                row?.recipientId?.username
              }`}
            {row?.purpose === "tip" &&
              row?.transactionType === "debit" &&
              `${getTipDescription(row?.tipType)} to ${
                row?.recipientId?.username
              }`}
          </span>
          <span>
            {row?.purpose === "withdrawal" && <Icon name="mdi:withdrawal" />}
          </span>
          <span>
            {row?.purpose === "deposit" && <Icon name="mdi:deposit" />}
          </span>
          <span>
            {row?.purpose === "transfer" && <Icon name="mdi:transfer-down" />}
          </span>
        </div>
      );
    },
  },
  // {
  //   name: "Transaction Type",
  //   accessor: "transactionType",
  //   cell: (row) => {
  //     return (
  //       <span>
  //         {row?.transactionType === "credit" ? (
  //           <Icon name="credit" />
  //         ) : (
  //           <Icon name="debit" />
  //         )}
  //       </span>
  //     );
  //   },
  // },
  // {
  //   name: "Action",
  //   accessor: "action",
  // },
];

export default function Transactions() {
  const [showFundWalletModal, setShowFundWalletModal] = useState(false);
  const { wallet, isLoading, error, getWallet } = useWalletStore();
  const { user } = useAuth();
  const [amount, setAmount] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState("all");

  useEffect(() => {
    getWallet();
  }, []);

  const config = flutterwaveConfig({
    ...user,
    amount,
    title: "Top up wallet",
    description: "Top up your wallet with NGN",
  });

  const handleFlutterPayment = useFlutterwave(config);

  const { data: transactions } = useQuery({
    queryKey: ["transactions"],
    queryFn: async () => {
      const response = await transactionService.getTransactions({
        page: 1,
        limit: 10,
      });
      return {
        data: response.transactions,
        total: response.total,
        currentPage: response.currentPage,
        totalPages: response.totalPages,
      };
    },
  });

  const { data: earnings } = useQuery({
    queryKey: ["earnings", selectedPeriod],
    queryFn: async () => {
      const response = await transactionService.getCreatorEarnings(
        selectedPeriod
      );
      return response;
    },
  });

  if (isLoading && !wallet) return <div>Loading...</div>;

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Earnings Dashboard</h1>
        <div className="flex items-center gap-4">
          <select
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            {PERIOD_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-green-100 rounded-full">
              <BsCashCoin className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-gray-500 text-sm">Total Earnings</p>
              <p className="text-2xl font-bold">
                {fCurrency(earnings?.totalEarnings || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <BsWallet2 className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-gray-500 text-sm">Available Balance</p>
              <p className="text-2xl font-bold">
                {fCurrency(wallet?.balance || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-yellow-100 rounded-full">
              <MdPendingActions className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-gray-500 text-sm">Pending Withdrawals</p>
              <p className="text-2xl font-bold">
                {fCurrency(earnings?.pendingWithdrawalsTotal || 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {earnings?.earningsByPurpose?.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-8">
          <h2 className="text-lg font-semibold mb-4">Earnings Breakdown</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {earnings.earningsByPurpose.map((item) => (
              <div key={item._id} className="p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-500 capitalize">{item._id}</p>
                <p className="text-xl font-bold">{fCurrency(item.total)}</p>
                <p className="text-sm text-gray-400">
                  {item.count} transactions
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      <div>
        <h2 className="text-xl font-bold mb-4">Recent Transactions</h2>
        <Transaction transactions={transactions?.data} />
      </div>

      <Modal
        isOpen={showFundWalletModal}
        onClose={() => setShowFundWalletModal(false)}
        title="Fund wallet"
      >
        <div>
          <div>
            <p className="text-gray-500">Available balance</p>
            <p className="text-3xl text-black font-extrabold">
              {fCurrency(wallet?.balance)}
            </p>
          </div>

          <div className="space-y-2 my-4">
            <CustomInput
              type="number"
              value={amount}
              label="Amount"
              placeholder="Enter amount"
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
          <div className="mt-10 flex flex-col gap-2">
            <InteractiveButton
              disabled={amount < 1000}
              onClick={() => {
                handleFlutterPayment({
                  callback: async (response) => {
                    console.log(response);
                    const result = await walletService.creditWallet({
                      amount: response.amount,
                      transactionRef: response,
                      paymentMethod: "flutterwave",
                    });
                    // await walletService.

                    fundWallet(result.data.wallet, result.data.transaction);
                    setShowFundWalletModal(false);
                    closePaymentModal(); // this will close the modal programmatically
                  },
                  onClose: () => {},
                });
              }}
              className="!flex items-center gap-2"
            >
              <span>Flutterwave</span>
              <span>
                <img
                  src="/icon/flutterwave.svg"
                  alt="Flutterwave"
                  className="w-4"
                />
              </span>
            </InteractiveButton>
            <InteractiveButton className="!flex items-center gap-2">
              <span>Paystack</span>
              <span>
                <img src="/icon/paystack.svg" alt="Paystack" className="w-4" />
              </span>
            </InteractiveButton>
          </div>
        </div>
      </Modal>
    </div>
  );
}

function Transaction({ transactions }) {
  if (!transactions || transactions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow-sm">
        <RiEmotionSadLine className="w-16 h-16 text-gray-400 mb-4" />
        <h3 className="text-xl font-medium text-gray-900 mb-2">
          No Transactions Yet
        </h3>
        <p className="text-gray-500">You haven't made any transactions yet.</p>
      </div>
    );
  }

  return (
    <div>
      <Table columns={columns} data={transactions} />
    </div>
  );
}
