import React, { useEffect } from "react";
import { FaPlus, FaEdit, FaTrash } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import usePostStore from "../../store/postStore";
import { useAuth } from "../../hooks/useAuth";
import Table from "../../components/Table";
import { formatDistanceToNow } from "date-fns";
import LoadingSpinner from "../../components/LoadingSpinner";
import { postService } from "../../services/api";
import toast from "react-hot-toast";

const CreatorPosts = () => {
  const { posts, loadUserPosts, isLoading, error } = usePostStore();
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadUserPosts(user?._id);
  }, [user]);

  const handleEdit = (postId) => {
    navigate(`/creator/edit-post/${postId}`);
  };

  const handleDelete = async (postId) => {
    if (window.confirm("Are you sure you want to delete this post?")) {
      // Add delete functionality here
      console.log("Delete post:", postId);
      try {
        await postService.deletePost(postId);
        toast.success("Post deleted successfully");
        loadUserPosts(user?._id);
      } catch (error) {
        console.error("Error deleting post:", error);
      }
    }
  };

  const columns = [
    { name: "Content", accessor: "content" },
    // { name: "Type", accessor: "type" },
    { name: "Media", accessor: "mediaCount" },
    { name: "Public", accessor: "isPublic" },
    { name: "Likes", accessor: "likesCount" },
    { name: "Comments", accessor: "commentsCount" },
    { name: "Created", accessor: "createdAt" },
    { name: "Actions", accessor: "actions" },
  ];

  const tableData = posts?.map((post) => ({
    content:
      post.content.length > 50
        ? post.content.substring(0, 50) + "..."
        : post.content,
    type:
      post.type === "free" ? (
        <span className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full">
          Free
        </span>
      ) : (
        <span className="px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded-full">
          Premium
        </span>
      ),
    mediaCount: (
      <span className="text-gray-600">
        {post.mediaUrls?.length || 0}{" "}
        {post.mediaUrls?.length === 1 ? "file" : "files"}
      </span>
    ),
    isPublic: post.isPublic ? (
      <span className="px-2 py-1 text-xs font-medium text-primary bg-primary/10 rounded-full">
        Public
      </span>
    ) : (
      <span className="px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-full">
        Private
      </span>
    ),
    likesCount: post.likesCount,
    commentsCount: post.commentsCount,
    createdAt: formatDistanceToNow(new Date(post.createdAt), {
      addSuffix: true,
    }),
    actions: (
      <div className="flex items-center gap-3">
        {/* <button
          onClick={() => handleEdit(post._id)}
          className="text-primary hover:text-primary/80 transition-colors"
        >
          <FaEdit className="w-4 h-4" />
        </button> */}
        <button
          onClick={() => handleDelete(post._id)}
          className="text-red-600 hover:text-red-800 transition-colors"
        >
          <FaTrash className="w-4 h-4" />
        </button>
      </div>
    ),
  }));

  if (isLoading) {
    return <LoadingSpinner size="xl" />;
  }

  return (
    <div className="container  px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800">My Posts</h1>
        <button
          onClick={() => navigate("/creator/add-post")}
          className="flex items-center gap-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/80 transition-colors"
        >
          <FaPlus className="text-sm" />
          <span>Create Post</span>
        </button>
      </div>

      {posts.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">No posts yet</div>
          <button
            onClick={() => navigate("/creator/add-post")}
            className="text-primary hover:text-primary/80 font-medium"
          >
            Create your first post
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm">
          <Table columns={columns} data={tableData} />
        </div>
      )}
    </div>
  );
};

export default CreatorPosts;
