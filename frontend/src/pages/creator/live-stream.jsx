import { useState, useRef, useEffect } from "react";
import {
  FaVideo,
  FaPlus,
  FaDollarSign,
  FaMicrophone,
  FaCog,
} from "react-icons/fa";
import { IoClose } from "react-icons/io5";
import { BsInfoCircle } from "react-icons/bs";
import { toast } from "react-hot-toast";

export default function LiveStream() {
  const [stream, setStream] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [showTipMenu, setShowTipMenu] = useState(false);
  const [title, setTitle] = useState("");
  const [error, setError] = useState(null);
  const [tipMenuItems, setTipMenuItems] = useState([]);
  const [newTipAmount, setNewTipAmount] = useState("");
  const [newTipAction, setNewTipAction] = useState("");
  const [availableMicrophones, setAvailableMicrophones] = useState([]);
  const [availableCameras, setAvailableCameras] = useState([]);
  const [selectedMicrophone, setSelectedMicrophone] = useState("");
  const [selectedCamera, setSelectedCamera] = useState("");
  const [videoQuality, setVideoQuality] = useState("medium");
  const videoRef = useRef(null);

  // Get available devices on component mount
  useEffect(() => {
    const getAvailableDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const microphones = devices.filter(
          (device) => device.kind === "audioinput"
        );
        const cameras = devices.filter(
          (device) => device.kind === "videoinput"
        );

        setAvailableMicrophones(microphones);
        setAvailableCameras(cameras);

        // Set defaults
        if (microphones.length > 0)
          setSelectedMicrophone(microphones[0].deviceId);
        if (cameras.length > 0) setSelectedCamera(cameras[0].deviceId);
      } catch (error) {
        console.error("Error getting devices:", error);
      }
    };

    getAvailableDevices();
  }, []);

  useEffect(() => {
    // Cleanup function to stop the stream when component unmounts
    return () => {
      if (stream) {
        const tracks = stream.getTracks();
        tracks.forEach((track) => track.stop());
      }
    };
  }, [stream]);

  // Effect to handle video stream setup
  useEffect(() => {
    if (stream && videoRef.current) {
      videoRef.current.srcObject = stream;
      // Handle video play ready state
      videoRef.current.onloadedmetadata = () => {
        videoRef.current.play().catch((e) => {
          console.error("Error playing video:", e);
          setError("Failed to play video stream");
        });
      };
    }
  }, [stream]);

  const getVideoConstraints = () => {
    const qualitySettings = {
      low: { width: 640, height: 480 },
      medium: { width: 1280, height: 720 },
      high: { width: 1920, height: 1080 },
    };

    return {
      deviceId: selectedCamera ? { exact: selectedCamera } : undefined,
      ...qualitySettings[videoQuality],
    };
  };

  const startCamera = async () => {
    try {
      setError(null);
      console.log("Requesting camera access...");

      const constraints = {
        video: getVideoConstraints(),
        audio: selectedMicrophone
          ? { deviceId: { exact: selectedMicrophone } }
          : true,
      };

      const mediaStream = await navigator.mediaDevices.getUserMedia(
        constraints
      );
      console.log("Camera access granted:", mediaStream.getTracks());

      setStream(mediaStream);
      toast.success("Camera started successfully");
    } catch (error) {
      console.error("Error accessing camera:", error);
      setError(error.message);
      toast.error(`Camera access failed: ${error.message}`);

      if (error.name === "NotAllowedError") {
        toast.error("Please allow camera access to start streaming");
      } else if (error.name === "NotFoundError") {
        toast.error("No camera found. Please connect a camera and try again");
      }
    }
  };

  const stopCamera = () => {
    if (stream) {
      console.log("Stopping camera tracks...");
      const tracks = stream.getTracks();
      tracks.forEach((track) => {
        console.log(`Stopping track: ${track.kind}`);
        track.stop();
      });
      setStream(null);
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
      toast.success("Camera stopped");
    }
  };

  const handleStartStream = () => {
    if (!stream) {
      toast.error("Please start your camera first");
      return;
    }
    if (!title.trim()) {
      toast.error("Please enter a stream title");
      return;
    }
    setIsStreaming(true);
    toast.success("Stream started!");
  };

  const handleStopStream = () => {
    setIsStreaming(false);
    stopCamera();
    toast.success("Stream ended");
  };

  const addTipMenuItem = () => {
    if (!newTipAmount || !newTipAction) {
      toast.error("Please enter both amount and action");
      return;
    }

    const amount = parseFloat(newTipAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    setTipMenuItems([...tipMenuItems, { amount, action: newTipAction }]);
    setNewTipAmount("");
    setNewTipAction("");
    toast.success("Tip menu item added");
  };

  const removeTipMenuItem = (index) => {
    setTipMenuItems(tipMenuItems.filter((_, i) => i !== index));
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">New stream</h1>
        <div className="flex items-center gap-2 text-gray-600">
          <BsInfoCircle className="w-5 h-5" />
          <p>Set up your stream and go live</p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="space-y-6">
          {/* Camera Control Button */}
          <div className="flex justify-end">
            {!stream ? (
              <button
                onClick={startCamera}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <FaVideo className="w-4 h-4" />
                Start Camera
              </button>
            ) : (
              <button
                onClick={stopCamera}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <IoClose className="w-4 h-4" />
                Stop Camera
              </button>
            )}
          </div>

          {/* Stream Preview with Controls */}
          <div className="space-y-2">
            <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
              {stream ? (
                <>
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-contain"
                  />
                  {error && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50 text-white">
                      <p>{error}</p>
                    </div>
                  )}
                </>
              ) : (
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <FaVideo className="w-12 h-12 text-gray-400 mb-2" />
                  <p className="text-gray-500">
                    Camera preview will appear here
                  </p>
                  {error && <p className="text-red-500 mt-2">{error}</p>}
                </div>
              )}
            </div>

            {/* Stream Controls */}
            <div className="flex items-center gap-4 p-2 bg-gray-50 rounded-lg">
              {/* Microphone Selection */}
              <div className="flex-1">
                <label className="block text-sm text-gray-600 mb-1">
                  Microphone
                </label>
                <select
                  value={selectedMicrophone}
                  onChange={(e) => setSelectedMicrophone(e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {availableMicrophones.map((mic) => (
                    <option key={mic.deviceId} value={mic.deviceId}>
                      {mic.label || `Microphone ${mic.deviceId.slice(0, 5)}`}
                    </option>
                  ))}
                </select>
              </div>

              {/* Camera Selection */}
              <div className="flex-1">
                <label className="block text-sm text-gray-600 mb-1">
                  Camera
                </label>
                <select
                  value={selectedCamera}
                  onChange={(e) => setSelectedCamera(e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {availableCameras.map((camera) => (
                    <option key={camera.deviceId} value={camera.deviceId}>
                      {camera.label || `Camera ${camera.deviceId.slice(0, 5)}`}
                    </option>
                  ))}
                </select>
              </div>

              {/* Video Quality Selection */}
              <div className="flex-1">
                <label className="block text-sm text-gray-600 mb-1">
                  Video quality
                </label>
                <select
                  value={videoQuality}
                  onChange={(e) => setVideoQuality(e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="low">Low (480p)</option>
                  <option value="medium">Medium (720p)</option>
                  <option value="high">High (1080p)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Stream Title */}
          <div>
            <label
              htmlFor="title"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Title (required)
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="What's this stream about?"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Tip Menu Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Create Tip Menu</h3>
                <p className="text-sm text-gray-600">
                  Display tip menu during the stream
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={showTipMenu}
                  onChange={() => setShowTipMenu(!showTipMenu)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>

            {showTipMenu && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                {/* Existing Tip Menu Items */}
                {tipMenuItems.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-4 bg-white p-3 rounded-lg"
                  >
                    <div className="flex items-center gap-2">
                      ₦<span>{item.amount}</span>
                    </div>
                    <div className="flex-1">{item.action}</div>
                    <button
                      onClick={() => removeTipMenuItem(index)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      <IoClose className="w-5 h-5" />
                    </button>
                  </div>
                ))}

                {/* Add New Tip Menu Item */}
                <div className="flex gap-2">
                  <div className="flex items-center gap-2 bg-white px-3 py-2 rounded-lg border border-gray-200">
                    ₦
                    <input
                      type="number"
                      value={newTipAmount}
                      onChange={(e) => setNewTipAmount(e.target.value)}
                      placeholder="Amount"
                      className="w-20 focus:outline-none"
                    />
                  </div>
                  <input
                    type="text"
                    value={newTipAction}
                    onChange={(e) => setNewTipAction(e.target.value)}
                    placeholder="Set action (e.g. Song request)"
                    className="flex-1 px-3 py-2 bg-white rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                  <button
                    onClick={addTipMenuItem}
                    className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    <FaPlus className="w-4 h-4" />
                    Add
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Stream Control Button */}
          <div className="flex justify-center">
            {!isStreaming ? (
              <button
                onClick={handleStartStream}
                className="w-full bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
                disabled={!stream}
              >
                Go live
              </button>
            ) : (
              <button
                onClick={handleStopStream}
                className="w-full bg-red-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-600 transition-colors"
              >
                End Stream
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
