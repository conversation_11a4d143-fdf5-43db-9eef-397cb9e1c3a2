import { useState, useEffect } from "react";
import { FiUser } from "react-icons/fi";
import { subscriptionService } from "../../services/api";
import Pagination from "../../components/Pagination";
import Table from "../../components/Table";
import { fDateTime } from "../../utils/formatTime";

export default function CreatorSubscribers() {
  const [activeTab, setActiveTab] = useState("active");
  const [subscribers, setSubscribers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const tabs = [
    { id: "active", label: "Active" },
    { id: "expired", label: "Expired" },
    { id: "all", label: "All" },
  ];

  const columns = [
    { name: "Subscriber", accessor: "username" },
    // { name: "Email", accessor: "email" },
    { name: "Amount", accessor: "amount" },
    { name: "Duration", accessor: "duration" },
    { name: "Start Date", accessor: "startDate" },
    { name: "End Date", accessor: "endDate" },
    // { name: "Payment Method", accessor: "paymentMethod" },
    { name: "Status", accessor: "status" },
  ];

  useEffect(() => {
    setCurrentPage(1); // Reset to first page when tab changes
  }, [activeTab]);

  useEffect(() => {
    const fetchSubscribers = async () => {
      try {
        setLoading(true);
        const response = await subscriptionService.getSubscribers(
          activeTab,
          currentPage
        );

        // Transform the data for table display
        const transformedData = response.subscriptions.map((sub) => ({
          username: `@${sub.subscriber.username}`,
          email: sub.subscriber.email,
          amount: sub.planSnapshot.amount,
          duration: `${sub.planSnapshot.noOfMonths} ${
            sub.planSnapshot.noOfMonths > 1 ? "months" : "month"
          }`,
          startDate: fDateTime(sub.startDate),
          endDate: fDateTime(sub.endDate),
          paymentMethod:
            sub.paymentMethod.charAt(0).toUpperCase() +
            sub.paymentMethod.slice(1),
          status: sub.status.charAt(0).toUpperCase() + sub.status.slice(1),
        }));

        setSubscribers(transformedData);
        setTotalPages(response.totalPages);
        setError(null);
      } catch (err) {
        setError(err.response?.data?.message || "Failed to fetch subscribers");
        console.log(err);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscribers();
  }, [activeTab, currentPage]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo(0, 0); // Scroll to top when page changes
  };

  return (
    <div className="flex container flex-col w-full h-full p-4 bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-300 mb-6">
          Subscribers
        </h1>

        <button className="bg-primary text-white px-4 py-2 rounded-lg">
          Mass DM subscribers
        </button>
      </div>

      {/* Tabs Navigation */}
      <div className="flex space-x-2 border-b border-gray-200 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-6 py-3 text-sm font-medium transition-all duration-200 relative
              ${
                activeTab === tab.id
                  ? "text-primary border-b-2 border-primary"
                  : "text-gray-500 hover:text-primary-400"
              }
              before:absolute before:bottom-0 before:left-0 before:w-full before:h-0.5 before:transition-all
              hover:before:bg-primary-100`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="flex-1 bg-white rounded-lg min-w-0">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-10 w-10 border-4 border-primary border-t-transparent"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 p-6 bg-red-50 rounded-lg">
            <p className="font-medium">{error}</p>
          </div>
        ) : subscribers.length === 0 ? (
          <div className="flex flex-col justify-center items-center h-64 bg-gray-50 rounded-lg">
            <FiUser className="w-16 h-16 text-gray-400 mb-4" />
            <p className="text-center text-gray-600 font-medium">
              You don&apos;t have any {activeTab} subscribers yet.
            </p>
            <p className="text-center text-gray-400 text-sm mt-2">
              Your subscribers will appear here once they subscribe to your
              content.
            </p>
          </div>
        ) : (
          <div className="flex flex-col min-w-0">
            <div className="mb-6">
              <Table columns={columns} data={subscribers} />
            </div>
            <div className="flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
