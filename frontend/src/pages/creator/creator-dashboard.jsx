import {
  FaCopy,
  FaUsers,
  FaDollarSign,
  FaUserFriends,
  FaCog,
  FaCalendarAlt,
  FaPen,
  FaVideo,
  FaPlus,
  FaCheck,
} from "react-icons/fa";
import { IoMdInformationCircle } from "react-icons/io";
import { BsCameraVideo } from "react-icons/bs";
import { useAuth } from "../../hooks/useAuth";
import { useState, useEffect } from "react";
import { userService } from "../../services/api";
import { toast } from "react-hot-toast";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useNavigate } from "react-router-dom";
import TooltipComponent from "../../components/ui/Tooltip";
import AccountSetupStatus from "../../components/AccountSetupStatus";

export default function CreatorDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    directLink: `https://errorsphere.com/${user?.username}`,
    subscribersOnline: 0,
    revenue: {
      allTime: 0.0,
      nextPayout: 0.0,
    },
    subscribers: {
      active: 0,
      thisWeek: {
        new: 0,
        expired: 0,
      },
    },
    pageOpens: {
      total: 0,
      authenticated: 0,
      unauthenticated: 0,
      trend: {
        today: 0,
        yesterday: 0,
      },
    },
    salesStats: {
      joins: 0,
      renews: 0,
      chatTips: 0,
      tips: 0,
    },
  });

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        const response = await userService.getCreatorStats(user?.username);
        setStats(response.stats);
      } catch (error) {
        console.error("Error fetching creator stats:", error);
        toast.error("Failed to load creator stats");
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.username) {
      fetchStats();
    }
  }, [user?.username]);

  // Sample data for the graph
  const graphData = [
    { date: "8 Jan", value: 0 },
    { date: "10 Jan", value: 0 },
    { date: "12 Jan", value: 0 },
    { date: "14 Jan", value: 0 },
    { date: "16 Jan", value: 0 },
    { date: "18 Jan", value: 0 },
    { date: "20 Jan", value: 0 },
    { date: "22 Jan", value: 0 },
    { date: "24 Jan", value: 0 },
    { date: "26 Jan", value: 0 },
    { date: "28 Jan", value: 0 },
    { date: "30 Jan", value: 0 },
    { date: "1 Feb", value: 0 },
    { date: "3 Feb", value: 0 },
    { date: "5 Feb", value: 0 },
  ];

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  return (
    <div className="p-6 max-w-7xl mx-auto bg-gray-50 min-h-screen relative">
      <div className="space-y-2 mb-6">
        <h1 className="text-2xl font-bold text-secondary">Dashboard</h1>

        <div>
          <p>
            Welcome back, {user?.username}! Here's a quick overview of your
            activity and performance.
          </p>
        </div>
      </div>
      {/* Getting Started Banner */}
      {/* Account Setup Status - Hide on mobile when chat is selected */}
      {user?.kycStatus === "pending" && (
        <div className="pt-4 overflow-y-auto">
          <AccountSetupStatus user={user} />
        </div>
      )}
      {/* Links Section */}
      <div className="bg-white rounded-2xl shadow-sm p-8 mb-6 border border-gray-100">
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-secondary-300 mb-3 flex items-center gap-2">
              Direct link
              <Tooltip content="Share this link with your audience to let them access your content">
                <IoMdInformationCircle className="text-gray-400 w-5 h-5 cursor-help" />
              </Tooltip>
            </h3>
            <div className="flex items-center gap-3">
              <input
                type="text"
                value={stats.directLink}
                readOnly
                className="flex-1 p-3 border border-gray-200 rounded-xl bg-gray-50 text-secondary focus:ring-2 focus:ring-primary-100 focus:border-primary"
              />
              <button
                onClick={() => copyToClipboard(stats.directLink)}
                className="p-3 text-primary hover:bg-primary-100 rounded-xl transition-colors duration-200"
              >
                <FaCopy className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Fans Online */}
        <div className="bg-white rounded-2xl shadow-sm p-6 border border-gray-100 hover:border-primary-200 transition-colors duration-200">
          <div className="flex justify-between items-start">
            <h3 className="text-lg font-medium text-secondary-300">
              Subscribers Online
            </h3>
            <Tooltip content="Number of subscribers currently viewing your content">
              <IoMdInformationCircle className="text-gray-400 w-5 h-5 cursor-help" />
            </Tooltip>
          </div>
          {isLoading ? (
            <div className="h-12 w-24 bg-gray-200 animate-pulse rounded mt-4"></div>
          ) : (
            <p className="text-4xl font-bold mt-4 text-secondary">
              {stats.subscribersOnline}
            </p>
          )}
        </div>

        {/* Revenue Stats */}
        <div className="bg-white rounded-2xl shadow-sm p-6 border border-gray-100 hover:border-primary-200 transition-colors duration-200">
          <div className="space-y-6">
            <div>
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium text-secondary-300">
                  All-time Revenue
                </h3>
                <Tooltip content="Total earnings since you started creating content">
                  <IoMdInformationCircle className="text-gray-400 w-5 h-5 cursor-help" />
                </Tooltip>
              </div>
              {isLoading ? (
                <div className="h-12 w-32 bg-gray-200 animate-pulse rounded mt-4"></div>
              ) : (
                <p className="text-4xl font-bold mt-4 text-secondary">
                  $ {stats.revenue.allTime.toFixed(2)}
                </p>
              )}
            </div>
            <div>
              <h3 className="text-lg font-medium text-secondary-300">
                Next Payout
              </h3>
              {isLoading ? (
                <div className="h-12 w-32 bg-gray-200 animate-pulse rounded mt-2"></div>
              ) : (
                <>
                  <p className="text-4xl font-bold mt-2 text-secondary">
                    $ {stats.revenue.nextPayout.toFixed(2)}
                  </p>
                  {stats.revenue.nextPayout === 0 && (
                    <p className="text-sm text-primary mt-1">
                      Below the minimum
                    </p>
                  )}
                </>
              )}
            </div>
          </div>
        </div>

        {/* Subscriber Stats */}
        <div className="bg-white rounded-2xl shadow-sm p-6 border border-gray-100 hover:border-primary-200 transition-colors duration-200">
          <div className="space-y-6">
            <div>
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium text-secondary-300">
                  Active Subscribers
                </h3>
                <Tooltip content="Total number of paying subscribers to your content">
                  <IoMdInformationCircle className="text-gray-400 w-5 h-5 cursor-help" />
                </Tooltip>
              </div>
              {isLoading ? (
                <div className="h-12 w-24 bg-gray-200 animate-pulse rounded mt-4"></div>
              ) : (
                <p className="text-4xl font-bold mt-4 text-secondary">
                  {stats.subscribers.active}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* This Week Stats */}
        <div className="bg-white rounded-2xl shadow-sm p-8 col-span-full border border-gray-100">
          <h3 className="text-xl font-medium text-secondary-400 mb-6">
            THIS WEEK YOU HAVE
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-lg text-secondary-300 mb-4">Subscribers</h4>
              {isLoading ? (
                <div className="space-y-2">
                  <div className="h-10 w-24 bg-gray-200 animate-pulse rounded"></div>
                  <div className="h-10 w-24 bg-gray-200 animate-pulse rounded"></div>
                </div>
              ) : (
                <div className="flex items-center gap-6">
                  <div className="text-green-500">
                    <span className="text-3xl font-bold">
                      {stats.subscribers.thisWeek.new}
                    </span>
                    <span className="text-sm ml-2">new</span>
                  </div>
                  <div className="text-primary">
                    <span className="text-3xl font-bold">
                      {stats.subscribers.thisWeek.expired}
                    </span>
                    <span className="text-sm ml-2">exp</span>
                  </div>
                </div>
              )}
            </div>
            <div>
              <h4 className="text-lg text-secondary-300 mb-4">Page opens</h4>
              {isLoading ? (
                <div className="h-10 w-24 bg-gray-200 animate-pulse rounded"></div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <p className="text-3xl font-bold text-secondary">
                      {stats.pageOpens.total}
                    </p>
                    <p className="text-sm text-gray-500">Total visits</p>
                  </div>
                  <div className="flex gap-4">
                    <div>
                      <p className="text-lg font-semibold text-secondary">
                        {stats.pageOpens.authenticated}
                      </p>
                      <p className="text-sm text-gray-500">Authenticated</p>
                    </div>
                    <div>
                      <p className="text-lg font-semibold text-secondary">
                        {stats.pageOpens.unauthenticated}
                      </p>
                      <p className="text-sm text-gray-500">Unauthenticated</p>
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <div>
                      <p className="text-lg font-semibold text-green-500">
                        {stats.pageOpens.trend.today}
                      </p>
                      <p className="text-sm text-gray-500">Today</p>
                    </div>
                    <div>
                      <p className="text-lg font-semibold text-gray-400">
                        {stats.pageOpens.trend.yesterday}
                      </p>
                      <p className="text-sm text-gray-500">Yesterday</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Sales Statistics */}
      <div className="bg-white rounded-2xl shadow-sm p-8 mt-6 border border-gray-100">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-medium text-secondary-400">SALES</h3>
          <div className="flex gap-3">
            <button className="p-3 text-secondary hover:bg-secondary-100 hover:text-white rounded-xl transition-all duration-200">
              <FaCog className="w-5 h-5" />
            </button>
            <button className="p-3 text-secondary hover:bg-secondary-100 hover:text-white rounded-xl transition-all duration-200">
              <FaCalendarAlt className="w-5 h-5" />
            </button>
          </div>
        </div>

        <p className="text-sm text-gray-500 mb-8">
          The data is shown in Europe/Amsterdam time zone. Your time zone is
          Africa/Lagos
        </p>

        <div className="h-[400px] mb-8">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={graphData}>
              <CartesianGrid
                strokeDasharray="3 3"
                vertical={false}
                stroke="#E5E7EB"
              />
              <XAxis
                dataKey="date"
                axisLine={false}
                tickLine={false}
                tick={{ fill: "#6B7280", fontSize: 12 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: "#6B7280", fontSize: 12 }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#fff",
                  border: "1px solid #E5E7EB",
                  borderRadius: "0.5rem",
                  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#E91E63"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 8, fill: "#E91E63" }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span className="text-sm text-secondary-300">Joins:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.joins}
              </span>
            </div>
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-purple-500"></div>
              <span className="text-sm text-secondary-300">Renews:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.renews}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-primary"></div>
              <span className="text-sm text-secondary-300">Refunds:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.refunds}
              </span>
            </div>
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <span className="text-sm text-secondary-300">DM Unlocks:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.dmUnlocks}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-orange-500"></div>
              <span className="text-sm text-secondary-300">Chat Tips:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.chatTips}
              </span>
            </div>
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span className="text-sm text-secondary-300">Tips:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.tips}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-gray-500"></div>
              <span className="text-sm text-secondary-300">Spins:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.spins}
              </span>
            </div>
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-lime-500"></div>
              <span className="text-sm text-secondary-300">Calls:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.calls}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="w-3 h-3 rounded-full bg-indigo-500"></div>
              <span className="text-sm text-secondary-300">Call tips:</span>
              <span className="text-sm font-medium text-secondary">
                {stats.salesStats.callTips}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
