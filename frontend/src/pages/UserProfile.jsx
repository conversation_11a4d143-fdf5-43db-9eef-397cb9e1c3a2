import { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useAuth } from "../hooks/useAuth";
import { useNavigate } from "react-router-dom";
import { postService, userService } from "../services/api";
import usePostStore from "../store/postStore";
import PostFeed from "../components/PostFeed";
import { config } from "../config";
import { useParams } from "react-router-dom";
import SubscriptionModal from "../components/SubscriptionModal";
import useWalletStore from "../store/walletStore";
import UserMedia from "../components/UserMedia";
import { fCurrency, fShortenNumber } from "../utils/formatNumber";
import LoadingSpinner from "../components/LoadingSpinner";
import { io } from "socket.io-client";
import { formatLastSeen } from "../utils/utils";
import { useQuery } from "@tanstack/react-query";
import PhotoViewer from "../components/PhotoViewer";
import TipModal from "../components/TipModal";
import { subscriptionService } from "../services/api";

// Initialize socket connection
const socket = io(import.meta.env.VITE_BASE_API_URL, {
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  path: "/socket.io",
  transports: ["websocket", "polling"],
  withCredentials: true,
  autoConnect: false,
});

const SOCIAL_LINKS_CONFIG = {
  facebook: {
    baseUrl: "https://facebook.com/",
    icon: "mdi:facebook",
    bgColor: "bg-blue-600",
    label: "Facebook",
  },
  instagram: {
    baseUrl: "https://instagram.com/",
    icon: "mdi:instagram",
    bgColor: "bg-red-600",
    label: "Instagram",
  },
  twitter: {
    baseUrl: "https://twitter.com/",
    icon: "mdi:twitter",
    bgColor: "bg-sky-500",
    label: "Twitter",
  },
  linkedin: {
    baseUrl: "https://linkedin.com/in/",
    icon: "mdi:linkedin",
    bgColor: "bg-blue-700",
    label: "LinkedIn",
  },
};

export default function UserProfile() {
  const [activeTab, setActiveTab] = useState("posts");
  const [isOnline, setIsOnline] = useState(false);
  const [lastSeen, setLastSeen] = useState(null);
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { media } = usePostStore();
  const [openSubscription, setOpenSubscription] = useState(true);
  const { username } = useParams();
  const [openSubscriptionModal, setOpenSubscriptionModal] = useState(null);
  const { wallet, getWallet, isLoading: isLoadingWallet } = useWalletStore();
  const [selectedImage, setSelectedImage] = useState(null);
  const [openTipModal, setOpenTipModal] = useState(false);
  const [existingSubscription, setExistingSubscription] = useState(null);

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["userProfile", username],
    queryFn: async () => {
      const response = await userService.getUserProfile(username);
      const postResponse = await postService.getUserPosts(response.user._id);
      const statsResponse = await postService.getUserMedia(response.user._id);

      // Check for existing subscription if user is authenticated
      let subscriptionStatus = null;
      if (isAuthenticated && user?._id) {
        try {
          const subResponse =
            await subscriptionService.checkExistingSubscription(
              response.user._id
            );
          console.log("subResponse", subResponse);
          if (subResponse) {
            subscriptionStatus = subResponse;
          }
        } catch (error) {
          console.error("Error checking subscription:", error);
        }
      }
      console.log("subscriptionStatus", subscriptionStatus);

      if (!user || user?._id !== response.user._id) {
        let sessionId = localStorage.getItem("visitorSessionId");
        if (!sessionId) {
          sessionId =
            Math.random().toString(36).substring(2) + Date.now().toString(36);
          localStorage.setItem("visitorSessionId", sessionId);
        }
        await userService.trackPageView(username, sessionId);
      }

      return {
        posts: postResponse.posts,
        userProfile: response.user,
        userStats: statsResponse,
        subscriptionStatus,
      };
    },
    enabled: !!username,
  });

  // Socket connection for online status
  useEffect(() => {
    if (data?.userProfile?._id) {
      socket.connect();

      // Request initial status
      socket.emit("get_user_status", data?.userProfile?._id);

      // Listen for status updates
      socket.on("user_status", ({ userId, online, lastSeen: lastSeenTime }) => {
        // console.log("user_status", { userId, online, lastSeenTime });
        if (userId === data?.userProfile?._id) {
          setIsOnline(online);
          setLastSeen(lastSeenTime);
        }
      });

      socket.on(
        "user_status_change",
        ({ userId, online, lastSeen: lastSeenTime }) => {
          if (userId === data?.userProfile?._id) {
            setIsOnline(online);
            setLastSeen(lastSeenTime);
          }
        }
      );

      return () => {
        socket.off("user_status");
        socket.off("user_status_change");
        socket.disconnect();
      };
    }
  }, [data?.userProfile?._id]);

  useEffect(() => {
    if (isAuthenticated) {
      getWallet();
    }
  }, [isAuthenticated]);
  // console.log(data);

  const handleShare = () => {
    navigator.share({
      title: `${
        data?.userProfile?.displayName || data?.userProfile?.username
      }'s profile`,
      text: `Check out their profile on ${config.app_name}`,
      url: `${config.base_url}/${data?.userProfile?.username}`,
    });
  };

  useEffect(() => {
    document.title = `${
      data?.userProfile?.displayName || data?.userProfile?.username
    } | ${config.app_name}`;
  }, [data?.userProfile]);

  const UnsubscribedContent = () => {
    return (
      <div className="mt-0 px-4 bg-gray-100 py-12 rounded-lg flex flex-col items-center justify-center">
        <div className="flex justify-center items-center mb-8">
          <Icon
            fontSize={50}
            className="text-gray-400"
            icon={"solar:heart-lock-line-duotone"}
          />
        </div>
        <div className="flex justify-center items-center space-x-8 mb-8">
          <div className="flex items-center space-x-2">
            <Icon
              icon="solar:file-text-line-duotone"
              className="text-gray-400 text-xl"
            />
            <span className="text-sm font-medium text-gray-600">
              {data?.userStats?.totalPostCount || 0}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon icon="heroicons:photo" className="text-gray-400 text-xl" />
            <span className="text-sm font-medium text-gray-600">
              {data?.userStats?.totalImageCount || 0}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon
              icon="heroicons:video-camera"
              className="text-gray-400 text-xl"
            />
            <span className="text-sm font-medium text-gray-600">
              {data?.userStats?.totalVideoCount || 0}
            </span>
          </div>
        </div>
        <button
          onClick={() =>
            setOpenSubscriptionModal(data?.userProfile?.pricingPlans[0])
          }
          className="w-full text-sm max-w-md mx-auto bg-primary text-white py-3 px-6 rounded-full text-center font-medium hover:bg-primary-500 transition-colors"
        >
          SUBSCRIBE TO SEE ALL USER'S CONTENT
        </button>
      </div>
    );
  };

  const SubscriptionSection = () => {
    if (!isAuthenticated) {
      return (
        <div className="mt-0 px-4 max-w-2xl mx-auto">
          <button
            onClick={() => navigate("/login")}
            className="w-full text-sm bg-secondary text-white py-3 px-6 rounded-full mb-6 flex items-center justify-between"
          >
            <span>Login to Subscribe</span>
            <Icon fontSize={18} icon="heroicons:arrow-right" />
          </button>
        </div>
      );
    }

    if (data?.userProfile?.isSubscribed) {
      return (
        <div className="mt-0 px-4 max-w-2xl mx-auto">
          <div className="border-2 text-sm border-green-700/50 text-green-700 py-2 px-6 rounded-full mb-6 flex items-center justify-between">
            <span>Subscribed</span>
            <Icon fontSize={20} icon="heroicons:check-circle" />
          </div>
        </div>
      );
    }

    if (data?.subscriptionStatus?.subscription) {
      const { subscription, canRetry, message } = data.subscriptionStatus;
      const { status, paymentStatus } = subscription;

      // Handle different subscription states
      switch (status) {
        case "active":
          return (
            <div className="mt-0 px-4 max-w-2xl mx-auto">
              <div className="border-2 text-sm border-green-700/50 text-green-700 py-2 px-6 rounded-full mb-6 flex items-center justify-between">
                <span>Active Subscription</span>
                <Icon fontSize={20} icon="heroicons:check-circle" />
              </div>
            </div>
          );

        case "payment_pending":
          return (
            <div className="mt-0 px-4 max-w-2xl mx-auto">
              <div className="border-2 text-sm border-yellow-500/50 text-yellow-700 py-2 px-6 rounded-full mb-6 flex items-center justify-between">
                <span>Payment Pending - Please wait for confirmation</span>
                <Icon fontSize={20} icon="heroicons:clock" />
              </div>
            </div>
          );

        case "payment_failed":
          return (
            <div className="mt-0 px-4 max-w-2xl mx-auto">
              {canRetry ? (
                <button
                  onClick={() =>
                    setOpenSubscriptionModal(data?.userProfile?.pricingPlans[0])
                  }
                  className="w-full text-sm bg-yellow-500 text-white py-3 px-6 rounded-full mb-6 flex items-center justify-between"
                >
                  <span>Payment Failed - Click to retry payment</span>
                  <Icon fontSize={18} icon="heroicons:arrow-path" />
                </button>
              ) : (
                <div className="border-2 text-sm border-red-500/50 text-red-700 py-2 px-6 rounded-full mb-6 flex items-center justify-between">
                  <span>Payment Failed - Maximum retry attempts reached</span>
                  <Icon fontSize={20} icon="heroicons:x-circle" />
                </div>
              )}
            </div>
          );

        case "cancelled":
          return (
            <div className="mt-0 px-4 max-w-2xl mx-auto">
              <div className="border-2 text-sm border-gray-500/50 text-gray-700 py-2 px-6 rounded-full mb-6 flex items-center justify-between">
                <span>Subscription Cancelled</span>
                <Icon fontSize={20} icon="heroicons:x-circle" />
              </div>
              <button
                onClick={() =>
                  setOpenSubscriptionModal(data?.userProfile?.pricingPlans[0])
                }
                className="w-full text-sm bg-secondary text-white py-3 px-6 rounded-full mb-6 flex items-center justify-between"
              >
                <span>Subscribe Again</span>
                <Icon fontSize={18} icon="heroicons:arrow-right" />
              </button>
            </div>
          );

        case "expired":
          return (
            <div className="mt-0 px-4 max-w-2xl mx-auto">
              <div className="border-2 text-sm border-gray-500/50 text-gray-700 py-2 px-6 rounded-full mb-6 flex items-center justify-between">
                <span>Subscription Expired</span>
                <Icon fontSize={20} icon="heroicons:clock" />
              </div>
              <button
                onClick={() =>
                  setOpenSubscriptionModal(data?.userProfile?.pricingPlans[0])
                }
                className="w-full text-sm bg-secondary text-white py-3 px-6 rounded-full mb-6 flex items-center justify-between"
              >
                <span>Renew Subscription</span>
                <Icon fontSize={18} icon="heroicons:arrow-path" />
              </button>
            </div>
          );

        default:
          return null;
      }
    }

    // No subscription exists - show subscription options
    return (
      <div className="mt-0 px-4 max-w-2xl mx-auto">
        <div className="">
          <button
            onClick={() => setOpenSubscription(!openSubscription)}
            className="w-full text-sm bg-secondary text-white py-3 px-6 rounded-full mb-6 flex items-center justify-between"
          >
            <span className="">Subscribe</span>
            <div className="flex items-center space-x-1">
              <span className="">Now</span>
              <Icon fontSize={18} icon="heroicons:chevron-down" />
            </div>
          </button>
          {openSubscription && data?.userProfile?.pricingPlans && (
            <div className="space-y-3">
              {data?.userProfile?.pricingPlans.map((tier) => (
                <button
                  onClick={() => setOpenSubscriptionModal(tier)}
                  key={tier.noOfMonths}
                  className="bg-pink-50 w-full rounded-full py-4 px-6 flex items-center justify-between"
                >
                  <span className="">{tier.noOfMonths} month(s)</span>
                  <div className="">
                    {fCurrency(tier.amount)}
                    {tier.discount > 0 && (
                      <span className="ml-2">({tier.discount}% discount)</span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full mx-auto">
      {isLoading || isLoadingWallet ? (
        <LoadingSpinner size="lg" />
      ) : (
        <>
          {/* Cover Photo and Profile Section */}
          <div className="relative">
            {/* Cover Photo */}
            <div className="h-48 w-full bg-primary relative">
              {data?.userProfile?.coverImage ? (
                <button
                  onClick={() => setSelectedImage(data?.userProfile.coverImage)}
                  className="w-full h-full"
                >
                  <img
                    src={data?.userProfile?.coverImage}
                    alt="Cover"
                    className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                  />
                </button>
              ) : (
                <div className="w-full h-full bg-primary" />
              )}
              {/* Back Button */}
              <div className="flex items-center justify-center">
                <button
                  onClick={() => navigate(-1)}
                  className="absolute top-4 left-4 p-2 rounded-full bg-white/50 hover:bg-white/80 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
                <button
                  onClick={handleShare}
                  className="absolute top-4 right-4 p-2 rounded-full bg-white/50 hover:bg-white/80 transition-colors"
                >
                  <Icon icon="hugeicons:share-01" fontSize={20} />
                </button>
              </div>
            </div>

            {/* Profile Info Section - Overlapping the cover photo */}
            <div className="relative  pb-4 -mt-10">
              <div className="bg-transparent rounded-lg p-4">
                <div className="flex items-end justify-between">
                  {/* Avatar - Moved up to overlap cover photo */}
                  <div className="flex items-start space-x-4">
                    <button
                      className="transform -translate-y-6"
                      onClick={() =>
                        data?.userProfile?.avatar &&
                        setSelectedImage(data?.userProfile.avatar)
                      }
                    >
                      {data?.userProfile?.avatar ? (
                        <img
                          src={data?.userProfile?.avatar}
                          alt="Profile"
                          className="w-32 h-32 rounded-full object-cover border-4 border-white cursor-pointer "
                        />
                      ) : (
                        <div className="w-24 h-24 rounded-full bg-gray-200 border-4 border-white flex items-center justify-center">
                          <Icon fontSize={48} icon="ph:user" />
                        </div>
                      )}
                    </button>
                  </div>

                  {/* Tip Button */}
                  <button
                    onClick={() => setOpenTipModal(true)}
                    className="border w-auto flex items-center gap-2 border-primary text-primary py-1 text-sm font-medium px-4 rounded-full"
                  >
                    <span>Tip</span>
                    <span>
                      <Icon fontSize={16} icon="hugeicons:money-add-01" />
                    </span>
                  </button>
                </div>
                <div className="mt-0">
                  <h2 className="text-xl  font-semibold">
                    {data?.userProfile?.displayName ||
                      data?.userProfile?.username}
                  </h2>
                  <div className="flex items-center space-x-5">
                    <p className="text-gray-600 text-sm">
                      @{data?.userProfile?.username}
                    </p>
                    <div className="flex items-center mt-1">
                      {isOnline ? (
                        <>
                          <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-sm text-gray-600">Online</span>
                        </>
                      ) : (
                        <>
                          <div className="w-2 h-2 rounded-full bg-gray-400 mr-2"></div>
                          <span className="text-sm text-gray-600">
                            {lastSeen
                              ? `${formatLastSeen(lastSeen)}`
                              : "Offline"}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-3 mt-2 font-medium">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-black">
                        {fShortenNumber(
                          data?.userStats?.totalActiveSubscribers || 0
                        )}{" "}
                        Subscribers
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-black">
                        {fShortenNumber(data?.userStats?.totalImageCount || 0)}{" "}
                        Images
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-black">
                        {fShortenNumber(data?.userStats?.totalVideoCount || 0)}{" "}
                        Videos
                      </span>
                    </div>
                  </div>
                  <div className="text-gray-600 my-2">
                    {data?.userProfile?.about}
                  </div>
                  {/* social media links */}
                  <div className="flex items-center space-x-2 mt-4 flex-wrap gap-2">
                    {data?.userProfile?.socialLinks &&
                      Object.entries(data?.userProfile?.socialLinks).map(
                        ([platform, username]) => {
                          if (!username || !SOCIAL_LINKS_CONFIG[platform])
                            return null;

                          const config = SOCIAL_LINKS_CONFIG[platform];
                          return (
                            <a
                              key={platform}
                              href={`${config.baseUrl}${username}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-1 bg-gray-100 p-1 rounded-full pr-3"
                            >
                              <div
                                className={`${config.bgColor} p-1 rounded-full h-6 w-6 flex items-center justify-center`}
                              >
                                <Icon
                                  icon={config.icon}
                                  fontSize={20}
                                  color="white"
                                />
                              </div>
                              <span className="text-sm">{config.label}</span>
                            </a>
                          );
                        }
                      )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Subscription section */}
          <SubscriptionSection />

          {/* Tabs */}
          <div className="mt-8 border-b">
            <div className="flex w-full justify-center">
              <button
                className={`px-6 py-3 font-medium ${
                  activeTab === "posts"
                    ? "border-b-2 border-primary text-primary"
                    : "text-gray-600"
                }`}
                onClick={() => setActiveTab("posts")}
              >
                {data?.userStats?.totalPostCount || 0} Posts
              </button>
              <button
                className={`px-6 py-3 font-medium ${
                  activeTab === "media"
                    ? "border-b-2 border-primary text-primary"
                    : "text-gray-600"
                }`}
                onClick={() => setActiveTab("media")}
              >
                {data?.userStats?.totalMediaCount || 0} Media
              </button>
            </div>
          </div>

          {activeTab === "posts" && (
            <>
              {data?.userStats?.totalPostCount > 0 &&
                !data?.userProfile?.isSubscribed && <UnsubscribedContent />}
              {/* Posts */}
              {data?.posts?.length > 0 && <PostFeed posts={data?.posts} />}

              {data?.userStats?.totalPostCount === 0 && (
                <div className="mt-12 text-center">
                  {/* Empty State */}
                  <div className="max-w-xs mx-auto">
                    <div className="flex justify-center space-x-4 mb-6">
                      <div className="w-16 h-16 bg-gray-100 rounded"></div>
                      <div className="w-16 h-16 bg-gray-100 rounded"></div>
                      <div className="w-16 h-16 bg-gray-100 rounded"></div>
                    </div>
                    <p className="text-gray-500">No posts yet</p>
                  </div>
                </div>
              )}
            </>
          )}
          {activeTab === "media" && (
            <>
              {media?.length > 0 && !data?.userProfile?.isSubscribed && (
                <UserMedia media={media} />
              )}
            </>
          )}

          {data?.userStats?.totalPostCount > 0 &&
            !data?.userProfile?.isSubscribed && <UnsubscribedContent />}
        </>
      )}

      {openSubscriptionModal && (
        <SubscriptionModal
          tier={openSubscriptionModal}
          onClose={() => {
            setOpenSubscriptionModal(null);
          }}
          isOpen={!!openSubscriptionModal}
          wallet={wallet}
          user={data?.userProfile}
          getUserProfile={refetch}
        />
      )}

      {/* Photo Viewer Modal */}
      <PhotoViewer
        isOpen={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        imageUrl={selectedImage}
      />

      {/* Tip Modal */}
      <TipModal
        isOpen={!!openTipModal}
        onClose={() => setOpenTipModal(null)}
        recipient={data?.userProfile}
        tipType="post_tip"
      />
    </div>
  );
}
