import useWalletStore from "../store/walletStore";
import { useEffect, useState } from "react";
import { fCurrency } from "../utils/formatNumber";
import InteractiveButton from "../components/InteractiveButton";
import Modal from "../components/Modal";
import { useAuth } from "../hooks/useAuth";
import CustomInput from "../components/CustomInput";
import { flutterwaveConfig } from "../config/flutterwave";
import { useFlutterwave, closePaymentModal } from "flutterwave-react-v3";
import { transactionService, walletService } from "../services/api";
import Transaction from "../components/Transaction";
import { BsCurrencyDollar } from "react-icons/bs";
import { AiOutlineInfoCircle } from "react-icons/ai";
import { useQuery } from "@tanstack/react-query";

const QUICK_AMOUNTS = [20000, 40000, 80000, 120000, 140000, 160000];

export default function Wallet() {
  const [showFundWalletModal, setShowFundWalletModal] = useState(false);
  const { wallet, isLoading, error, getWallet, getTransactions, fundWallet } =
    useWalletStore();
  const { user } = useAuth();
  const [amount, setAmount] = useState(0);
  useEffect(() => {
    getWallet();
  }, []);
  const config = flutterwaveConfig({
    ...user,
    amount,
    title: "Top up wallet",
    description: "Top up your wallet with NGN",
  });
  const handleFlutterPayment = useFlutterwave(config);

  const { data: transactions } = useQuery({
    queryKey: ["transactions"],
    queryFn: async () => {
      const response = await transactionService.getTransactions({
        page: 1,
        limit: 10,
      });
      console.log("TRANSACTIONS", response);
      return {
        data: response.transactions,
        total: response.total,
        currentPage: response.currentPage,
        totalPages: response.totalPages,
      };
    },
  });

  const handleQuickTopup = (quickAmount) => {
    setAmount(quickAmount);
    handleFlutterPayment({
      callback: async (response) => {
        console.log(response);
        const result = await transactionService.processDeposit({
          amount: response.amount,
          transactionReference: response,
          paymentMethod: "flutterwave",
        });
        getWallet();
        getTransactions();
        // fundWallet(result.data.wallet, result.data.transaction);
        closePaymentModal();
      },

      onClose: () => {},
    });
  };
  console.log("TRANSACTIONS", transactions);

  if (isLoading && !wallet) return <div>Loading...</div>;
  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Wallet</h1>
      </div>

      <div className="rounded-lg border border-gray-200 p-4 my-4 space-y-4">
        <div className="space-y-2">
          <h2 className="text-gray-500">Available balance:</h2>
          <div className="text-4xl font-bold">{fCurrency(wallet?.balance)}</div>
        </div>

        <div className="flex items-center gap-2">
          <span>Top up your wallet</span>
          <AiOutlineInfoCircle className="text-gray-400" />
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 my-6">
          {QUICK_AMOUNTS.map((quickAmount) => (
            <button
              key={quickAmount}
              onClick={() => {
                setAmount(quickAmount);
                handleQuickTopup(quickAmount);
              }}
              className="flex items-center justify-center gap-1 py-3 px-6 bg-gray-50 rounded-full hover:bg-gray-100 transition-colors"
            >
              <span className="text-gray-700">₦</span>
              <span className="font-semibold">
                {quickAmount.toLocaleString()}
              </span>
            </button>
          ))}
        </div>

        <div className="flex justify-center my-4">
          <button
            onClick={() => setShowFundWalletModal(true)}
            className="text-primary font-medium underline"
          >
            Custom
          </button>
        </div>

        {/* <div className="space-y-2">
          <InteractiveButton
            className="!py-2"
            onClick={() => setShowFundWalletModal(true)}
          >
            Fund wallet
          </InteractiveButton> */}
        {/* </div> */}
      </div>
      <div>
        <h2 className="text-xl font-bold mb-4">Transactions</h2>
        <Transaction transactions={transactions?.data} />
      </div>

      <Modal
        isOpen={showFundWalletModal}
        onClose={() => setShowFundWalletModal(false)}
        title="Fund wallet"
      >
        <div>
          <div>
            <p className="text-gray-500">Available balance</p>
            <p className="text-3xl text-black font-extrabold">
              {fCurrency(wallet?.balance)}
            </p>
          </div>

          <div className="space-y-2 my-4">
            <CustomInput
              type="number"
              value={amount}
              label="Amount"
              placeholder="Enter amount"
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
          <div className="mt-10 flex flex-col gap-2">
            <InteractiveButton
              disabled={amount < 1000}
              onClick={() => {
                handleFlutterPayment({
                  callback: async (response) => {
                    console.log(response);
                    const result = await transactionService.processDeposit({
                      amount: response.amount,
                      transactionReference: response,
                      paymentMethod: "flutterwave",
                    });
                    // await walletService.

                    fundWallet(result.data.wallet, result.data.transaction);
                    setShowFundWalletModal(false);
                    closePaymentModal(); // this will close the modal programmatically
                  },
                  onClose: () => {},
                });
              }}
              className="!flex items-center gap-2"
            >
              <span>Flutterwave</span>
              <span>
                <img
                  src="/icon/flutterwave.svg"
                  alt="Flutterwave"
                  className="w-4"
                />
              </span>
            </InteractiveButton>
            <InteractiveButton className="!flex items-center gap-2">
              <span>Paystack</span>
              <span>
                <img src="/icon/paystack.svg" alt="Paystack" className="w-4" />
              </span>
            </InteractiveButton>
          </div>
        </div>
      </Modal>
    </div>
  );
}
