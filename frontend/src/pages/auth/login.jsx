import { Link, useSearchParams, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useAuth } from "../../hooks/useAuth";
import toast from "react-hot-toast";
import { Icon } from "@iconify/react/dist/iconify.js";
import AuthSocial from "../../components/auth/AuthSocial";
import InteractiveButton from "../../components/InteractiveButton";
import { FiMail, FiAlertCircle, FiCheckCircle } from "react-icons/fi";
import { authService } from "../../services/api";
import { useNavigate } from "react-router-dom";

// Add validation schema
const schema = yup.object().shape({
  email: yup
    .string()
    .required("Email is required")
    .email("Must be a valid email"),
  password: yup.string().required("Password is required"),
});

const verificationSchema = yup.object().shape({
  verificationCode: yup
    .string()
    .required("Verification code is required")
    .matches(/^\d{6}$/, "Code must be 6 digits"),
});

export default function Login() {
  const [showPassword, setShowPassword] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const redirect = searchParams.get("redirect");
  const { login, verifyEmail } = useAuth();
  const [unverifiedEmail, setUnverifiedEmail] = useState("");
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const navigate = useNavigate();

  // Handle verified email state
  useEffect(() => {
    if (location.state?.verifiedEmail) {
      // Pre-fill the email field
      setValue("email", location.state.verifiedEmail);

      // Show success message
      if (location.state.message) {
        toast.success(location.state.message);
      }

      // Clear location state after using it
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Update useForm to use yup resolver
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const {
    register: registerVerification,
    handleSubmit: handleVerificationSubmit,
    formState: { errors: verificationErrors },
  } = useForm({
    resolver: yupResolver(verificationSchema),
  });

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      await login({ ...data, redirect });
      toast.success("Login successful");
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.code === "EMAIL_NOT_VERIFIED") {
        // Handle unverified email case
        setUnverifiedEmail(error.response.data.email);
        setShowVerificationModal(true);
        toast.error("Please verify your email to continue");
      } else if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.code === "ERR_NETWORK") {
        toast.error("Network error - Please check your internet connection");
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  const onVerificationSubmit = async (data) => {
    setVerificationLoading(true);

    try {
      // Don't automatically log in after verification
      const response = await authService.verifyEmail(
        unverifiedEmail,
        data.verificationCode
      );
      toast.success("Email verified successfully");
      setShowVerificationModal(false);

      // Manually log in with stored email
      try {
        // We need to get the password from the form again
        const loginData = {
          email: unverifiedEmail,
          password: document.querySelector('input[type="password"]').value,
          redirect,
        };

        await login(loginData);
      } catch (loginError) {
        // If we can't automatically log in, just close the modal
        console.error("Auto login failed:", loginError);
        // Navigate to login page - they can try manually
        navigate("/login");
      }
    } catch (error) {
      console.error("Verification error:", error);
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Verification failed. Please try again.");
      }
    } finally {
      setVerificationLoading(false);
    }
  };

  const handleResendCode = async () => {
    setResendLoading(true);

    try {
      await authService.resendVerificationCode(unverifiedEmail);
      toast.success("New verification code sent");
    } catch (error) {
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className="w-full lg:w-1/2 p-8 flex items-center justify-center">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h1 className="text-4xl font-medium mb-2">Login to your account</h1>
          <p className="text-gray-600">
            Don&apos;t have an account?{" "}
            <Link to="/register" className="text-primary hover:underline">
              Register
            </Link>
          </p>
        </div>

        <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-2">
            <input
              type="email"
              placeholder="Email"
              className={`w-full p-3 rounded-lg bg-gray-100 border ${
                errors.email ? "border-red-500" : "border-gray-200"
              } focus:outline-none focus:ring-2 focus:ring-primary-500`}
              {...register("email")}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                className={`w-full p-3 rounded-lg bg-gray-100 border ${
                  errors.password ? "border-red-500" : "border-gray-200"
                } focus:outline-none focus:ring-2 focus:ring-primary-500`}
                {...register("password")}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2"
              >
                {showPassword ? "👁️" : "👁️‍🗨️"}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{errors.password.message}</p>
            )}
          </div>

          <InteractiveButton
            type="submit"
            variant="primary"
            isLoading={submitLoading}
            className="w-full"
          >
            Login
          </InteractiveButton>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or login with</span>
            </div>
          </div>
          <AuthSocial />
        </form>
      </div>

      {/* Verification Email Modal */}
      {showVerificationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <FiAlertCircle className="w-8 h-8 text-primary" />
              </div>
              <h2 className="text-2xl font-semibold">Email Not Verified</h2>
              <p className="text-gray-600">
                Your email address ({unverifiedEmail}) has not been verified.
                Please enter the verification code sent to your email or request
                a new one.
              </p>

              <form
                onSubmit={handleVerificationSubmit(onVerificationSubmit)}
                className="w-full space-y-4"
              >
                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="Enter 6-digit code"
                    className="w-full p-3 rounded-lg bg-gray-100 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 text-center text-2xl tracking-widest"
                    maxLength="6"
                    {...registerVerification("verificationCode")}
                  />
                  {verificationErrors.verificationCode && (
                    <p className="text-red-500 text-sm">
                      {verificationErrors.verificationCode.message}
                    </p>
                  )}
                </div>

                <div className="flex flex-col gap-3 w-full">
                  <InteractiveButton
                    className="w-full"
                    type="submit"
                    variant="primary"
                    isLoading={verificationLoading}
                  >
                    Verify Email
                  </InteractiveButton>

                  <button
                    type="button"
                    className="text-primary hover:underline text-sm"
                    onClick={handleResendCode}
                    disabled={resendLoading}
                  >
                    {resendLoading ? "Sending..." : "Resend Verification Code"}
                  </button>

                  <button
                    type="button"
                    className="text-gray-500 hover:text-gray-700 text-sm"
                    onClick={() => setShowVerificationModal(false)}
                  >
                    Close
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
