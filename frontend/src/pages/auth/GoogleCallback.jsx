import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "../../hooks/useAuth";
import toast from "react-hot-toast";

export default function GoogleCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { handleGoogleCallback } = useAuth();

  useEffect(() => {
    const code = searchParams.get("code");
    if (!code) {
      toast.error("Authentication failed");
      navigate("/login");
      return;
    }

    const processGoogleCallback = async () => {
      try {
        await handleGoogleCallback(code);
        toast.success("Successfully logged in with Google!");
        navigate("/");
      } catch (error) {
        toast.error(error?.response?.data?.message || "Authentication failed");
        navigate("/login");
      }
    };

    processGoogleCallback();
  }, [searchParams, navigate, handleGoogleCallback]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold mb-2">
          Completing Authentication
        </h2>
        <p className="text-gray-600">Please wait while we log you in...</p>
      </div>
    </div>
  );
}
