import { Link, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useAuth } from "../../hooks/useAuth";
import toast from "react-hot-toast";
import AuthSocial from "../../components/auth/AuthSocial";
import InteractiveButton from "../../components/InteractiveButton";
import { FiMail } from "react-icons/fi";
import { authService } from "../../services/api";
// Add validation schema
const schema = yup.object().shape({
  displayName: yup
    .string()
    .required("Display name is required")
    .min(3, "Display name must be at least 3 characters"),
  email: yup
    .string()
    .required("Email is required")
    .email("Must be a valid email"),
  password: yup
    .string()
    .required("Password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    ),
  confirmPassword: yup
    .string()
    .required("Please confirm your password")
    .oneOf([yup.ref("password")], "Passwords must match"),
});

const verificationSchema = yup.object().shape({
  verificationCode: yup
    .string()
    .required("Verification code is required")
    .matches(/^\d{6}$/, "Code must be 6 digits"),
});

export default function Register() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { register: registerUser, verifyEmail } = useAuth();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState("");
  const navigate = useNavigate();

  // Update useForm to use yup resolver
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const {
    register: registerVerification,
    handleSubmit: handleVerificationSubmit,
    formState: { errors: verificationErrors },
  } = useForm({
    resolver: yupResolver(verificationSchema),
  });

  const onSubmit = async (data) => {
    setSubmitLoading(true);

    try {
      await registerUser(data).then((response) => {
        console.log(response, "response");
        setRegisteredEmail(data.email);
        toast.success("Registration successful");
        setShowVerificationModal(true);
      });
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  const onVerificationSubmit = async (data) => {
    setVerificationLoading(true);

    try {
      const response = await authService.verifyEmail(
        registeredEmail,
        data.verificationCode
      );
      toast.success("Email verified successfully");

      navigate("/login", {
        state: {
          verifiedEmail: registeredEmail,
          message: "Your email has been verified. You can now log in.",
        },
      });
    } catch (error) {
      console.error("Verification error:", error);
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Verification failed. Please try again.");
      }
    } finally {
      setVerificationLoading(false);
    }
  };

  const handleResendCode = async () => {
    setResendLoading(true);

    try {
      await authService.resendVerificationCode(registeredEmail);
      toast.success("New verification code sent");
    } catch (error) {
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className="w-full lg:w-1/2 p-8 flex items-center justify-center">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h1 className="text-4xl font-medium mb-2">Create an account</h1>
          <p className="text-gray-600">
            Already have an account?{" "}
            <Link to="/login" className="text-primary hover:underline">
              Log in
            </Link>
          </p>
        </div>

        <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-2">
            <input
              type="text"
              placeholder="Display name"
              className={`w-full p-3 rounded-lg bg-gray-100 border ${
                errors.displayName ? "border-red-500" : "border-gray-200"
              } focus:outline-none focus:ring-2 focus:ring-primary-500`}
              {...register("displayName")}
            />
            {errors.displayName && (
              <p className="text-red-500 text-sm">
                {errors.displayName.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <input
              type="email"
              placeholder="Email"
              className={`w-full p-3 rounded-lg bg-gray-100 border ${
                errors.email ? "border-red-500" : "border-gray-200"
              } focus:outline-none focus:ring-2 focus:ring-primary-500`}
              {...register("email")}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                className={`w-full p-3 rounded-lg bg-gray-100 border ${
                  errors.password ? "border-red-500" : "border-gray-200"
                } focus:outline-none focus:ring-2 focus:ring-primary-500`}
                {...register("password")}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2"
              >
                {showPassword ? "👁️" : "👁️‍🗨️"}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{errors.password.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm your password"
                className={`w-full p-3 rounded-lg bg-gray-100 border ${
                  errors.confirmPassword ? "border-red-500" : "border-gray-200"
                } focus:outline-none focus:ring-2 focus:ring-primary-500`}
                {...register("confirmPassword")}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2"
              >
                {showConfirmPassword ? "👁️" : "👁️‍🗨️"}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-500 text-sm">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>

          <div className="flex items-center gap-2">
            <p className="text-sm text-gray-600">
              By signing up, you agree to our{" "}
              <Link to="/terms" className="text-primary hover:underline">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link to="/privacy" className="text-primary hover:underline">
                Privacy Policy
              </Link>{" "}
              and confirm that you're at least 18 years old.
            </p>
          </div>

          <InteractiveButton
            className="w-full"
            type="submit"
            variant="primary"
            isLoading={submitLoading}
          >
            Create account
          </InteractiveButton>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">
                Or register with
              </span>
            </div>
          </div>

          <AuthSocial />
        </form>
      </div>

      {/* Verification Email Modal */}
      {showVerificationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <FiMail className="w-8 h-8 text-primary" />
              </div>
              <h2 className="text-2xl font-semibold">Check your email</h2>
              <p className="text-gray-600">
                We've sent a verification code to your email address. Please
                enter the code below to verify your account.
              </p>

              <form
                onSubmit={handleVerificationSubmit(onVerificationSubmit)}
                className="w-full space-y-4"
              >
                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="Enter 6-digit code"
                    className="w-full p-3 rounded-lg bg-gray-100 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 text-center text-2xl tracking-widest"
                    maxLength="6"
                    {...registerVerification("verificationCode")}
                  />
                  {verificationErrors.verificationCode && (
                    <p className="text-red-500 text-sm">
                      {verificationErrors.verificationCode.message}
                    </p>
                  )}
                </div>

                <div className="flex flex-col gap-3 w-full">
                  <InteractiveButton
                    className="w-full"
                    type="submit"
                    variant="primary"
                    isLoading={verificationLoading}
                  >
                    Verify Email
                  </InteractiveButton>

                  <button
                    type="button"
                    className="text-primary hover:underline text-sm"
                    onClick={handleResendCode}
                    disabled={resendLoading}
                  >
                    {resendLoading ? "Sending..." : "Resend Code"}
                  </button>

                  <button
                    type="button"
                    className="text-gray-500 hover:text-gray-700 text-sm"
                    onClick={() => setShowVerificationModal(false)}
                  >
                    Close
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
