import { Outlet, Link, useRoutes } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import Dashboard from "../pages/dashboard";
import {
  Chats,
  ChatDetail,
  Collections,
  Subscribers,
  AddCard,
  MyProfile,
  Notifications,
  UserSubscriptions,
  CreatorSubscribers,
} from "../pages";
import AuthGuard from "../guards/AuthGuard";
import Login from "../pages/auth/login";
import Register from "../pages/auth/register";
import AuthLayout from "../layout/Auth";
import GuestGuard from "../guards/GuestGuard";
import EditProfile from "../pages/dashboard/edit-profile";
import Suggestions, {
  SuggestionLayout,
} from "../components/dashboard/suggestions";
import UserProfile from "../pages/UserProfile";
import PostDetail from "../pages/post-detail";
import ManageSubscription from "../pages/creator/manage-subscription";
import SettingLayout from "../components/SettingNav";
import WithdrawalAccount from "../pages/settings/withdrawal-account";
import ChangePassword from "../pages/settings/change-password";
import Payment from "../pages/settings/payment";
import Wallet from "../pages/wallet";
import AddPost from "../pages/creator/add-post";

import CreatorDashboard from "../pages/creator/creator-dashboard";
import CreatorMessages from "../pages/creator/messages";
import CreatorPosts from "../pages/creator/posts";
import CreatorLayout from "../layout/CreatorLayout";
import LiveStream from "../pages/creator/live-stream";
import Stories from "../pages/creator/stories";
import Account from "../pages/creator/account";
import PaymentDetails from "../pages/creator/payment-details";
import CreatorTransactions from "../pages/creator/transactions";

import MainLayout from "../layout/MainLayout";
import OnboardingGuard from "../guards/OnboardingGuard";

export default function Routes() {
  let routes = [
    {
      path: "/",
      element: (
        <AuthGuard>
          <Layout />
        </AuthGuard>
      ),
      children: [
        {
          index: true,
          element: (
            <div className="flex gap-4 w-full mx-auto">
              <Helmet>
                <title>Home | Erossphere</title>
              </Helmet>
              <div className="w-full">
                <Dashboard />
              </div>

              <SuggestionLayout />
            </div>
          ),
        },
        {
          path: "subscriptions",
          element: (
            <>
              <Helmet>
                <title>User Subscriptions | Erossphere</title>
              </Helmet>
              <UserSubscriptions />
            </>
          ),
        },
        {
          path: "post/:id",
          element: (
            <div className="flex gap-4 w-full mx-auto">
              <Helmet>
                <title>Post Details | Erossphere</title>
              </Helmet>
              <div className="w-full">
                <PostDetail />
              </div>
              {/* <div className="w-[490px] lg:w-[490px] sm:hidden md:block">
                <Suggestions />
              </div> */}
            </div>
          ),
        },
        {
          path: "wallet",
          element: (
            <>
              <Helmet>
                <title>Wallet | Erossphere</title>
              </Helmet>
              <Wallet />
            </>
          ),
        },
        {
          path: "chats",
          element: (
            <div className="flex h-[calc(100vh-80px)] bg-gray-100 overflow-hidden">
              <Helmet>
                <title>Messages | Erossphere</title>
              </Helmet>
              <div className="flex w-full">
                <Chats />
                <Outlet />
              </div>
            </div>
          ),
        },
        {
          path: "chats/chat/:id",
          element: (
            <div className="flex h-[calc(100vh-80px)] bg-gray-100 overflow-hidden">
              <Helmet>
                <title>Chat | Erossphere</title>
              </Helmet>
              <div className="flex w-full">
                <div className="md:block hidden">
                  <Chats />
                </div>
                <ChatDetail />
              </div>
            </div>
          ),
        },
        {
          path: "collections",
          element: (
            <>
              <Helmet>
                <title>Collections | Erossphere</title>
              </Helmet>
              <Collections />
            </>
          ),
        },
        {
          path: "subscribers",
          element: (
            <>
              <Helmet>
                <title>Subscribers | Erossphere</title>
              </Helmet>
              <Subscribers />
            </>
          ),
        },
        {
          path: "add-card",
          element: (
            <>
              <Helmet>
                <title>Add Payment Card | Erossphere</title>
              </Helmet>
              <AddCard />
            </>
          ),
        },
        {
          path: "profile",
          element: (
            <>
              <Helmet>
                <title>My Profile | Erossphere</title>
              </Helmet>
              <MyProfile />
            </>
          ),
        },
        {
          path: "notifications",
          element: (
            <>
              <Helmet>
                <title>Notifications | Erossphere</title>
              </Helmet>
              <Notifications />
            </>
          ),
        },
        {
          path: "add-post",
          element: (
            <>
              <Helmet>
                <title>Create Post | Erossphere</title>
              </Helmet>
              <AddPost />
            </>
          ),
        },
        { path: "settings", element: <SettingLayout /> },
        {
          path: "settings",
          element: (
            <div className=" gap-10 h-full relative flex">
              <Helmet>
                <title>Settings | Erossphere</title>
              </Helmet>
              <div className="w-[360px] hidden md:block">
                <SettingLayout />
              </div>
              <div className="w-full">
                <Outlet />
              </div>
            </div>
          ),
          children: [
            {
              path: "profile",
              element: (
                <>
                  <Helmet>
                    <title>Edit Profile | Erossphere</title>
                  </Helmet>
                  <EditProfile />
                </>
              ),
            },
            {
              path: "manage-subscription",
              element: (
                <>
                  <Helmet>
                    <title>Manage Subscription | Erossphere</title>
                  </Helmet>
                  <ManageSubscription />
                </>
              ),
            },
            { path: "withdrawal", element: <ManageSubscription /> },
            { path: "password", element: <ManageSubscription /> },
            { path: "help-and-support", element: <ManageSubscription /> },
            { path: "account", element: <ManageSubscription /> },
            {
              path: "withdrawal-account",
              element: (
                <>
                  <Helmet>
                    <title>Withdrawal Account | Erossphere</title>
                  </Helmet>
                  <WithdrawalAccount />
                </>
              ),
            },
            {
              path: "change-password",
              element: (
                <>
                  <Helmet>
                    <title>Change Password | Erossphere</title>
                  </Helmet>
                  <ChangePassword />
                </>
              ),
            },
            {
              path: "payment",
              element: (
                <>
                  <Helmet>
                    <title>Payment Settings | Erossphere</title>
                  </Helmet>
                  <Payment />
                </>
              ),
            },
          ],
        },
        // {
        //   path: ":username",
        //   element: (
        //     <div className="flex gap-4 w-full relative">
        //       <UserProfile />

        //       <SuggestionLayout />
        //     </div>
        //   ),
        // },
        { path: "*", element: <NoMatch /> },
      ],
    },
    {
      path: "",
      element: (
        <GuestGuard>
          <AuthLayout />
        </GuestGuard>
      ),
      children: [
        {
          path: "login",
          element: (
            <>
              <Helmet>
                <title>Login | Erossphere</title>
              </Helmet>
              <Login />
            </>
          ),
        },
        {
          path: "register",
          element: (
            <>
              <Helmet>
                <title>Register | Erossphere</title>
              </Helmet>
              <Register />
            </>
          ),
        },
      ],
    },
    {
      path: ":username",
      element: (
        <div className="flex gap-4 w-full relative">
          <UserProfile />

          <SuggestionLayout />
        </div>
      ),
    },

    {
      path: "creator",
      element: <CreatorLayout />,
      children: [
        {
          path: "",
          element: (
            <>
              <Helmet>
                <title>Creator Dashboard | Erossphere</title>
              </Helmet>
              <CreatorDashboard />
            </>
          ),
        },
        {
          path: "messages",
          element: (
            <>
              <Helmet>
                <title>Creator Messages | Erossphere</title>
              </Helmet>
              <CreatorMessages />
            </>
          ),
        },
        {
          path: "posts",
          element: (
            <>
              <Helmet>
                <title>Creator Posts | Erossphere</title>
              </Helmet>
              <CreatorPosts />
            </>
          ),
        },
        // { path: "settings", element: <CreatorSettings /> },
        {
          path: "add-post",
          element: (
            <>
              <Helmet>
                <title>Create Post | Erossphere</title>
              </Helmet>
              <AddPost />
            </>
          ),
        },
        {
          path: "manage-subscription",
          element: (
            <>
              <Helmet>
                <title>Manage Creator Subscription | Erossphere</title>
              </Helmet>
              <ManageSubscription />
            </>
          ),
        },
        {
          path: "live-stream",
          element: (
            <>
              <Helmet>
                <title>Live Stream | Erossphere</title>
              </Helmet>
              <LiveStream />
            </>
          ),
        },
        {
          path: "subscribers",
          element: (
            <>
              <Helmet>
                <title>Creator Subscribers | Erossphere</title>
              </Helmet>
              <CreatorSubscribers />
            </>
          ),
        },

        {
          path: "stories",
          element: (
            <>
              <Helmet>
                <title>Stories | Erossphere</title>
              </Helmet>
              <Stories />
            </>
          ),
        },
        {
          path: "messages",
          element: (
            <>
              <Helmet>
                <title>Messages | Erossphere</title>
              </Helmet>
              <CreatorMessages />
            </>
          ),
        },
        {
          path: "notifications",
          element: (
            <>
              <Helmet>
                <title>Notifications | Erossphere</title>
              </Helmet>
              <Notifications />
            </>
          ),
        },
        {
          path: "transactions",
          element: (
            <>
              <Helmet>
                <title>Transactions | Erossphere</title>
              </Helmet>
              <CreatorTransactions />
            </>
          ),
        },
        {
          path: "account",
          element: (
            <>
              <Helmet>
                <title>Account | Erossphere</title>
              </Helmet>
              <Account />
            </>
          ),
        },
        {
          path: "payment-details",
          element: (
            <>
              <Helmet>
                <title>Payment details | Erossphere</title>
              </Helmet>
              <PaymentDetails />
            </>
          ),
        },
      ],
    },
  ];

  let element = useRoutes(routes);

  return <div>{element}</div>;
}

function Layout() {
  return (
    <OnboardingGuard>
      <MainLayout />
    </OnboardingGuard>
  );
}

function NoMatch() {
  return (
    <div>
      <Helmet>
        <title>404 - Page Not Found | Erossphere</title>
      </Helmet>
      <h2>It looks like you&apos;re lost...</h2>
      <p>
        <Link to="/">Go to the home page</Link>
      </p>
    </div>
  );
}
