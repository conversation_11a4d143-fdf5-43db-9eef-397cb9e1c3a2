import axios from "axios";
import api from "../utils/axios";

// Auth services
export const authService = {
  register: async (data) => {
    const response = await api.post("/auth/register", data);
    return response.data;
  },

  login: async (email, password) => {
    const response = await api.post("/auth/login", { email, password });
    return response.data;
  },
  editProfile: async (data) => {
    const response = await api.patch("/auth/profile", data);
    return response.data;
  },
  changePassword: async (data) => {
    const response = await api.patch("/auth/change-password", data);
    return response.data;
  },

  logout: async () => {
    const response = await api.post("/auth/logout");
    return response.data;
  },

  verifyEmail: async (email, code) => {
    const response = await api.post("/auth/verify-email", {
      email,
      verificationCode: code,
    });
    return response.data;
  },
  resendVerificationCode: async (email) => {
    const response = await api.post("/auth/resend-verification", { email });
    return response.data;
  },

  resetPassword: async (token, data) => {
    const response = await api.post(`/auth/reset-password/${token}`, data);
    return response.data;
  },
  forgotPassword: async (data) => {
    const response = await api.post("/auth/forgot-password", data);
    return response.data;
  },
  checkUsername: (username) => {
    return api.get(`/users/check-username?username=${username}`);
  },
  checkUsernamePublic: (username) => {
    return api.get(`/users/check-username-public?username=${username}`);
  },
  getSuggestedCreators: async () => {
    const response = await api.get("/users/suggested-creators");
    return response.data;
  },
};

export const postService = {
  getPosts: async (query) => {
    const response = await api.get("/posts", { params: query });
    return response.data;
  },
  getPost: async (postId) => {
    const response = await api.get(`/posts/${postId}`);
    return response.data;
  },
  getUserMedia: async (userId) => {
    const response = await api.get(`/posts/media/user/${userId}`);
    return response.data;
  },
  getPostMedia: async (postId) => {
    const response = await api.get(`/posts/${postId}/media`);
    return response.data;
  },
  createPost: async (postData) => {
    return await api.post("/posts", postData);
  },
  updatePost: async (postId, data) => {
    const response = await api.patch(`/posts/${postId}`, data);
    return response.data;
  },
  deletePost: async (postId) => {
    const response = await api.delete(`/posts/${postId}`);
    return response.data;
  },
  likePost: async (postId) => {
    return api.patch(`/posts/${postId}/like`);
  },
  dislikePost: async (postId) => {
    const response = await api.post(`/posts/${postId}/dislike`);
    return response.data;
  },
  addComment: async (postId, comment) => {
    const response = await api.post(`/comments/post/${postId}`, {
      content: comment,
    });
    return response.data;
  },
  getSubscribedPosts: async () => {
    try {
      const response = await api.get("/posts/subscribed");
      return response.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.message || "Failed to fetch subscribed posts"
      );
    }
  },
  getUserPosts: async (userId) => {
    const response = await api.get(`/posts/user/${userId}`);
    return response.data;
  },
};

export const userService = {
  getUserProfile: async (username) => {
    const response = await api.get(`/users/${username}`);
    return response.data;
  },
  getCreatorStats: async (username) => {
    const response = await api.get(`/users/${username}/stats`);
    return response.data;
  },
  getUserPosts: async (username) => {
    const response = await api.get(`/users/${username}/posts`);
    return response.data;
  },
  getUserFollowers: async (username) => {
    const response = await api.get(`/users/${username}/followers`);
    return response.data;
  },
  getUserFollowing: async (username) => {
    const response = await api.get(`/users/${username}/following`);
    return response.data;
  },
  getUsers: async () => {
    const response = await api.get("/users");
    return response.data;
  },
  getRecommendedUsers: async () => {
    const response = await api.get("/users/recommended");
    return response.data;
  },
  searchUsers: async (searchTerm) => {
    const response = await api.get(`/users/search?q=${searchTerm}`);
    return response.data;
  },
  getSuggestedCreators: async () => {
    const response = await api.get("/users/suggested-creators");
    return response.data;
  },
  trackPageView: async (username, sessionId) => {
    const response = await api.post(`/users/${username}/track-view`, {
      sessionId,
    });
    return response.data;
  },
  getCreatorSubscribers: async (username, query) => {
    const response = await api.get(`/users/${username}/subscribers`, {
      params: query,
    });
    return response.data;
  },
};

export const commentService = {
  createComment: async (postId, content) => {
    const response = await api.post(`/comments`, { postId, content });
    return response.data;
  },

  likeComment: async (commentId) => {
    const response = await api.post(`/comments/${commentId}/like`);
    return response.data;
  },

  getComments: async (postId) => {
    const response = await api.get(`/comments/post/${postId}`);
    return response.data;
  },
  createReply: async (commentId, content) => {
    const response = await api.post(`/comments/${commentId}/reply`, {
      content,
    });
    return response.data;
  },
  getCommentReplies: async (commentId) => {
    const response = await api.get(`/comments/${commentId}/replies`);
    return response.data;
  },
};

export const subscriptionService = {
  getSubscriptionTiers: async () => {
    const response = await api.get("/subscriptions");
    return response.data;
  },
  subscribeToCreator: async (data) => {
    const response = await api.post(`/subscriptions/subscribe`, data);
    return response.data;
  },
  getSubscribers: async (status) => {
    const response = await api.get(
      `/subscriptions/subscribers?status=${status}`
    );
    return response.data;
  },
  getMySubscriptions: async (filters) => {
    const response = await api.get("/subscriptions/my-subscriptions", {
      params: filters,
    });
    return response.data;
  },
  cancelSubscription: async (subscriptionId) => {
    const response = await api.post(`/subscriptions/${subscriptionId}/cancel`);
    return response.data;
  },
  checkExistingSubscription: async (creatorId) => {
    const response = await api.get(
      `/subscriptions/check-existing-subscription/${creatorId}`
    );
    return response.data;
  },
};

export const notificationService = {
  getNotifications: async (page = 1, filters = {}) => {
    const params = new URLSearchParams({
      page,
      ...filters, // This can include read/unread status, type, date range, etc.
    });

    const response = await api.get(`/notifications?${params}`);
    return response.data;
  },
  markNotificationAsRead: async (notificationId) => {
    const response = await api.put(`/notifications/${notificationId}/read`);
    return response.data;
  },
};

export const walletService = {
  getWallet: async () => {
    const response = await api.get("/wallet");
    return response.data;
  },
  creditWallet: async (data) => {
    const response = await api.post("/wallet/credit", data);
    return response.data;
  },
  debitWallet: async (amount) => {
    const response = await api.post("/wallet/debit", { amount });
    return response.data;
  },
  getTransactions: async () => {
    const response = await api.get("/wallet/transactions");
    return response.data;
  },
  addPendingCredit: async (amount) => {
    const response = await api.post("/wallet/pending-credit", { amount });
    return response.data;
  },
  confirmPendingTransaction: async (transactionId) => {
    const response = await api.post("/wallet/confirm-transaction", {
      transactionId,
    });
    return response.data;
  },
};

export const transactionService = {
  getTransactions: async ({ page = 1, limit = 10 }) => {
    const response = await api.get("/transactions/history", {
      params: { page, limit },
    });
    return response.data;
  },
  getCreatorEarnings: async (period = "all") => {
    const response = await api.get("/transactions/earnings", {
      params: { period },
    });
    return response.data;
  },
  getPendingWithdrawals: async () => {
    const response = await api.get("/transactions/pending-withdrawals");
    return response.data;
  },
  cancelWithdrawal: async (transactionId) => {
    const response = await api.post(
      `/transactions/withdrawal/${transactionId}/cancel`
    );
    return response.data;
  },
  processDeposit: async (data) => {
    const response = await api.post("/transactions/deposit", data);
    return response.data;
  },
  getWithdrawalAccounts: async () => {
    const response = await api.get("/transactions/withdrawal-accounts");
    return response.data;
  },
};

export const uploadService = {
  uploadMultiple: async (formData) => {
    const config = {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        // You could add a progress state if you want to show upload progress
        console.log(`Upload Progress: ${percentCompleted}%`);
      },
    };

    return await api.post("/upload/multiple", formData, config);
  },
  uploadSingle: async (data) => {
    const response = await api.post("/upload/single", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },
};

export const storyService = {
  getStories: async () => {
    const response = await api.get("/stories/feed");
    return response.data;
  },
  getUserStories: async (userId) => {
    const response = await api.get(`/stories/user/${userId}`);
    return response.data;
  },
  createStory: async (data) => {
    const response = await api.post("/stories", data);
    return response.data;
  },
  viewStory: async (storyId) => {
    const response = await api.post(`/stories/${storyId}/view`);
    return response.data;
  },
  deleteStory: async (storyId) => {
    const response = await api.delete(`/stories/${storyId}`);
    return response.data;
  },
  viewStory: async (storyId) => {
    const response = await api.post(`/stories/${storyId}/view`);
    return response.data;
  },
  // getStory: async (storyId) => {
  //   const response = await api.get(`/stories/${storyId}`);
  //   return response.data;
  // },
};

export const chatService = {
  getConversations: async () => {
    const response = await api.get("/chat/conversations");
    return response.data;
  },
  getMessages: async (conversationId) => {
    const response = await api.get(`/chat/${conversationId}`);
    return response.data;
  },
  createConversation: async (data) => {
    const response = await api.post("/chat/conversation", data);
    return response.data;
  },
  // get messages for a conversation
  getMessages: async (conversationId) => {
    const response = await api.get(`/chat/messages/${conversationId}`);
    return response.data;
  },
  sendMessage: async (data) => {
    const response = await api.post(`/chat/messages`, data);
    return response.data;
  },
  startNewConversation: async (receiverId) => {
    const response = await api.post("/chat/conversations", { receiverId });
    return response.data;
  },
};

export const automationService = {
  getAutomations: async () => {
    const response = await api.get("/automations");
    return response.data;
  },
  createAutomation: async (data) => {
    const response = await api.post("/automations", data);
    return response.data;
  },
  updateAutomation: async (id, data) => {
    const response = await api.patch(`/automations/${id}`, data);
    return response.data;
  },
  deleteAutomation: async (id) => {
    const response = await api.delete(`/automations/${id}`);
    return response.data;
  },
  toggleAutomation: async (id) => {
    const response = await api.patch(`/automations/${id}/toggle`);
    return response.data;
  },
};

export const banksService = {
  getBanks: async (country) => {
    const response = await axios.get(
      `https://api.ravepay.co/v2/banks/${country}`
    );
    return response.data;
  },
  getCountries: async () => {
    const response = await axios.get("https://api.paystack.co/country", {
      headers: {
        Authorization: `Bearer ${import.meta.env.VITE_PAYSTACK_SECRET_KEY}`,
      },
    });
    return response.data;
  },
  verifyBankDetails: async (accountNumber, bankCode) => {
    const response = await axios.post(
      `https://api.paystack.co/bank/resolve?account_number=${accountNumber}&bank_code=${bankCode}`,
      {
        headers: {
          Authorization: `Bearer ${import.meta.env.VITE_PAYSTACK_SECRET_KEY}`,
        },
      }
    );
    return response.data;
  },
  getBanks: async (country) => {
    const response = await axios.get(
      `https://api.paystack.co/bank?country=${country}`,
      {
        headers: {
          Authorization: `Bearer ${import.meta.env.VITE_PAYSTACK_SECRET_KEY}`,
        },
      }
    );
    return response.data;
  },
};

export const withdrawalAccountService = {
  createWithdrawalAccount: async (data) => {
    const response = await api.post("/withdrawal-accounts", data);
    return response.data;
  },
  getAllWithdrawalAccounts: async () => {
    const response = await api.get("/withdrawal-accounts");
    return response.data;
  },
  getWithdrawalAccount: async (id) => {
    const response = await api.get(`/withdrawal-accounts/${id}`);
    return response.data;
  },
  updateWithdrawalAccount: async (id, data) => {
    const response = await api.patch(`/withdrawal-accounts/${id}`, data);
    return response.data;
  },
  deleteWithdrawalAccount: async (id) => {
    const response = await api.delete(`/withdrawal-accounts/${id}`);
    return response.data;
  },
  setDefaultWithdrawalAccount: async (id) => {
    const response = await api.patch(`/withdrawal-accounts/${id}/set-default`);
    return response.data;
  },
};
