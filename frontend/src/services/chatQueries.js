import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { chatService } from "./api";
import { useChatMessages } from "../context/ChatMessagesContext";

// Query keys
export const chatKeys = {
  all: ["chats"],
  conversations: () => [...chatKeys.all, "conversations"],
  conversation: (id) => [...chatKeys.conversations(), id],
  messages: (conversationId) => [
    ...chatKeys.conversation(conversationId),
    "messages",
  ],
};

// Queries
export const useConversations = () => {
  return useQuery({
    queryKey: chatKeys.conversations(),
    queryFn: async () => {
      const response = await chatService.getConversations();
      return {
        conversations: response.conversations,
        totalUnreadCount: response.totalUnreadCount,
        unreadCounts: response.conversations.reduce((acc, conv) => {
          acc[conv._id] = conv.unreadCount || 0;
          return acc;
        }, {}),
      };
    },
    staleTime: 0, // Always fetch fresh data
    cacheTime: Infinity, // Keep the data cached
  });
};

export const useMessages = (conversationId) => {
  const queryClient = useQueryClient();
  return useQuery({
    queryKey: chatKeys.messages(conversationId),
    queryFn: async () => {
      const response = await chatService.getMessages(conversationId);
      return response;
    },
    onSuccess: (data) => {
      // Update unread counts in conversations query
      queryClient.setQueryData(chatKeys.conversations(), (old) => {
        if (!old) return old;

        const newUnreadCounts = { ...old.unreadCounts };
        newUnreadCounts[conversationId] = 0;

        const updatedConversations = old.conversations.map((conv) => {
          if (conv._id === conversationId) {
            return { ...conv, unreadCount: 0 };
          }
          return conv;
        });

        const totalUnread = Object.values(newUnreadCounts).reduce(
          (sum, count) => sum + count,
          0
        );

        return {
          conversations: updatedConversations,
          unreadCounts: newUnreadCounts,
          totalUnreadCount: totalUnread,
        };
      });
    },
    enabled: !!conversationId,
  });
};

// Mutations
export const useSendMessage = () => {
  const queryClient = useQueryClient();
  const { sendMessage: socketSendMessage } = useChatMessages();

  return useMutation({
    mutationFn: async ({
      receiverId,
      content,
      conversationId,
      messageType,
      media,
    }) => {
      const response = await chatService.sendMessage({
        receiverId,
        content,
        conversationId,
        messageType,
        media,
      });

      // Send the message through socket
      socketSendMessage({
        type: "message",
        payload: response,
      });

      return response;
    },
    onSuccess: (data, variables) => {
      // Update messages in the cache
      queryClient.setQueryData(
        chatKeys.messages(variables.conversationId),
        (old) => {
          if (!old) return { messages: [data] };
          return {
            ...old,
            messages: [...old.messages, data],
          };
        }
      );
    },
  });
};

export const useStartNewConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (receiverId) => {
      const response = await chatService.startNewConversation(receiverId);
      return response;
    },
    onSuccess: (data) => {
      // Update conversations list in the cache
      queryClient.setQueryData(chatKeys.conversations(), (old) => {
        if (!old)
          return {
            conversations: [data],
            unreadCounts: {},
            totalUnreadCount: 0,
          };
        return {
          ...old,
          conversations: [data, ...old.conversations],
        };
      });
    },
  });
};
