import { format, formatDistanceToNow, differenceInMinutes, differenceInHours, differenceInDays } from 'date-fns';
import { convertToLocalTime } from 'date-fns-timezone';
// ----------------------------------------------------------------------

const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
// export const formatDate = (date) => {
//   const dateTmp = Date.parse(date.toLocaleString());
//   const localDate = convertToLocalTime(dateTmp, {
//     timeZone: timezone
//   });
//   return format(localDate, DEFAULT_DATE_FORMAT);
// };

export function fDate(date) {
  return format(new Date(date), 'dd MMMM yyyy');
}

export function fDateTime(date) {
  const dateTmp = Date.parse(date.toLocaleString());
  const localDate = convertToLocalTime(dateTmp, {
    timeZone: timezone
  });

  return format(localDate, 'dd MMM yyyy HH:mm');
}

export function fDateTimeSuffix(date) {
  const dateTmp = Date.parse(date.toLocalString());
  const localDate = convertToLocalTime(dateTmp, { timeZone: timezone });
  return format(localDate, 'dd/MM/yyyy hh:mm p');
}

export function fToNow(date) {
  return formatDistanceToNow(new Date(date), {
    addSuffix: true
  });
}

export function formatLastSeen(dateString) {
  if (!dateString) return "";
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = differenceInMinutes(now, date);
  const diffInHours = differenceInHours(now, date);
  const diffInDays = differenceInDays(now, date);

  if (diffInMinutes < 1) return "just now";
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInHours < 24) return `${diffInHours}h ago`;
  return `${diffInDays}d ago`;
}
