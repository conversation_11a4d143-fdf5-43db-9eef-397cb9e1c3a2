import {
  differenceInMinutes,
  differenceInHours,
  differenceInDays,
} from "date-fns";

export const triggerEvents = [
  { value: "WELCOME_MESSAGE", label: "Welcome message for subscribers" },
  { value: "TIP_RECEIVED", label: "Fan has tipped" },
  {
    value: "SUBSCRIPTION_EXPIRED",
    label: "Fan's subscription has expired",
  },
  {
    value: "RESUBSCRIBED",
    label: "Expired subscriber has re-subscribed",
  },
];

// Format last seen time
export const formatLastSeen = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = differenceInMinutes(now, date);
  const diffInHours = differenceInHours(now, date);
  const diffInDays = differenceInDays(now, date);

  if (diffInMinutes < 1) return "just now";
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInHours < 24) return `${diffInHours}h ago`;
  return `${diffInDays}d ago`;
};

export const getTipDescription = (tipType) => {
  switch (tipType) {
    case "post_tip":
      return "Post Tip";
    case "livestream_tip":
      return "Livestream Tip";
    case "story_tip":
      return "Story Tip";
    case "chat_tip":
      return "Chat Tip";
    case "profile_tip":
      return "Profile Tip";
    case "video_tip":
      return "Video Tip";
    case "audio_tip":
      return "Audio Tip";
  }
};
