import io from "socket.io-client";

let socket;

export const initializeSocket = (user) => {
  if (!socket && user) {
    socket = io(import.meta.env.VITE_BASE_API_URL || "http://localhost:3001", {
      path: "/socket.io",
      transports: ["websocket", "polling"],
      withCredentials: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 10000,
    });

    socket.on("connect_timeout", () => {
      console.error("Socket connection timeout");
    });

    socket.io.on("error", (error) => {
      console.error("Socket error:", error);
    });
  }
  return socket;
};

export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

export const getSocket = () => socket;

export const emitMessage = (receiverId, message) => {
  if (!socket?.connected) {
    console.error("Socket not connected while trying to emit message");
    return;
  }

  socket.emit("private message", { receiverId, message }, (error) => {
    if (error) {
      console.error("Error sending message through socket:", error);
    }
  });
};

// Global message handler setup
export const setupGlobalMessageHandler = (addMessage) => {
  if (socket) {
    // Remove any existing listeners to prevent duplicates
    socket.off("new message");

    // Add new listener with message deduplication
    const processedMessages = new Set();

    socket.on("new message", (message) => {
      // Check if we've already processed this message
      if (message._id && !processedMessages.has(message._id)) {
        console.log("Global message received:", message);
        processedMessages.add(message._id);
        addMessage(message);

        // Clean up old messages from the Set after 5 seconds
        setTimeout(() => {
          processedMessages.delete(message._id);
        }, 5000);
      }
    });
  }
};

export const removeGlobalMessageHandler = () => {
  if (socket) {
    socket.off("new message");
  }
};

export const setupSocketListeners = (user, handlers) => {
  if (socket && user) {
    // Remove existing listeners
    socket.off("connect");
    socket.off("disconnect");
    socket.off("connect_error");
    socket.off("user_status_change");
    socket.off("messages_read");

    // Add new listeners
    socket.on("connect", () => {
      console.log("Socket connected");
      socket.emit("join", user._id);
      handlers.onConnect?.();
    });

    socket.on("disconnect", () => {
      console.log("Socket disconnected");
      handlers.onDisconnect?.();
    });

    socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
      handlers.onError?.(error);
    });

    socket.on("user_status_change", (data) => {
      handlers.onUserStatusChange?.(data);
    });

    socket.on("messages_read", (data) => {
      handlers.onMessagesRead?.(data);
    });
  }
};
