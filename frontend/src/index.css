@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;
body {
  @apply bg-transparent;
  font-family: "DM Sans", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: ThepeerSans;
  src: url(/font/ThepeerSans.woff2) format("woff2"),url(/font/ThepeerSans.woff) format("woff"),url(/font/ThepeerSans.ttf) format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap
}

@font-face {
  font-family: Satoshi;
  src: url(/font/Satoshi-Regular.woff2) format("woff2"),url(/font/Satoshi-Regular.woff) format("woff"),url(/font/Satoshi-Regular.ttf) format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap
}

@font-face {
  font-family: SatoshiMedium;
  src: url(/font/Satoshi-Medium.woff2) format("woff2"),url(/font/Satoshi-Medium.woff) format("woff"),url(/font/Satoshi-Medium.ttf) format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap
}

@font-face {
  font-family: SatoshiBold;
  src: url(/font/Satoshi-Bold.woff2) format("woff2"),url(/font/Satoshi-Bold.woff) format("woff"),url(/font/Satoshi-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Add these styles for the toggle switch */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Add these styles if they don't exist already */
@layer components {
  .profile-tab-active {
    @apply border-b-2 border-blue-500 text-blue-500;
  }
  
  .profile-tab-inactive {
    @apply text-gray-600;
  }
}
