import { createContext, useCallback, useContext, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { chatKeys } from "../services/chatQueries";
import { useAuth } from "../hooks/useAuth";
import { io } from "socket.io-client";

const ChatMessagesContext = createContext(null);

const SOCKET_URL = "http://localhost:3001";

export const useChatMessages = () => {
  const context = useContext(ChatMessagesContext);
  if (!context) {
    throw new Error(
      "useChatMessages must be used within a ChatMessagesProvider"
    );
  }
  return context;
};

export const ChatMessagesProvider = ({ children }) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const socket = io(SOCKET_URL, {
    auth: {
      token: localStorage.getItem("token"),
    },
    transports: ["websocket", "polling"],
    autoConnect: false,
  });

  useEffect(() => {
    if (user) {
      socket.connect();

      socket.on("connect", () => {
        console.log("Socket connected successfully");
        socket.emit("join", user._id);
      });

      socket.on("disconnect", () => {
        console.log("Socket disconnected");
      });

      socket.on("error", (error) => {
        console.error("Socket error:", error);
      });

      socket.on("new message", (message) => {
        console.log("new message", message);
        queryClient.setQueryData(
          chatKeys.messages(message.conversation),
          (old) => {
            if (!old) return { messages: [message] };
            return {
              ...old,
              messages: [...old.messages, message],
            };
          }
        );

        queryClient.setQueryData(chatKeys.conversations(), (old) => {
          if (!old) {
            return {
              conversations: [
                {
                  _id: message.conversation,
                  lastMessage: message,
                  participants: [message.sender, message.receiver],
                  unreadCount: message.sender._id !== user._id ? 1 : 0,
                },
              ],
              totalUnreadCount: message.sender._id !== user._id ? 1 : 0,
              unreadCounts: {
                [message.conversation]: message.sender._id !== user._id ? 1 : 0,
              },
            };
          }

          let conversationExists = false;
          const updatedConversations = old.conversations.map((conv) => {
            console.log("CONV", conv);
            console.log("MESSAGE", message);
            if (conv._id === message.conversation._id) {
              conversationExists = true;
              return {
                ...conv,
                lastMessage: message,
                unreadCount:
                  message.sender._id !== user._id
                    ? (conv.unreadCount || 0) + 1
                    : conv.unreadCount || 0,
              };
            }
            return conv;
          });
          //   console.log("updatedConversations", updatedConversations);
          //   console.log("conversationExists", conversationExists);
          //   console.log("message", message);
          if (!conversationExists) {
            updatedConversations.unshift({
              _id: message.conversation,
              lastMessage: message,
              participants: [message.sender, message.receiver],
              unreadCount: message.sender._id !== user._id ? 1 : 0,
            });
          }

          const totalUnread = updatedConversations.reduce(
            (sum, conv) => sum + (conv.unreadCount || 0),
            0
          );

          return {
            conversations: updatedConversations,
            totalUnreadCount: totalUnread,
            unreadCounts: updatedConversations.reduce((acc, conv) => {
              acc[conv._id] = conv.unreadCount || 0;
              return acc;
            }, {}),
          };
        });
      });

      socket.on("user_status_change", ({ userId, online, lastSeen }) => {
        console.log("User status changed:", userId, online, lastSeen);
      });

      socket.on("messages_read", (data) => {
        console.log("messages_read", data);
        queryClient.setQueryData(
          chatKeys.messages(data.conversationId),
          (old) => {
            if (!old) return old;
            return {
              ...old,
              messages: old.messages.map((msg) => ({
                ...msg,
                readBy: [...new Set([...msg.readBy, data.readBy])],
              })),
            };
          }
        );
      });
    }

    return () => {
      if (socket.connected) {
        socket.disconnect();
      }
    };
  }, [user, queryClient]);

  const sendMessage = useCallback(
    (payload) => {
      console.log("socket payload", payload);
      if (socket.connected) {
        socket.emit("private message", {
          receiverId: payload?.payload?.receiver,
          message: payload?.payload,
        });
      }
    },
    [socket]
  );

  const value = {
    sendMessage,
    isConnected: socket.connected,
  };

  return (
    <ChatMessagesContext.Provider value={value}>
      {children}
    </ChatMessagesContext.Provider>
  );
};

export default ChatMessagesContext;
