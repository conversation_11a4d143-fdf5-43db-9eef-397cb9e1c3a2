import "./App.css";
import Routes from "./routes";
import { useAuth } from "./hooks/useAuth";
import { Toaster } from "react-hot-toast";
import LoadingSpinner from "./components/LoadingSpinner";
import { HelmetProvider } from "react-helmet-async";
import { ChatMessagesProvider } from "./context/ChatMessagesContext";

function App() {
  const { isInitialized, user } = useAuth();

  if (!isInitialized) {
    return <LoadingSpinner size="xl" />;
  }

  return (
    <HelmetProvider>
      <ChatMessagesProvider>
        <Toaster
          position="top-right"
          duration={3000}
          containerClassName="z-50"
        />
        <Routes />
      </ChatMessagesProvider>
    </HelmetProvider>
  );
}

export default App;
