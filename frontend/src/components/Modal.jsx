import PropTypes from "prop-types";
import { Icon } from "@iconify/react";
import { useEffect } from "react";

export default function Modal({ isOpen, onClose, children, title, className }) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex justify-center items-center z-[9999999] px-5">
      <div
        className={`bg-white p-6 rounded-xl w-full max-w-lg relative max-h-[75vh] flex flex-col ${className}`}
      >
        <div className="absolute top-3 right-3">
          <button type="button" className="z-50 relative" onClick={onClose}>
            <Icon
              icon="mdi:close"
              fontSize={24}
              className="text-gray-500 cursor-pointer"
            />
          </button>
        </div>

        {title && (
          <div className="p-4">
            <h2 className="text-xl font-medium">{title}</h2>
          </div>
        )}

        <div className="overflow-y-auto scrollbar-hide flex-1">{children}</div>
      </div>
    </div>
  );
}

Modal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  title: PropTypes.string,
  className: PropTypes.string,
};
