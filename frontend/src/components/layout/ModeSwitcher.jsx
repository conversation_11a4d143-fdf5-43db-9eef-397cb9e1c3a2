import { useState } from "react";
import { Icon } from "@iconify/react";
import { useLayoutMode } from "../../hooks/useLayoutMode";
import { useAuth } from "../../hooks/useAuth";
import InteractiveButton from "../InteractiveButton";
import Modal from "../Modal";

/**
 * Component for switching between fan and creator modes
 * Shows current mode and allows switching with confirmation
 */
export default function ModeSwitcher({ className = "" }) {
  const { user } = useAuth();
  const {
    currentMode,
    isCreatorMode,
    isFanMode,
    canSwitchToCreator,
    switchMode,
    isTransitioning,
  } = useLayoutMode();

  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [targetMode, setTargetMode] = useState(null);

  const handleModeSwitch = (mode) => {
    if (mode === currentMode) return;

    setTargetMode(mode);
    setShowConfirmModal(true);
  };

  const confirmModeSwitch = () => {
    if (targetMode) {
      switchMode(targetMode, { preserveRoute: true });
      setShowConfirmModal(false);
      setTargetMode(null);
    }
  };

  const cancelModeSwitch = () => {
    setShowConfirmModal(false);
    setTargetMode(null);
  };

  return (
    <>
      <div
        className={`bg-white rounded-2xl p-6 border border-gray-100 ${className}`}
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Icon
              icon="solar:refresh-circle-bold"
              className="w-5 h-5 text-white"
            />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Mode Switcher</h3>
            <p className="text-sm text-gray-600">
              Switch between fan and creator experience
            </p>
          </div>
        </div>

        {/* Current Mode Display */}
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-medium text-gray-700">
              Current Mode:
            </span>
            <div
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                isCreatorMode
                  ? "bg-purple-100 text-purple-700"
                  : "bg-blue-100 text-blue-700"
              }`}
            >
              {isCreatorMode ? (
                <>
                  <Icon
                    icon="solar:star-bold"
                    className="w-4 h-4 inline mr-1"
                  />
                  Creator Mode
                </>
              ) : (
                <>
                  <Icon
                    icon="solar:heart-bold"
                    className="w-4 h-4 inline mr-1"
                  />
                  Fan Mode
                </>
              )}
            </div>
          </div>
          <p className="text-sm text-gray-600">
            {isCreatorMode
              ? "You're currently using creator tools and dashboard"
              : "You're currently browsing as a fan"}
          </p>
        </div>

        {/* Mode Options */}
        <div className="space-y-3">
          {/* Fan Mode Option */}
          <button
            onClick={() => handleModeSwitch("fan")}
            disabled={isFanMode || isTransitioning}
            className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${
              isFanMode
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-blue-300 hover:bg-blue-25"
            } ${isTransitioning ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Icon icon="solar:heart-bold" className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">Fan Mode</h4>
                <p className="text-sm text-gray-600">
                  Browse content, subscribe to creators
                </p>
              </div>
              {isFanMode && (
                <Icon
                  icon="solar:check-circle-bold"
                  className="w-5 h-5 text-blue-600"
                />
              )}
            </div>
          </button>

          {/* Creator Mode Option */}
          <button
            onClick={() => handleModeSwitch("creator")}
            disabled={isCreatorMode || !canSwitchToCreator || isTransitioning}
            className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${
              isCreatorMode
                ? "border-purple-500 bg-purple-50"
                : canSwitchToCreator
                ? "border-gray-200 hover:border-purple-300 hover:bg-purple-25"
                : "border-gray-200 bg-gray-50 cursor-not-allowed"
            } ${isTransitioning ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <div className="flex items-center gap-3">
              <div
                className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                  canSwitchToCreator
                    ? "bg-gradient-to-br from-purple-500 to-purple-600"
                    : "bg-gray-300"
                }`}
              >
                <Icon icon="solar:star-bold" className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">Creator Mode</h4>
                <p className="text-sm text-gray-600">
                  {canSwitchToCreator
                    ? "Manage content, view analytics, earn money"
                    : "Become a creator to access this mode"}
                </p>
              </div>
              {isCreatorMode && (
                <Icon
                  icon="solar:check-circle-bold"
                  className="w-5 h-5 text-purple-600"
                />
              )}
              {!canSwitchToCreator && (
                <Icon
                  icon="solar:lock-bold"
                  className="w-5 h-5 text-gray-400"
                />
              )}
            </div>
          </button>
        </div>

        {/* Become Creator CTA */}
        {!canSwitchToCreator && (
          <div className="mt-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-100">
            <div className="flex items-start gap-3">
              <Icon
                icon="solar:star-bold"
                className="w-5 h-5 text-purple-600 mt-0.5"
              />
              <div className="flex-1">
                <h4 className="font-medium text-purple-900 mb-1">
                  Ready to become a creator?
                </h4>
                <p className="text-sm text-purple-700 mb-3">
                  Start earning money from your content and build your
                  community.
                </p>
                <InteractiveButton
                  onClick={() => {
                    // Show onboarding to become creator
                    localStorage.removeItem(`onboarding_completed_${user._id}`);
                    localStorage.removeItem(`onboarding_skipped_${user._id}`);
                    window.location.reload();
                  }}
                  variant="primary"
                  size="sm"
                  className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
                >
                  Become a Creator
                  <Icon
                    icon="solar:arrow-right-linear"
                    className="w-4 h-4 ml-1"
                  />
                </InteractiveButton>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      <Modal isOpen={showConfirmModal} onClose={cancelModeSwitch}>
        <div className="p-6">
          <div className="text-center mb-6">
            <div
              className={`w-16 h-16 mx-auto rounded-2xl flex items-center justify-center mb-4 ${
                targetMode === "creator"
                  ? "bg-gradient-to-br from-purple-500 to-pink-600"
                  : "bg-gradient-to-br from-blue-500 to-blue-600"
              }`}
            >
              <Icon
                icon={
                  targetMode === "creator"
                    ? "solar:star-bold"
                    : "solar:heart-bold"
                }
                className="w-8 h-8 text-white"
              />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Switch to {targetMode === "creator" ? "Creator" : "Fan"} Mode?
            </h3>
            <p className="text-gray-600">
              {targetMode === "creator"
                ? "You'll be redirected to your creator dashboard with access to content management tools."
                : "You'll be redirected to the main feed with the fan browsing experience."}
            </p>
          </div>

          <div className="flex gap-3">
            <InteractiveButton
              onClick={cancelModeSwitch}
              variant="secondary"
              className="flex-1"
            >
              Cancel
            </InteractiveButton>
            <InteractiveButton
              onClick={confirmModeSwitch}
              variant="primary"
              className="flex-1"
              isLoading={isTransitioning}
            >
              Switch Mode
            </InteractiveButton>
          </div>
        </div>
      </Modal>
    </>
  );
}
