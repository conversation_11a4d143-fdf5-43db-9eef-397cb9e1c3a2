import PropTypes from "prop-types";
import StatusChip from "./StatusChip";
import { fCurrency } from "../utils/formatNumber";

export default function Table({ columns, data }) {
  const renderCell = (column, row) => {
    // If column has custom cell renderer, use it
    if (column.cell) {
      return column.cell(row);
    }

    const value = row[column.accessor];

    // Default renderers for specific column types
    switch (column.accessor) {
      case "status":
        return <StatusChip status={value} />;
      case "amount":
        return fCurrency(value);
      case "action":
        return (
          <button className="font-medium text-primary hover:text-primary-400 transition-colors">
            Edit
          </button>
        );
      default:
        return value;
    }
  };

  return (
    <div className="w-full overflow-hidden border border-gray-100 rounded-xl">
      <div className="w-full overflow-x-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-gray-50">
        <table className="w-full text-sm text-left min-w-[1000px]">
          <thead>
            <tr className="border-b border-gray-100">
              {columns?.map((column) => (
                <th
                  key={column.accessor}
                  scope="col"
                  className="px-6 py-4 bg-gray-50/50 text-xs font-semibold text-secondary-300 uppercase tracking-wider sticky top-0"
                >
                  {column.name}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {data?.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className="bg-white transition-colors hover:bg-gray-50/50"
              >
                {columns?.map((column) => {
                  const cellClassName =
                    {
                      amount: "font-medium text-secondary-300",
                      username: "font-medium text-secondary-400",
                      email: "text-gray-500",
                      startDate: "text-gray-600",
                      endDate: "text-gray-600",
                    }[column.accessor] || "text-gray-600";

                  return (
                    <td
                      key={`${rowIndex}-${column.accessor}`}
                      className={`px-6 py-4 whitespace-nowrap ${cellClassName}`}
                    >
                      {renderCell(column, row)}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

Table.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      accessor: PropTypes.string.isRequired,
      cell: PropTypes.func,
    })
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
};
