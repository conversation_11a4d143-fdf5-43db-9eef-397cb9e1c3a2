import { fCurrency } from "../utils/formatNumber";
import InteractiveButton from "./InteractiveButton";
import PropTypes from "prop-types";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Modal from "./Modal";
import { walletService, subscriptionService } from "../services/api";
import useWalletStore from "../store/walletStore";
import toast from "react-hot-toast";
import { useFlutterwave, closePaymentModal } from "flutterwave-react-v3";
import { usePaystackPayment } from "react-paystack";
import { FaWallet } from "react-icons/fa";
import { SiFlutter } from "react-icons/si";
import { FaMoneyBillTransfer } from "react-icons/fa6";
import { useAuth } from "../hooks/useAuth";

export default function SubscriptionModal({
  tier,
  onClose,
  isOpen,
  wallet,
  user,
  getUserProfile,
}) {
  const [initiateSubscription, setInitiateSubscription] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [error, setError] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState(null);
  const [existingSubscription, setExistingSubscription] = useState(null);
  const { debitWallet } = useWalletStore();
  const { user: profile, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Check for self-subscription and existing subscriptions
  useEffect(() => {
    const checkSubscription = async () => {
      if (!isAuthenticated || !user?._id || !profile?._id) return;

      if (user._id === profile._id) {
        setError("You cannot subscribe to yourself");
        return;
      }

      try {
        const response = await subscriptionService.checkExistingSubscription(
          user._id
        );
        if (response.data) {
          const { subscription, canRetry, message } = response.data;
          setExistingSubscription({ subscription, canRetry, message });
          if (!canRetry) {
            setError(message);
          }
        }
      } catch (error) {
        console.error("Error checking subscription:", error);
      }
    };

    checkSubscription();
  }, [user?._id, profile?._id, isAuthenticated]);

  // Generate unique transaction reference
  const generateTransactionRef = () => {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // FLUTTERWAVE CONFIG & PAYMENT
  const flutterwaveConfig = {
    public_key: import.meta.env.VITE_FLUTTERWAVE_PUBLIC_KEY,
    tx_ref: generateTransactionRef(),
    amount: tier?.amount,
    currency: "NGN",
    payment_options: "card,ussd,banktransfer",
    customer: {
      email: profile?.email,
      name: profile?.name,
      phone_number: profile?.phone || "",
    },
    customizations: {
      title: `Subscribe to ${user?.name}`,
      description: `${tier?.noOfMonths} month(s) subscription`,
      logo: user?.avatar || "https://your-logo-url.png",
    },
  };

  const handleFlutterPayment = useFlutterwave(flutterwaveConfig);

  // PAYSTACK CONFIG & PAYMENT
  const paystackConfig = {
    reference: generateTransactionRef(),
    email: profile?.email,
    amount: tier?.amount * 100, // Paystack amount is in kobo
    publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY,
    metadata: {
      custom_fields: [
        {
          display_name: "Subscription Plan",
          variable_name: "subscription_plan",
          value: `${tier?.noOfMonths} month(s)`,
        },
        {
          display_name: "Creator",
          variable_name: "creator_name",
          value: user?.name,
        },
      ],
    },
  };

  const initializePaystack = usePaystackPayment(paystackConfig);

  const handlePaymentSuccess = async (paymentDetails, method) => {
    try {
      setPaymentLoading(true);

      let reference = {
        transactionId: "",
        reference: "",
        paymentStatus: "success",
        paymentGatewayResponse: null,
        metadata: {},
      };

      if (method === "wallet") {
        reference = {
          transactionId: generateTransactionRef(),
          reference: generateTransactionRef(),
          paymentStatus: "success",
          metadata: {
            paymentType: "wallet",
            walletBalance: wallet.balance,
          },
        };
      } else if (method === "flutterwave") {
        reference = {
          transactionId: paymentDetails.transaction_id,
          reference: paymentDetails.tx_ref,
          paymentStatus:
            paymentDetails.status === "successful" ? "success" : "failed",
          paymentGatewayResponse: paymentDetails,
          metadata: {
            flw_ref: paymentDetails.flw_ref,
            paymentType: "flutterwave",
          },
        };
      } else if (method === "paystack") {
        reference = {
          transactionId: paymentDetails.trans,
          paymentReference: paymentDetails.reference, // This is the correct field name expected by the backend
          paymentStatus:
            paymentDetails.status === "success" ? "success" : "failed",
          paymentGatewayResponse: paymentDetails,
          metadata: {
            paymentType: "paystack",
          },
        };

        // Log the reference object for debugging
        console.log("Paystack reference object:", reference);
      }

      // Use transactionReference instead of reference to match backend expectations
      const response = await subscriptionService.subscribeToCreator({
        creatorId: user?._id,
        planId: tier?._id,
        paymentMethod: method,
        transactionReference: reference,
        subscriptionId: existingSubscription?.subscription?._id, // Include if retrying payment
      });

      await getUserProfile();
      toast.success("Subscription successful!");
      onClose();
    } catch (error) {
      console.error("Payment verification error:", error);
      toast.error(
        error?.response?.data?.message || "Payment verification failed"
      );
      setError(error?.response?.data?.message || "Payment verification failed");
    } finally {
      setPaymentLoading(false);
    }
  };

  const handlePayNow = async (method) => {
    if (!isAuthenticated) {
      navigate(`/login?redirect=${window.location.pathname}`);
      return;
    }

    setError(null);
    setPaymentLoading(true);
    setPaymentMethod(method);

    try {
      if (method === "wallet") {
        if (wallet.balance < tier?.amount) {
          throw new Error("Insufficient wallet balance");
        }
        await handlePaymentSuccess(
          {
            status: "success",
            timestamp: new Date().toISOString(),
          },
          "wallet"
        );
      } else if (method === "flutterwave") {
        handleFlutterPayment({
          callback: async (response) => {
            if (response.status === "successful") {
              await handlePaymentSuccess(response, "flutterwave");
            } else {
              throw new Error("Flutterwave payment failed");
            }
            closePaymentModal();
          },
          onClose: () => {
            setPaymentLoading(false);
            setPaymentMethod(null);
            toast.error("Payment cancelled");
          },
        });
      } else if (method === "paystack") {
        initializePaystack({
          onSuccess: async (response) => {
            await handlePaymentSuccess(response, "paystack");
          },
          onClose: () => {
            setPaymentLoading(false);
            setPaymentMethod(null);
            toast.error("Payment cancelled");
          },
        });
      }
    } catch (error) {
      console.error("Payment error:", error);
      toast.error(error.message || "Payment failed");
      setError(error.message || "Payment failed");
    } finally {
      if (method === "wallet") {
        setPaymentLoading(false);
      }
    }
  };

  const SubscriptionContent = () => (
    <div className="space-y-6">
      {error && !existingSubscription?.canRetry && (
        <div className="p-4 bg-red-50 text-red-600 rounded-lg border border-red-100">
          {error}
        </div>
      )}

      {existingSubscription?.canRetry && (
        <div className="p-4 bg-yellow-50 text-yellow-800 rounded-lg border border-yellow-100">
          <p className="font-medium">Previous payment failed</p>
          <p className="text-sm mt-1">
            You can retry the payment to complete your subscription.
          </p>
        </div>
      )}

      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-2xl font-semibold text-gray-900 mb-2">
          {fCurrency(tier?.amount)}
          <span className="text-base text-gray-500">
            / {tier?.noOfMonths} month(s)
          </span>
        </p>
      </div>

      <div>
        <p className="text-lg font-medium text-gray-900 mb-3">
          Subscription benefits
        </p>
        <ul className="space-y-3">
          {[
            "Full access to this user's content",
            "Send direct messages to this user",
            "Be the first to know when this user publishes new posts",
            "Cancel your subscription anytime",
          ].map((benefit, index) => (
            <li key={index} className="flex items-center text-gray-600">
              <svg
                className="w-5 h-5 text-green-500 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              {benefit}
            </li>
          ))}
        </ul>
      </div>

      <div className="flex justify-end gap-4">
        <InteractiveButton
          onClick={() => {
            if (!isAuthenticated) {
              navigate(`/login?redirect=${window.location.pathname}`);
              return;
            }
            setError(null);
            setInitiateSubscription(true);
          }}
          disabled={!!error && !existingSubscription?.canRetry}
          className="px-6"
        >
          {existingSubscription?.canRetry ? "Retry Payment" : "Continue"}
        </InteractiveButton>
        <button
          type="button"
          onClick={onClose}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
      </div>

      <p className="text-sm text-gray-500 mt-4">
        Your subscription will automatically renew every {tier?.noOfMonths}{" "}
        month(s). You can cancel anytime before the next renewal.
      </p>
    </div>
  );

  const PaymentContent = () => (
    <div className="space-y-6">
      {error && (
        <div className="p-4 bg-red-50 text-red-600 rounded-lg border border-red-100">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg border p-4">
        <div className="flex justify-between items-center">
          <p className="text-gray-600">Wallet balance</p>
          <p className="text-lg font-medium">{fCurrency(wallet.balance)}</p>
        </div>
        <InteractiveButton
          onClick={() => handlePayNow("wallet")}
          disabled={paymentLoading || !!error || wallet.balance < tier?.amount}
          isLoading={paymentMethod === "wallet" && paymentLoading}
          className="w-full mt-4 !flex items-center justify-center gap-2"
        >
          <FaWallet className="w-5 h-5" />
          <span>Pay with Wallet</span>
        </InteractiveButton>
      </div>

      <div className="space-y-4">
        <p className="font-medium text-gray-900">Other payment methods</p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <InteractiveButton
            onClick={() => handlePayNow("flutterwave")}
            disabled={paymentLoading || !!error}
            isLoading={paymentMethod === "flutterwave" && paymentLoading}
            className="!flex items-center justify-center gap-2"
          >
            <SiFlutter className="w-5 h-5" />
            <span>Flutterwave</span>
          </InteractiveButton>

          <InteractiveButton
            onClick={() => handlePayNow("paystack")}
            disabled={paymentLoading || !!error}
            isLoading={paymentMethod === "paystack" && paymentLoading}
            className="!flex items-center justify-center gap-2"
          >
            <FaMoneyBillTransfer className="w-5 h-5" />
            <span>Paystack</span>
          </InteractiveButton>
        </div>
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={initiateSubscription ? "Complete Payment" : "Subscription Details"}
    >
      {!initiateSubscription ? <SubscriptionContent /> : <PaymentContent />}
    </Modal>
  );
}

SubscriptionModal.propTypes = {
  tier: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    amount: PropTypes.number.isRequired,
    noOfMonths: PropTypes.number.isRequired,
  }).isRequired,
  onClose: PropTypes.func.isRequired,
  isOpen: PropTypes.bool.isRequired,
  wallet: PropTypes.shape({
    balance: PropTypes.number.isRequired,
  }),
  user: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    avatar: PropTypes.string,
  }),
  getUserProfile: PropTypes.func.isRequired,
};
