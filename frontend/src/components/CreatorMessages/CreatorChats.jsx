import React, { useState } from "react";
import {
  useConversations,
  useStartNewConversation,
  chatKeys,
} from "../../services/chatQueries";
import { useQueryClient } from "@tanstack/react-query";
import { FiMessageSquare } from "react-icons/fi";
import { BsCheckAll } from "react-icons/bs";
import Avatar from "../Avatar";
import { toast } from "react-hot-toast";
import SearchSubscribersModal from "./SearchSubscribersModal";
import { useAuth } from "../../hooks/useAuth";
import useChatStore from "../../store/chatStore";
import { formatLastSeen } from "../../utils/formatTime";

const CreatorChats = ({ onSelectChat }) => {
  const { data, isLoading } = useConversations();
  const startNewConversationMutation = useStartNewConversation();
  const { user } = useAuth();
  const { getUserStatus } = useChatStore();
  const queryClient = useQueryClient();
  const [showSearch, setShowSearch] = useState(false);
  const [selectedConversationId, setSelectedConversationId] = useState(null);

  const handleChatSelect = (conversation) => {
    setSelectedConversationId(conversation._id);

    // Update the unread count in the cache immediately
    queryClient.setQueryData(chatKeys.conversations(), (old) => {
      if (!old) return old;

      const newUnreadCounts = { ...old.unreadCounts };
      newUnreadCounts[conversation._id] = 0;

      const updatedConversations = old.conversations.map((conv) => {
        if (conv._id === conversation._id) {
          return { ...conv, unreadCount: 0 };
        }
        return conv;
      });

      const totalUnread = Object.values(newUnreadCounts).reduce(
        (sum, count) => sum + count,
        0
      );

      return {
        ...old,
        conversations: updatedConversations,
        unreadCounts: newUnreadCounts,
        totalUnreadCount: totalUnread,
      };
    });

    if (onSelectChat) {
      onSelectChat(conversation);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col space-y-4 p-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-full bg-gray-200 animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto">
      {data?.conversations?.length === 0 && (
        <div className="flex flex-col items-center justify-center h-full p-8 text-gray-600">
          <div className="bg-gray-50 rounded-full p-6 mb-6">
            <FiMessageSquare className="w-16 h-16 text-primary" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No Messages Yet</h3>
          <p className="text-center text-gray-500 mb-6 max-w-sm">
            Start connecting with your subscribers! Send them a message to begin
            a conversation.
          </p>
          <button
            onClick={() => setShowSearch(true)}
            className="flex items-center gap-2 px-6 py-3 bg-primary hover:bg-primary/90 text-white rounded-lg transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md"
          >
            <FiMessageSquare className="w-4 h-4" />
            Message a Subscriber
          </button>
        </div>
      )}
      {data?.conversations?.length > 0 && (
        <>
          <div className="p-4 border-b h-16">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-medium flex items-center gap-2">
                Messages
                {data.totalUnreadCount > 0 && (
                  <span className="bg-primary text-white text-xs rounded-full px-2 py-1">
                    {data.totalUnreadCount}
                  </span>
                )}
              </h2>
              <button
                onClick={() => setShowSearch(true)}
                className="px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-lg transition-colors duration-200 text-sm"
              >
                New message
              </button>
            </div>
          </div>
          <div className="divide-y">
            {data.conversations.map((conversation) => {
              const otherParticipant = conversation.participants.find(
                (p) => p._id !== user._id
              );
              const unreadCount = data.unreadCounts[conversation._id] || 0;

              return (
                <div
                  key={conversation._id}
                  className={`flex items-center p-4 cursor-pointer hover:bg-gray-50 ${
                    selectedConversationId === conversation._id
                      ? "bg-gray-100"
                      : ""
                  }`}
                  onClick={() => handleChatSelect(conversation)}
                >
                  <div className="relative">
                    <Avatar
                      avatar={otherParticipant?.avatar}
                      className="w-12 h-12"
                    />
                    {getUserStatus(otherParticipant?._id).isOnline && (
                      <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
                    )}
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">
                          {otherParticipant?.username}
                        </h3>
                        <div className="text-xs text-gray-500">
                          {getUserStatus(otherParticipant?._id).isOnline ? (
                            <span className="text-green-500">Online</span>
                          ) : (
                            getUserStatus(otherParticipant?._id).lastSeen && (
                              <span>
                                Last seen{" "}
                                {formatLastSeen(
                                  getUserStatus(otherParticipant?._id).lastSeen
                                )}
                              </span>
                            )
                          )}
                        </div>
                      </div>
                      {unreadCount > 0 &&
                        selectedConversationId !== conversation._id && (
                          <span className="bg-primary-500 text-white text-xs px-2 py-1 rounded-full">
                            {unreadCount}
                          </span>
                        )}
                    </div>
                    {conversation.lastMessage && (
                      <div className="flex flex-col space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-500 truncate max-w-[200px]">
                            {conversation.lastMessage.content}
                          </p>
                          <div className="flex items-center space-x-1">
                            {conversation.lastMessage.readBy?.length > 1 && (
                              <span className="text-primary-500bg-primary-500">
                                <BsCheckAll size={16} />
                              </span>
                            )}
                            <span className="text-xs text-gray-400">
                              {new Date(
                                conversation.lastMessage.createdAt
                              ).toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}

      <SearchSubscribersModal
        showSearch={showSearch}
        onClose={() => setShowSearch(false)}
        onSelect={async (subscription) => {
          const subscriber = subscription.subscriber;
          // Find existing conversation or create new one
          const existingConv = data.conversations.find((conv) =>
            conv.participants.some((p) => p._id === subscriber._id)
          );

          if (existingConv) {
            handleChatSelect(existingConv);
          } else {
            try {
              const newConversation =
                await startNewConversationMutation.mutateAsync(subscriber._id);
              if (newConversation) {
                handleChatSelect(newConversation);
              }
            } catch (error) {
              console.error("Error starting new conversation:", error);
              toast.error("Failed to start conversation");
            }
          }
          setShowSearch(false);
        }}
      />
    </div>
  );
};

export default CreatorChats;
