import { useState, useEffect } from "react";
import {
  IoChevronDownOutline,
  IoAttachOutline,
  IoHappyOutline,
  IoMicOutline,
  IoTimeOutline,
  IoCheckmarkCircle,
} from "react-icons/io5";
import { BsLink45Deg } from "react-icons/bs";
import BackButton from "../BackButton";
import MessageInput from "../MessageInput";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { automationService } from "../../services/api";
import { toast } from "react-hot-toast";
import EmojiPicker from "emoji-picker-react";
import { triggerEvents } from "../../utils/utils";
import LoadingSpinner from "../LoadingSpinner";

export default function CreateAutomation({
  onClose,
  editMode = false,
  automation = null,
}) {
  const [selectedTrigger, setSelectedTrigger] = useState(
    editMode && automation?.triggerEvent ? automation?.triggerEvent : ""
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [message, setMessage] = useState(
    editMode && automation?.message ? automation.message : ""
  );
  const [delayDuration, setDelayDuration] = useState(
    editMode && automation?.timing?.delayDuration
      ? automation.timing.delayDuration
      : 30
  );
  const [timing, setTiming] = useState(
    editMode && automation?.timing?.type
      ? automation.timing.type
      : "immediately"
  );
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  const queryClient = useQueryClient();

  const {
    data: automations,
    isLoading: isLoadingAutomations,
    refetch,
  } = useQuery({
    queryKey: ["automations"],
    queryFn: async () => {
      const response = await automationService.getAutomations();
      return response.data;
    },
  });
  const { mutate: createAutomation, isLoading: isCreating } = useMutation({
    mutationFn: (data) => automationService.createAutomation(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["automations"] });
      toast.success("Automation created successfully!");
      refetch();
      onClose?.();
      setSelectedTrigger("");
      setMessage("");
      setTiming("");
      setDelayDuration(0);
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to create automation");
    },
  });

  const { mutate: updateAutomation, isLoading: isUpdating } = useMutation({
    mutationFn: (data) =>
      automationService.updateAutomation(automation._id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["automations"] });
      toast.success("Automation updated successfully!");
      refetch();
      onClose?.();
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to update automation");
    },
  });

  const isLoading = isCreating || isUpdating;

  const sampleMessages = {
    WELCOME_MESSAGE:
      "Hey {subscriberName}! 👋 Welcome to my exclusive content! I'm so excited to have you here. Feel free to check out all my content and don't hesitate to send me a message. Let's make this journey amazing together! 💫",
    TIP_RECEIVED:
      "Thank you so much {fanName} for the generous tip! 🎉 Your support means the world to me. I really appreciate you showing love for my content. Stay tuned for more exciting updates! ❤️",
    SUBSCRIPTION_EXPIRED:
      "Hey {subscriberName}! 😢 I noticed your subscription has expired. I've loved having you as part of my community and would be thrilled to have you back! Don't miss out on all the exclusive content I have planned. Hope to see you again soon! 🌟",
    RESUBSCRIBED:
      "Welcome back {subscriberName}! 🎉 I'm so happy you've decided to rejoin the family! You've made my day. Can't wait to share all the new content I have planned just for you! 💖",
  };

  const handleTriggerChange = (trigger) => {
    setSelectedTrigger(trigger);
    setMessage(sampleMessages[trigger]);
    setIsDropdownOpen(false);
  };

  const handleSubmit = async (message) => {
    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    const data = {
      triggerEvent: selectedTrigger,
      message,
      timing,
      delayDuration: timing === "delay" ? delayDuration : 0,
    };

    if (editMode) {
      updateAutomation(data);
    } else {
      createAutomation(data);
    }
  };

  useEffect(() => {
    if (editMode && automation) {
      setSelectedTrigger(automation.triggerEvent || "");
      setMessage(automation.message || "");
      setDelayDuration(automation.timing?.delayDuration || 30);
      setTiming(automation.timing?.type || "immediately");
    }
  }, [editMode, automation]);

  useEffect(() => {
    if (!editMode) {
      setSelectedTrigger("");
      setMessage("");
      setTiming("immediately");
      setDelayDuration(30);
    }
  }, [editMode]);

  if (isLoadingAutomations) return <LoadingSpinner size="xl" />;

  return (
    <div className="flex flex-col h-full bg-white px-4 md:px-6 w-full space-y-6 max-w-4xl mx-auto py-6">
      <div className="md:hidden">
        <BackButton onClick={onClose} />
      </div>

      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-heading text-secondary font-semibold">
          {editMode ? "Edit Automation" : "Create Automation"}
        </h2>
        <p className="text-gray-600 text-sm">
          {editMode
            ? "Update your automated response settings"
            : "Set up automated responses for different events"}
        </p>
      </div>

      {/* Trigger Event Selector */}
      <div className="relative space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Select trigger event
        </label>
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="w-full disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-between px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-all duration-200 focus:ring-2 focus:ring-primary-100 focus:border-primary"
          disabled={isLoading || editMode}
        >
          <span className="text-gray-800">
            {triggerEvents.find((trigger) => trigger.value === selectedTrigger)
              ?.label || "Select trigger event"}
          </span>
          {!editMode && (
            <IoChevronDownOutline
              className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${
                isDropdownOpen ? "rotate-180" : ""
              }`}
            />
          )}
        </button>

        {/* Dropdown Menu */}
        {isDropdownOpen && !editMode && (
          <div className="absolute z-10 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-soft max-h-60 overflow-y-auto">
            {triggerEvents
              .filter(
                (trigger) =>
                  !automations.some(
                    (automation) => automation.triggerEvent === trigger.value
                  )
              )
              .map((trigger, index) => (
                <button
                  key={index}
                  onClick={() => handleTriggerChange(trigger.value)}
                  className="w-full px-4 py-3.5 text-left hover:bg-gray-50 transition-colors text-gray-700 first:rounded-t-lg last:rounded-b-lg border-b last:border-b-0 border-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isLoading}
                >
                  {trigger.label}
                </button>
              ))}
          </div>
        )}
      </div>

      {/* Timing Controls */}
      <div className="space-y-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            When to send
          </label>
          <select
            value={timing}
            onChange={(e) => setTiming(e.target.value)}
            className="w-full px-4 py-2.5 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-100 focus:border-primary text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            <option value="immediately">Send immediately</option>
            <option value="delay">Send with delay</option>
          </select>
        </div>

        {timing === "delay" && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Delay duration
            </label>
            <div className="relative">
              <IoTimeOutline className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
              <select
                value={delayDuration}
                onChange={(e) => setDelayDuration(Number(e.target.value))}
                className="w-full pl-10 pr-4 py-2.5 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-100 focus:border-primary text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isLoading}
              >
                <option value={30}>30 minutes</option>
                <option value={60}>1 hour</option>
                <option value={120}>2 hours</option>
                <option value={180}>3 hours</option>
                <option value={240}>4 hours</option>
                <option value={300}>5 hours</option>
                <option value={360}>6 hours</option>
                <option value={420}>7 hours</option>
                <option value={480}>8 hours</option>
                <option value={540}>9 hours</option>
                <option value={600}>10 hours</option>
                <option value={660}>11 hours</option>
                <option value={720}>12 hours</option>
                <option value={1440}>24 hours</option>
                <option value={2880}>48 hours</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Message Input */}
      <div className="!mt-auto relative">
        <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center gap-2">
            <button
              className="p-2 text-gray-500 hover:text-primary transition-colors"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            >
              <IoHappyOutline className="w-6 h-6" />
            </button>
          </div>

          <div className="flex-1 relative">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your automated message..."
              className="w-full px-4 py-2.5 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-100 focus:border-primary text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            />

            {showEmojiPicker && (
              <div className="absolute bottom-full right-0 mb-2">
                <EmojiPicker
                  onEmojiClick={(emojiObject) => {
                    setMessage((prev) => prev + emojiObject.emoji);
                    setShowEmojiPicker(false);
                  }}
                  width={320}
                  height={450}
                />
              </div>
            )}
          </div>

          <button
            onClick={() => handleSubmit(message)}
            disabled={isLoading || !message.trim() || !selectedTrigger}
            className="p-2 rounded-full bg-primary hover:bg-primary-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <IoCheckmarkCircle className="w-6 h-6 text-white" />
          </button>
        </div>
      </div>
    </div>
  );
}
