export default function CreatorMassDMs({ chats = [] }) {
  return (
    <div className="flex-1 overflow-y-auto">
      {chats.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-32 h-32 mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            {/* Placeholder for chat icon/illustration */}
            <svg
              className="w-16 h-16 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-heading text-secondary-DEFAULT mb-2">
            Your mass DMs will appear here
          </h3>
        </div>
      ) : (
        <div className="divide-y">
          {/* Chat list will be rendered here when there are chats */}
        </div>
      )}
    </div>
  );
}
