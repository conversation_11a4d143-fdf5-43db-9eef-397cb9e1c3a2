import React, { useState, useEffect, useRef } from "react";
import { useAuth } from "../../hooks/useAuth";
import { FiArrowDown, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>aperclip } from "react-icons/fi";
import useChatStore from "../../store/chatStore";
import Avatar from "../Avatar";
import LoadingSpinner from "../LoadingSpinner";
import { emitMessage } from "../../utils/socket";
import { chatService } from "../../services/api";
import { toast } from "react-hot-toast";
import { useMessages, useSendMessage } from "../../services/chatQueries";
import { formatDistanceToNow } from "date-fns";
import { BsCheckAll } from "react-icons/bs";
import MessageInput from "../MessageInput";
import VoiceNotePlayer from "../VoiceNotePlayer";
import { formatLastSeen } from "../../utils/formatTime";

const CreatorChatArea = ({ conversation }) => {
  const { user } = useAuth();
  const messagesEndRef = useRef(null);
  const [newMessage, setNewMessage] = useState("");
  const { getUserStatus } = useChatStore();

  const { data, isLoading } = useMessages(conversation?._id);
  const sendMessageMutation = useSendMessage();

  const messages = data?.messages || [];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (!conversation) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        Select a conversation to start messaging
      </div>
    );
  }

  const otherParticipant = conversation.participants.find(
    (p) => p._id !== user._id
  );

  // Get online status of the other participant
  const { isOnline, lastSeen } = otherParticipant
    ? getUserStatus(otherParticipant._id)
    : { isOnline: false, lastSeen: null };

  const handleSendMessage = async (messageData) => {
    try {
      const payload = {
        conversationId: conversation._id,
        receiverId: otherParticipant._id,
        content: messageData.content,
        messageType: messageData.messageType,
        media: messageData.media,
      };
      console.log("form payload 2", payload);
      await sendMessageMutation.mutateAsync(payload);
      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message");
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Chat Header */}
      <div className="p-4 border-b flex items-center gap-3 h-16">
        <div className="relative">
          <Avatar avatar={otherParticipant?.avatar} />
          {isOnline && (
            <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
          )}
        </div>
        <div>
          <h3 className="font-semibold">{otherParticipant?.username}</h3>
          <p className="text-xs text-gray-500">
            {isOnline ? (
              <span className="text-green-500">Online</span>
            ) : (
              lastSeen && <span>Last seen {formatLastSeen(lastSeen)}</span>
            )}
          </p>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => {
          const isOwnMessage = message.sender._id === user._id;
          return (
            <div
              key={message._id}
              className={`flex ${
                isOwnMessage ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`flex gap-2 max-w-[70%] ${
                  isOwnMessage ? "flex-row-reverse" : ""
                }`}
              >
                {!isOwnMessage && (
                  <Avatar
                    avatar={message.sender.avatar}
                    className="w-8 h-8 self-end"
                  />
                )}
                <div
                  className={`rounded-2xl p-3 ${
                    isOwnMessage
                      ? "bg-primary text-white"
                      : "bg-gray-100 text-gray-900"
                  }`}
                >
                  {message.messageType === "text" && (
                    <p className="whitespace-pre-wrap break-words">
                      {message.content}
                    </p>
                  )}
                  {message.messageType === "image" && (
                    <div className="">
                      <img
                        src={message.media.url}
                        alt="Image"
                        className="rounded-lg max-w-full"
                      />
                      {message.content && (
                        <p className="text-sm mt-2 text-white">
                          {message.content}
                        </p>
                      )}
                    </div>
                  )}
                  {message.messageType === "voice" && (
                    <VoiceNotePlayer audioUrl={message.media.url} />
                  )}
                  {message.messageType === "file" && (
                    <a
                      href={message.media.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-current"
                    >
                      <FiPaperclip className="w-4 h-4" />
                      <span className="underline">
                        {message.media.filename}
                      </span>
                    </a>
                  )}
                  <div
                    className={`text-xs mt-1 flex items-center gap-1 ${
                      isOwnMessage ? "text-white/80" : "text-gray-500"
                    }`}
                  >
                    <span>
                      {formatDistanceToNow(new Date(message.createdAt), {
                        addSuffix: true,
                      })}
                    </span>
                    {isOwnMessage && message.readBy?.length > 1 && (
                      <BsCheckAll className="text-blue-500" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <MessageInput
        value={newMessage}
        onChange={setNewMessage}
        onSubmit={handleSendMessage}
        isLoading={sendMessageMutation.isLoading}
      />
    </div>
  );
};

export default CreatorChatArea;
