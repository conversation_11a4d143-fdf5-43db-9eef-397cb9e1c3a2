import { useState } from "react";
import { Io<PERSON>earch, Io<PERSON><PERSON>er } from "react-icons/io5";
import { userService } from "../../services/api";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "../../hooks/useAuth";
import Pagination from "../Pagination";
import Skeleton from "../ui/Skeleton";
import { fCurrency } from "../../utils/formatNumber";

export default function SearchSubscribersModal({
  showSearch,
  onClose,
  onSelect,
}) {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const { user } = useAuth();
  const { data, isLoading, error, isPending } = useQuery({
    queryKey: ["subscribers", searchQuery, currentPage],
    queryFn: () =>
      userService.getCreatorSubscribers(user.username, {
        page: currentPage,
        limit: 10,
        search: searchQ<PERSON>y,
      }),
  });

  if (error) {
    toast.error(error.message);
  }

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  if (!showSearch) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start sm:items-start justify-center sm:pt-16 pt-0">
      <div className="bg-white w-full h-full sm:h-auto sm:max-w-2xl rounded-none sm:rounded-lg shadow-xl">
        <div className="w-full h-full">
          <div className="flex items-center justify-between p-3 sm:p-4 border-b">
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-sm sm:text-base"
            >
              Cancel
            </button>
            <h2 className="text-base sm:text-lg font-medium">
              All subscribers
            </h2>
            <div className="w-14"></div>
          </div>

          <div className="p-3 sm:p-4">
            <div className="relative flex items-center mb-4">
              <IoSearch className="absolute left-3 text-gray-400 text-lg sm:text-xl" />
              <input
                type="text"
                placeholder="Search for subscribers"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full pl-10 pr-4 py-2 text-sm sm:text-base bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
              <button className="absolute right-3">
                <IoFilter className="text-gray-400 text-lg sm:text-xl" />
              </button>
            </div>

            {isLoading ? (
              <div className="min-h-[400px] sm:min-h-[500px] flex items-center justify-center">
                <Skeleton count={3} variant="text" />
              </div>
            ) : !data?.subscribers?.length ? (
              <div className="min-h-[400px] sm:min-h-[500px] flex flex-col items-center justify-center text-center p-4 sm:p-8">
                <div className="w-16 h-16 sm:w-24 sm:h-24 mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <IoSearch className="text-2xl sm:text-4xl text-gray-400" />
                </div>
                <h3 className="text-base sm:text-lg text-gray-500 mb-2">
                  Oops! No subscribers found.
                </h3>
                <p className="text-sm sm:text-base text-gray-400">
                  Please double check your search term.
                </p>
              </div>
            ) : (
              <>
                <div className="min-h-[400px] sm:min-h-[500px] overflow-y-auto">
                  {data.subscribers.map((subscription) => (
                    <div
                      key={subscription._id}
                      onClick={() => {
                        onSelect(subscription);
                        onClose();
                      }}
                      className="flex items-center gap-3 sm:gap-4 p-3 sm:p-4 hover:bg-gray-50 border-b last:border-b-0"
                    >
                      <img
                        src={subscription.subscriber.avatar}
                        alt={subscription.subscriber.username}
                        className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 text-sm sm:text-base truncate">
                          @{subscription.subscriber.username}
                        </h4>
                        <div className="flex flex-wrap gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500">
                          <span>
                            Amount:{" "}
                            {fCurrency(subscription.planSnapshot.amount)}
                          </span>
                          <span className="hidden sm:inline">•</span>
                          <span>
                            Expires:{" "}
                            {new Date(
                              subscription.endDate
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <span
                        className={`px-2 py-1 text-xs rounded-full whitespace-nowrap ${
                          subscription.status === "active"
                            ? "bg-green-100 text-green-700"
                            : "bg-red-100 text-red-700"
                        }`}
                      >
                        {subscription.status}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={data.pagination.totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
