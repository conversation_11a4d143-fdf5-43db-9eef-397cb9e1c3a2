import BackButton from "../BackButton";
import { useQuery, useMutation } from "@tanstack/react-query";
import { automationService } from "../../services/api";
import LoadingSpinner from "../LoadingSpinner";
import { triggerEvents } from "../../utils/utils";
import { IoTimeOutline, IoCheckmarkCircle } from "react-icons/io5";
import { format } from "date-fns";
import DeleteModal from "../DeleteModal";
import { useState } from "react";
import { toast } from "react-hot-toast";
import CreateAutomation from "./CreateAutomation";

const AutomationSkeleton = () => (
  <div className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden animate-pulse">
    <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <div className="h-4 w-24 bg-gray-200 rounded"></div>
          <div className="h-4 w-16 bg-gray-200 rounded-full"></div>
        </div>
        <div className="h-4 w-32 bg-gray-200 rounded"></div>
      </div>
    </div>
    <div className="p-6">
      <div className="h-4 w-3/4 bg-gray-200 rounded mb-4"></div>
      <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
    </div>
    <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
      <div className="flex justify-end space-x-3">
        <div className="h-6 w-16 bg-gray-200 rounded"></div>
        <div className="h-6 w-16 bg-gray-200 rounded"></div>
      </div>
    </div>
  </div>
);

const formatDuration = (minutes) => {
  if (minutes >= 60 && minutes % 60 === 0) {
    const hours = minutes / 60;
    return `${hours} ${hours === 1 ? "hour" : "hours"}`;
  }
  return `${minutes} minutes`;
};

export default function CreatorAutomation({ onCreateClick, onEditClick }) {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [automationToDelete, setAutomationToDelete] = useState(null);

  const {
    data: automations,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["automations"],
    queryFn: async () => {
      const response = await automationService.getAutomations();
      return response.data;
    },
  });

  const { mutate: deleteAutomation, isPending: isDeleting } = useMutation({
    mutationFn: (id) => automationService.deleteAutomation(id),
    onSuccess: () => {
      toast.success("Automation deleted successfully");
      setDeleteModalOpen(false);
      refetch();
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to delete automation");
    },
  });

  const handleDelete = () => {
    deleteAutomation(automationToDelete);
    setAutomationToDelete(null);
  };

  if (isLoading) {
    return (
      <div className="p-4 space-y-4">
        <div className="flex justify-between items-center mb-6">
          <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-1 gap-4">
          {[1, 2, 3].map((i) => (
            <AutomationSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto h-full">
      {/* Empty State */}
      {automations?.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-32 h-32 mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            <svg
              className="w-16 h-16 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-heading text-secondary-DEFAULT mb-2">
            No automations found
          </h3>
          <p className="text-gray-500 mb-6">
            Create a new automation to get started
          </p>
          <button
            onClick={onCreateClick}
            className="px-6 py-3 bg-primary text-sm hover:bg-primary-400 text-white rounded-lg transition-colors duration-200"
          >
            Create new automation
          </button>
        </div>
      ) : (
        <div className="p-4 space-y-4">
          {/* Header with Create Button */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-base font-heading text-secondary-DEFAULT">
              Automated Messages
            </h2>
            <button
              onClick={onCreateClick}
              className="px-4 py-2 bg-primary text-sm hover:bg-primary-400 text-white rounded-lg transition-colors duration-200"
            >
              Create new
            </button>
          </div>

          {/* Automations List */}
          <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-1 gap-4">
            {automations?.map((automation) => (
              <div
                key={automation._id}
                className="bg-white rounded-lg border border-gray-200 shadow-soft overflow-hidden"
              >
                {/* Header */}
                <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-700">
                        {
                          triggerEvents.find(
                            (trigger) =>
                              trigger.value === automation.triggerEvent
                          )?.label
                        }
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      Created{" "}
                      {format(new Date(automation.createdAt), "MMM d, yyyy")}
                    </div>
                  </div>
                </div>

                {/* Message Content */}
                <div className="p-5">
                  <p className="text-gray-700 whitespace-pre-wrap mb-4 text-sm">
                    {automation.message}
                  </p>

                  {/* Timing Info */}
                  <div className="flex items-center text-xs text-gray-500">
                    <IoTimeOutline className="w-4 h-4 mr-2" />
                    <span>
                      {automation.timing.type === "immediately"
                        ? "Sends immediately"
                        : `Sends after ${formatDuration(
                            automation.timing.delayDuration
                          )}`}
                    </span>
                  </div>
                  {automation.isActive && (
                    <span className="px-2 mt-2 block max-w-fit py-1 text-xs font-medium text-primary-500 bg-primary-100 rounded-full">
                      Active
                    </span>
                  )}
                </div>

                {/* Actions */}
                <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => onEditClick(automation)}
                      className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => {
                        setDeleteModalOpen(true);
                        setAutomationToDelete(automation._id);
                      }}
                      className="px-3 py-1.5 text-sm text-error hover:text-error-500 transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      <DeleteModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onDelete={handleDelete}
        isLoading={isDeleting}
        title="Delete Automation"
        message="Are you sure you want to delete this automation?"
      />
    </div>
  );
}
