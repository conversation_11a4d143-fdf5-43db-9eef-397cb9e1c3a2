import React from "react";
import PropTypes from "prop-types";
import Post from "./Post";

const PostFeed = ({ posts, isSuggestedFeed }) => {
  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No posts to show</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {posts.map((post) => (
        <Post key={post._id} post={post} isSuggested={isSuggestedFeed} />
      ))}
    </div>
  );
};

PostFeed.propTypes = {
  posts: PropTypes.arrayOf(PropTypes.object).isRequired,
  isSuggestedFeed: PropTypes.bool,
};

PostFeed.defaultProps = {
  isSuggestedFeed: false,
};

export default PostFeed;
