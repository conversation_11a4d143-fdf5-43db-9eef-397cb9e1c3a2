import { IoArrowBack } from "react-icons/io5";
import { useNavigate } from "react-router-dom";

export default function BackButton({ onClick }) {
  const navigate = useNavigate();

  const handleClick = (e) => {
    if (onClick) {
      onClick(e);
    } else {
      navigate(-1);
    }
  };

  return (
    <button
      className="text-gray-500 hover:text-gray-700 p-1 hover:bg-gray-100 rounded-full"
      onClick={handleClick}
    >
      <IoArrowBack size={24} />
    </button>
  );
}
