import { useState } from "react";
import toast from "react-hot-toast";
import { useForm } from "react-hook-form";
import { Icon } from "@iconify/react";
import InteractiveButton from "../InteractiveButton";
import { useAuth } from "../../hooks/useAuth";
import PropTypes from "prop-types";

export default function EditSubscription({
  isOpen,
  onClose,
  initialPlan,
  planIndex,
}) {
  const [submitLoading, setSubmitLoading] = useState(false);
  const { user, updateProfile } = useAuth();
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      baseAmount: user.pricingPlans[0].amount,
      discountPercentage: initialPlan.discountPercentage || 0,
    },
  });

  // Watch for changes in base amount and discount
  const baseAmount = watch("baseAmount");
  const discountPercentage = watch("discountPercentage");

  // Calculate the final amount based on months and discount
  const calculatedAmount = baseAmount
    ? (
        baseAmount *
        initialPlan.noOfMonths *
        (1 - discountPercentage / 100)
      ).toFixed(2)
    : "";

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      const updatedPricing = [...user.pricingPlans];

      // Update base amount for all plans if editing first month
      if (planIndex === 0) {
        updatedPricing.forEach((plan, idx) => {
          const discount = plan.discountPercentage || 0;
          updatedPricing[idx] = {
            ...plan,
            amount: data.baseAmount * plan.noOfMonths * (1 - discount / 100),
          };
        });
      } else {
        // For other months, only update the discount and calculated amount
        updatedPricing[planIndex] = {
          ...initialPlan,
          amount: parseFloat(calculatedAmount),
          discountPercentage: data.discountPercentage,
        };
      }

      await updateProfile({ pricingPlans: updatedPricing });
      toast.success("Subscription plan updated successfully!");
      onClose();
    } catch (error) {
      console.error(error);
      if (error?.response?.data?.message) {
        toast.error(error?.response?.data?.message);
      } else {
        toast.error("Failed to update subscription plan");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  return (
    <div className="bg-white p-5 md:p-8 rounded-lg shadow-lg max-w-2xl w-full relative">
      <button
        className="absolute text-gray-600 hover:text-gray-900 top-3 right-3"
        onClick={onClose}
        type="button"
      >
        <Icon icon="ep:close" fontSize={24} />
      </button>
      <h2 className="text-xl font-bold mb-2">Edit Subscription Plan</h2>
      <p className="text-gray-600 mb-6">
        Update your subscription pricing for {initialPlan.noOfMonths} month
        {initialPlan.noOfMonths > 1 ? "s" : ""} plan
      </p>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="flex items-center bg-gray-50 rounded-lg px-4 py-3">
                <span className="text-purple-600 text-lg mr-2">₦</span>
                {planIndex === 0 ? (
                  <input
                    type="number"
                    placeholder="Amount"
                    className="bg-transparent outline-none w-full"
                    {...register("baseAmount", {
                      required: "Amount is required",
                      min: { value: 0, message: "Amount must be positive" },
                    })}
                  />
                ) : (
                  <input
                    type="number"
                    value={calculatedAmount}
                    className="bg-transparent outline-none w-full"
                    disabled
                  />
                )}
              </div>
              {errors.baseAmount && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.baseAmount.message}
                </p>
              )}
            </div>
            {initialPlan.noOfMonths > 1 && (
              <div className="w-40">
                <select
                  className="w-full border border-green-500 rounded-lg px-4 py-3 text-gray-700"
                  {...register("discountPercentage")}
                >
                  <option value={0}>0% off</option>
                  <option value={5}>5% off</option>
                  <option value={10}>10% off</option>
                  <option value={15}>15% off</option>
                  <option value={20}>20% off</option>
                </select>
              </div>
            )}
          </div>
        </div>

        <div className="flex gap-4 mt-8">
          <InteractiveButton
            isLoading={submitLoading}
            className="flex-1"
            type="submit"
          >
            Update plan
          </InteractiveButton>
          <button
            type="button"
            onClick={onClose}
            className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}

EditSubscription.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  initialPlan: PropTypes.shape({
    noOfMonths: PropTypes.number.isRequired,
    amount: PropTypes.number.isRequired,
    discountPercentage: PropTypes.number,
  }).isRequired,
  planIndex: PropTypes.number.isRequired,
};
