import usePostStore from "../../store/postStore";
import React from "react";
import InteractiveButton from "../InteractiveButton";
import { postService, userService } from "../../services/api";
import toast from "react-hot-toast";
import { IoClose } from "react-icons/io5";
import { MdOutlineImage, MdOutlineGif } from "react-icons/md";
import { FaRegSmile } from "react-icons/fa";
import { IoLocationOutline } from "react-icons/io5";
import EmojiPicker from "emoji-picker-react";
import { uploadService } from "../../services/api";
import { useNavigate } from "react-router-dom";

export default function CreatePost({ onFinish }) {
  const { addPost, isLoading, error } = usePostStore();
  const [content, setContent] = React.useState("");
  const [submitLoading, setSubmitLoading] = React.useState(false);
  const [mentions, setMentions] = React.useState([]);
  const [showMentionSuggestions, setShowMentionSuggestions] =
    React.useState(false);
  const [mentionSuggestions, setMentionSuggestions] = React.useState([]);
  const [cursorPosition, setCursorPosition] = React.useState(0);
  const [cursorVisible, setCursorVisible] = React.useState(true);
  const [showEmojiPicker, setShowEmojiPicker] = React.useState(false);
  const [mediaFiles, setMediaFiles] = React.useState([]);
  const [mediaPreview, setMediaPreview] = React.useState([]);
  const [isPublic, setIsPublic] = React.useState(false);
  const textareaRef = React.useRef(null);
  const fileInputRef = React.useRef(null);
  const navigate = useNavigate();

  React.useEffect(() => {
    const interval = setInterval(() => {
      setCursorVisible((prev) => !prev);
    }, 600); // Blink every 600ms

    return () => clearInterval(interval);
  }, []);

  // Function to handle text changes and detect @ mentions
  const handleContentChange = async (e) => {
    const newContent = e.target.value;
    setContent(newContent);

    // Get cursor position
    const cursorPos = e.target.selectionStart;
    setCursorPosition(cursorPos);

    // Check if we should show mention suggestions
    const textBeforeCursor = newContent.slice(0, cursorPos);
    const matches = textBeforeCursor.match(/@(\w*)$/);

    if (matches) {
      const searchTerm = matches[1];
      setShowMentionSuggestions(true);
      // Fetch user suggestions from your API
      try {
        const response = await userService.searchUsers(searchTerm);
        console.log("user response", response);
        setMentionSuggestions(response.users);
      } catch (error) {
        console.error("Error fetching user suggestions:", error);
      }
    } else {
      setShowMentionSuggestions(false);
    }
  };

  // Function to handle mention selection
  const handleMentionSelect = (user) => {
    const textBeforeCursor = content.slice(0, cursorPosition);
    const textAfterCursor = content.slice(cursorPosition);
    const mentionText = `@${user.username} `;

    // Replace the partial @mention with the full username
    const newContent =
      textBeforeCursor.replace(/@\w*$/, mentionText) + textAfterCursor;

    // Calculate new cursor position after the mention
    const newCursorPosition = textBeforeCursor.replace(
      /@\w*$/,
      mentionText
    ).length;

    setContent(newContent);
    setCursorPosition(newCursorPosition);
    setShowMentionSuggestions(false);

    // Only add the mention if it's not already in the array
    if (!mentions.includes(user._id)) {
      setMentions((prev) => [...prev, user._id]);
    }
  };

  const validateFile = (file) => {
    const maxSize = {
      image: 10 * 1024 * 1024, // 10MB for images
      video: 100 * 1024 * 1024, // 100MB for videos
      audio: 20 * 1024 * 1024, // 20MB for audio
    };

    const allowedTypes = {
      image: ["image/jpeg", "image/png", "image/gif", "image/webp"],
      video: ["video/mp4", "video/quicktime", "video/webm"],
      audio: ["audio/mpeg", "audio/wav", "audio/ogg"],
    };

    const mediaType = Object.keys(allowedTypes).find((type) =>
      allowedTypes[type].includes(file.type)
    );

    if (!mediaType) {
      toast.error(`File type ${file.type} is not supported`);
      return false;
    }

    if (file.size > maxSize[mediaType]) {
      toast.error(
        `File ${
          file.name
        } is too large. Maximum size for ${mediaType} is ${Math.floor(
          maxSize[mediaType] / 1024 / 1024
        )}MB`
      );
      return false;
    }

    return { valid: true, mediaType };
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const validatedFiles = files
      .map((file) => ({
        file,
        validation: validateFile(file),
      }))
      .filter(({ validation }) => validation.valid);

    if (validatedFiles.length + mediaFiles.length > 10) {
      toast.error("Maximum 10 files allowed");
      return;
    }

    setMediaFiles((prev) => [
      ...prev,
      ...validatedFiles.map(({ file }) => file),
    ]);

    // Generate previews
    validatedFiles.forEach(({ file, validation }) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        setMediaPreview((prev) => [
          ...prev,
          {
            url: reader.result,
            type: validation.mediaType,
            name: file.name,
            file: file,
          },
        ]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeMedia = (index) => {
    setMediaFiles((prev) => prev.filter((_, i) => i !== index));
    setMediaPreview((prev) => prev.filter((_, i) => i !== index));
  };

  const onEmojiClick = (emojiObject) => {
    const cursor = textareaRef.current.selectionStart;
    const text =
      content.slice(0, cursor) + emojiObject.emoji + content.slice(cursor);
    setContent(text);
    setShowEmojiPicker(false);
  };

  const handleSubmit = async () => {
    if (!content.trim() && mediaFiles.length === 0) {
      toast.error("Please add some content or media");
      return;
    }

    setSubmitLoading(true);
    const toastId = toast.loading("Creating your post...");

    try {
      let uploadedUrls = [];

      // First upload media files if any
      if (mediaFiles.length > 0) {
        toast.loading("Uploading media...", { id: toastId });
        const imageFormData = new FormData();
        mediaFiles.forEach((file) => {
          imageFormData.append("files", file);
        });

        const uploadResponse = await uploadService.uploadMultiple(
          imageFormData
        );
        uploadedUrls = uploadResponse.data.data;
        toast.loading("Media uploaded successfully, creating post...", {
          id: toastId,
        });
      }

      // Filter out any null or undefined mentions
      const validMentions = mentions.filter((mention) => mention);

      // Create the post data
      const postData = {
        content,
        mentions: validMentions,
        mediaUrls: uploadedUrls,
        isPublic,
      };

      // Create the post
      const response = await postService.createPost(postData);
      addPost(response.data.post);
      toast.success("Post created successfully!", { id: toastId });

      // Reset form
      setContent("");
      setMediaFiles([]);
      setMediaPreview([]);
      setMentions([]);
      setShowEmojiPicker(false);
      onFinish();
    } catch (error) {
      console.error("Error creating post:", error);
      toast.error(error?.response?.data?.message || "Failed to create post", {
        id: toastId,
      });
    } finally {
      setSubmitLoading(false);
    }
  };

  const renderHighlightedContent = () => {
    if (!content) {
      return (
        <React.Fragment>
          <span
            className={`w-0.5 h-5 bg-gray-400 inline-block -mb-0.5 ${
              cursorVisible ? "opacity-100" : "opacity-0"
            }`}
          />
        </React.Fragment>
      );
    }

    let parts = content.split(/(@\w+)/g);
    let currentPosition = 0;

    return parts.map((part, index) => {
      currentPosition += part.length;

      const element = part.match(/@\w+/) ? (
        <span key={index} className="text-primary font-medium">
          {part}
        </span>
      ) : (
        <span key={index}>{part}</span>
      );

      if (currentPosition === cursorPosition) {
        return (
          <React.Fragment key={`${index}-cursor`}>
            {element}
            <span
              className={`w-0.5 h-5 bg-black inline-block -mb-0.5 ${
                cursorVisible ? "opacity-100" : "opacity-0"
              }`}
            />
          </React.Fragment>
        );
      }

      return element;
    });
  };

  const handleKeyUp = (e) => {
    setCursorPosition(e.target.selectionStart);
  };

  const handleClick = (e) => {
    setCursorPosition(e.target.selectionStart);
  };

  // Add a cleanup function to handle component unmounting
  React.useEffect(() => {
    return () => {
      // Clean up any file references when component unmounts
      setMediaFiles([]);
      setMediaPreview([]);
    };
  }, []);

  const renderMediaPreview = (media, index) => {
    switch (media.type) {
      case "video":
        return (
          <div className="relative aspect-video">
            <video
              src={media.url}
              className="rounded-lg w-full h-full object-cover"
              controls
            />
            <div className="absolute top-2 right-2 flex gap-2">
              <span className="px-2 py-1 bg-black bg-opacity-50 text-white text-xs rounded-full">
                Video
              </span>
              <button
                onClick={() => removeMedia(index)}
                className="p-1 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70"
              >
                <IoClose className="w-4 h-4" />
              </button>
            </div>
          </div>
        );

      case "audio":
        return (
          <div className="relative bg-gray-100 p-4 rounded-lg">
            <audio src={media.url} controls className="w-full" />
            <div className="absolute top-2 right-2 flex gap-2">
              <span className="px-2 py-1 bg-black bg-opacity-50 text-white text-xs rounded-full">
                Audio
              </span>
              <button
                onClick={() => removeMedia(index)}
                className="p-1 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70"
              >
                <IoClose className="w-4 h-4" />
              </button>
            </div>
          </div>
        );

      default: // image
        return (
          <div className="relative aspect-square">
            <img
              src={media.url}
              className="rounded-lg w-full h-full object-cover"
              alt={media.name}
            />
            <div className="absolute top-2 right-2 flex gap-2">
              <span className="px-2 py-1 bg-black bg-opacity-50 text-white text-xs rounded-full">
                Image
              </span>
              <button
                onClick={() => removeMedia(index)}
                className="p-1 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70"
              >
                <IoClose className="w-4 h-4" />
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <div>
      <div className="relative mb-4">
        <textarea
          ref={textareaRef}
          className="w-full min-h-[120px] p-4 border rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-primary"
          value={content}
          onChange={handleContentChange}
          onKeyUp={handleKeyUp}
          onClick={handleClick}
          placeholder="What's on your mind?"
          autoFocus
        />

        {/* Mention suggestions */}
        {showMentionSuggestions && mentionSuggestions.length > 0 && (
          <div className="absolute left-0 right-0 bg-white border rounded-lg shadow-lg mt-1 max-h-48 overflow-y-auto z-10">
            {mentionSuggestions.map((user) => (
              <div
                key={user.id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center"
                onClick={() => handleMentionSelect(user)}
              >
                {user.avatar && (
                  <img
                    src={user.avatar}
                    alt={user.username}
                    className="w-6 h-6 rounded-full mr-2"
                  />
                )}
                <span>{user.username}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Media Preview Grid */}
      {mediaPreview.length > 0 && (
        <div
          className={`grid gap-2 mb-4 max-h-[400px] rounded-xl overflow-y-auto ${
            mediaPreview.length === 1
              ? "grid-cols-1"
              : mediaPreview.length === 2
              ? "grid-cols-2"
              : "grid-cols-2"
          }`}
        >
          {mediaPreview.map((media, index) => (
            <div key={index} className="relative">
              {renderMediaPreview(media, index)}
            </div>
          ))}
        </div>
      )}

      {/* Action Buttons */}
      <div className="border rounded-lg p-3 mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Add to your post</span>
          <div className="flex gap-2">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="p-2 hover:bg-gray-100 rounded-full text-green-500"
            >
              <MdOutlineImage className="w-6 h-6" />
            </button>

            <button
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              className="p-2 hover:bg-gray-100 rounded-full text-yellow-500"
            >
              <FaRegSmile className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>

      {/* Post Visibility Toggle */}
      <div className="border rounded-lg p-3 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <span className="text-sm font-medium">Post Visibility</span>
            <span className="text-xs text-gray-500">
              {isPublic
                ? "Anyone can view this post"
                : "Only subscribers can view this post"}
            </span>
          </div>
          <button
            onClick={() => setIsPublic(!isPublic)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${
              isPublic ? "bg-primary" : "bg-gray-200"
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition duration-200 ease-in-out ${
                isPublic ? "translate-x-6" : "translate-x-1"
              }`}
            />
          </button>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        multiple
        accept=".jpg,.jpeg,.png,.gif,.webp,.mp4,.mov,.webm"
        onChange={handleFileUpload}
      />

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <div className="absolute bottom-20 right-4">
          <EmojiPicker onEmojiClick={onEmojiClick} />
        </div>
      )}

      {/* Submit Button */}
      <InteractiveButton
        onClick={handleSubmit}
        isLoading={submitLoading}
        disabled={(!content.trim() && mediaFiles.length === 0) || submitLoading}
        className="w-full py-2 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {submitLoading ? (
          <div className="flex items-center justify-center gap-2">
            <span className="animate-pulse">Creating post</span>
            <span className="loading loading-dots"></span>
          </div>
        ) : (
          "Post"
        )}
      </InteractiveButton>
    </div>
  );
}

export const CreatePostModal = ({ isOpen, setIsOpen }) => {
  return (
    <div className="fixed inset-0 bg-black px-5 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-4 w-full max-w-xl">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Create post</h2>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700 p-1 hover:bg-gray-100 rounded-full"
          >
            <IoClose className="w-6 h-6" />
          </button>
        </div>
        <CreatePost
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          onFinish={() => setIsOpen(false)}
        />
      </div>
    </div>
  );
};
