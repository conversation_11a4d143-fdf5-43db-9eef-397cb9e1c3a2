import { Icon } from "@iconify/react";
import { useAuth } from "../../hooks/useAuth";
import { useState, useRef, useEffect } from "react";
import { userService } from "../../services/api";
import { Link } from "react-router-dom";
import { FiSearch, FiUser } from "react-icons/fi";
import Avatar from "../Avatar";
import Skeleton from "../ui/Skeleton";

export default function Suggestions() {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchRef = useRef(null);
  const [suggestedCreators, setSuggestedCreators] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const delayDebounceFn = setTimeout(async () => {
      if (searchTerm.trim()) {
        setIsSearching(true);
        try {
          const response = await userService.searchUsers(searchTerm);
          setSearchResults(response.users || []);
        } catch (error) {
          console.error("Error searching users:", error);
        } finally {
          setIsSearching(false);
        }
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm]);

  async function getSuggestedCreators() {
    setIsLoading(true);
    try {
      const response = await userService.getSuggestedCreators();
      setSuggestedCreators(response.creators || []);
    } catch (error) {
      console.error("Error getting suggested creators:", error);
    } finally {
      setIsLoading(false);
    }
  }
  useEffect(() => {
    getSuggestedCreators();
  }, []);

  return (
    <div>
      <div className="flex-1 max-w-xl mb-5">
        <div className="relative" ref={searchRef}>
          <input
            type="search"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-gray-100 rounded-lg py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
          <FiSearch className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" />

          {/* Search Results Dropdown */}
          {(searchResults.length > 0 || isSearching) && (
            <div className="absolute w-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 max-h-96 overflow-y-auto z-50">
              {isSearching ? (
                <div className="p-4">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="flex items-center gap-3 p-3">
                      <Skeleton variant="avatar" width={40} height={40} />
                      <div className="flex-1">
                        <Skeleton variant="text" className="w-32 mb-1" />
                        <Skeleton variant="text" className="w-24" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                searchResults.map((result) => (
                  <Link
                    key={result._id}
                    to={`/${result.username}`}
                    className="flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors"
                  >
                    {result.avatar ? (
                      <img
                        src={result.avatar}
                        alt={result.username}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                        <FiUser className="w-5 h-5 text-gray-500" />
                      </div>
                    )}
                    <div>
                      <p className="font-medium text-gray-900">
                        {result.displayName || result.username}
                      </p>
                      <p className="text-sm text-gray-500">
                        @{result.username}
                      </p>
                    </div>
                  </Link>
                ))
              )}
            </div>
          )}
        </div>
      </div>
      <div className="flex items-center justify-between mb-3">
        <p className="text-sm">Suggestions for you</p>
        <button
          onClick={getSuggestedCreators}
          className="flex items-center gap-2 text-sm text-gray-500"
          disabled={isLoading}
        >
          <Icon
            icon="solar:refresh-line-duotone"
            className={`w-6 h-6 transition-transform ${
              isLoading ? "animate-spin" : ""
            }`}
          />
        </button>
      </div>
      <div className="flex flex-col gap-3">
        {isLoading
          ? [...Array(3)].map((_, index) => <SkeletonCard key={index} />)
          : suggestedCreators.map((creator) => (
              <SuggestionCard key={creator._id} creator={creator} />
            ))}
      </div>
    </div>
  );
}

const SuggestionCard = ({ creator }) => {
  return (
    <Link
      to={`/${creator.username}`}
      className="relative overflow-hidden rounded-2xl bg-gray-100 shadow-sm"
    >
      {/* Cover Image */}
      <div
        className="h-24 w-full bg-cover bg-center"
        style={{
          backgroundImage: `url(${creator.coverImage || "/default-cover.jpg"})`,
        }}
      />

      {/* Profile Info */}
      <div className="p-3 text-sm">
        {/* Avatar and Name Section */}
        <div className="flex flex-col items-center -mt-14">
          <div className="relative">
            <Avatar avatar={creator.avatar} size="xl" />
          </div>
          <h3 className="mt-2 text-xl font-semibold text-gray-900 ">
            {creator.displayName || creator.name}
          </h3>
          <p className="text-gray-500">@{creator.username}</p>
        </div>

        {/* Stats Section */}
        <div className="flex justify-center gap-4 mt-4 text-sm">
          <div className="flex flex-col items-center">
            <span className="font-semibold text-gray-900 ">
              {creator.totalSubscribers}
            </span>
            <span className="text-gray-500">Subscribers</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="font-semibold text-gray-900 ">
              {creator.totalPhotos}
            </span>
            <span className="text-gray-500">Photos</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="font-semibold text-gray-900 ">
              {creator.totalVideos}
            </span>
            <span className="text-gray-500">Videos</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

const SkeletonCard = () => {
  return (
    <div className="relative overflow-hidden rounded-2xl bg-gray-100 shadow-sm">
      <Skeleton variant="box" height={112} className="rounded-none" />
      <div className="p-4">
        <div className="flex flex-col items-center -mt-12">
          <Skeleton variant="avatar" width={64} height={64} />
          <Skeleton variant="text" className="mt-2 w-32" />
          <Skeleton variant="text" className="w-24 mt-1" />
        </div>
        <div className="flex justify-center gap-4 mt-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="flex flex-col items-center">
              <Skeleton variant="text" className="w-8 mb-1" />
              <Skeleton variant="text" className="w-16" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export const SuggestionLayout = () => {
  return (
    <div className="sticky top-5 hidden lg:block w-[490px] lg:w-[490px] mt-5 px-4">
      <Suggestions />
    </div>
  );
};
