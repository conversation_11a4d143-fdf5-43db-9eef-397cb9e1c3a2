import { Icon } from "@iconify/react";
import { useAuth } from "../../hooks/useAuth";
import { useState, useRef, useEffect } from "react";
import { userService } from "../../services/api";
import { Link, useLocation } from "react-router-dom";
import { FiSearch, FiUser } from "react-icons/fi";
import Modal from "../Modal";
import Suggestions from "./suggestions";

export default function DashboardHeader() {
  const { user, logout } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const dropdownRef = useRef(null);
  const location = useLocation();

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    setIsSearchModalOpen(false);
  }, [location.pathname]);

  return (
    <>
      <header className="flex items-center justify-between py-2 px-3 rounded-xl">
        {/* <div className="flex items-center gap-2">
          <div>
            <Icon icon="solar:menu-dots-outline" fontSize={24} />
          </div>
          <span className="font-semibold text-xl text-primary">Errosphere</span>
        </div> */}
        <Link to="/">
          <img src="/logo-text.png" alt="Errosphere" className="w-32 h-auto" />
        </Link>

        {/* <div className="flex-1 max-w-xl mx-4">
          <div className="relative" ref={searchRef}>
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-gray-100 rounded-lg py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary/20"
            />
            <FiSearch className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" />

            {(searchResults.length > 0 || isSearching) && (
              <div className="absolute w-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 max-h-96 overflow-y-auto z-50">
                {isSearching ? (
                  <div className="p-4 text-center text-gray-500">
                    Searching...
                  </div>
                ) : (
                  searchResults.map((result) => (
                    <Link
                      key={result._id}
                      to={`/${result.username}`}
                      className="flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors"
                    >
                      {result.avatar ? (
                        <img
                          src={result.avatar}
                          alt={result.username}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <FiUser className="w-5 h-5 text-gray-500" />
                        </div>
                      )}
                      <div>
                        <p className="font-medium text-gray-900">
                          {result.displayName || result.username}
                        </p>
                        <p className="text-sm text-gray-500">
                          @{result.username}
                        </p>
                      </div>
                    </Link>
                  ))
                )}
              </div>
            )}
          </div>
        </div> */}

        <div className="flex items-center gap-2">
          <button
            className="p-2 hover:bg-gray-100 rounded-full transition-all duration-200 active:scale-95"
            onClick={() => setIsSearchModalOpen(true)}
          >
            <Icon icon="lucide:search" fontSize={24} color="gray" />
          </button>
        </div>
      </header>

      <Modal
        isOpen={isSearchModalOpen}
        onClose={() => setIsSearchModalOpen(false)}
        title="Search"
      >
        <div className="w-full">
          <Suggestions />
        </div>
      </Modal>
    </>
  );
}
