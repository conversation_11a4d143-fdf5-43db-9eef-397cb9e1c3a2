import { useState, useEffect, useMemo } from "react";
import toast from "react-hot-toast";
import { useForm } from "react-hook-form";
import { Icon } from "@iconify/react";
import InteractiveButton from "../InteractiveButton";
import { useAuth } from "../../hooks/useAuth";
import PropTypes from "prop-types";

export default function CreateSubscription({ onClose }) {
  const [submitLoading, setSubmitLoading] = useState(false);
  const { updateProfile } = useAuth();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      plans: [
        { noOfMonths: 1, amount: "", discountPercentage: 0 },
        { noOfMonths: 2, amount: "", discountPercentage: 0 },
        { noOfMonths: 3, amount: "", discountPercentage: 0 },
        { noOfMonths: 4, amount: "", discountPercentage: 0 },
      ],
    },
  });

  // Watch for changes in the base amount and discount percentages
  const firstMonthAmount = watch("plans.0.amount");
  const discountPercentages = useMemo(
    () => [
      0,
      watch("plans.1.discountPercentage"),
      watch("plans.2.discountPercentage"),
      watch("plans.3.discountPercentage"),
    ],
    [
      watch("plans.1.discountPercentage"),
      watch("plans.2.discountPercentage"),
      watch("plans.3.discountPercentage"),
    ]
  );

  // Add this useEffect to update form values when dependencies change
  useEffect(() => {
    if (firstMonthAmount) {
      [2, 3, 4].forEach((noOfMonths, index) => {
        const actualIndex = index + 1;
        const calculatedAmount = (
          firstMonthAmount *
          noOfMonths *
          (1 - discountPercentages[actualIndex] / 100)
        ).toFixed(2);
        setValue(`plans.${actualIndex}.amount`, calculatedAmount);
      });
    }
  }, [firstMonthAmount, discountPercentages, setValue]);

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      await updateProfile({ pricingPlans: data.plans });

      toast.success("Subscription plans created successfully!");
      onClose();
    } catch (error) {
      if (error?.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Failed to create subscription plans");
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <div className="bg-white p-5 md:p-8 rounded-lg shadow-lg max-w-2xl w-full relative">
      <button
        className="absolute text-gray-600 hover:text-gray-900 top-3 right-3"
        onClick={handleCancel}
        type="button"
      >
        <Icon icon="ep:close" fontSize={24} className="" />
      </button>
      <h2 className="text-xl font-bold mb-2">Configure Subscription Plans</h2>
      <p className="text-gray-600 mb-6">
        Define your subscription pricing tiers and apply discounts for extended
        periods. All discounts are calculated using the monthly base rate as
        reference.
      </p>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-6">
          {[1, 2, 3, 4].map((noOfMonths, index) => (
            <div key={index} className="flex items-end gap-4">
              <div className="flex-1">
                <div className="mb-2 text-sm md:text-base">
                  {noOfMonths} month{noOfMonths > 1 ? "s" : ""} subscription
                  plan
                </div>

                <div className="flex items-center bg-gray-50 rounded-lg px-4 py-3">
                  <span className="text-purple-600 text-lg mr-2">₦</span>
                  {index === 0 ? (
                    <input
                      type="number"
                      placeholder="Amount"
                      className="bg-transparent outline-none w-full"
                      {...register(`plans.${index}.amount`, {
                        required: "Amount is required",
                        min: { value: 0, message: "Amount must be positive" },
                      })}
                    />
                  ) : (
                    <input
                      type="number"
                      {...register(`plans.${index}.amount`)}
                      value={
                        firstMonthAmount
                          ? (
                              firstMonthAmount *
                              noOfMonths *
                              (1 - discountPercentages[index] / 100)
                            ).toFixed(2)
                          : ""
                      }
                      className="bg-transparent outline-none w-full"
                      disabled
                    />
                  )}
                </div>
                {index === 0 && errors.plans?.[index]?.amount && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.plans[index].amount.message}
                  </p>
                )}
              </div>
              {index > 0 && (
                <div className="w-40">
                  <select
                    className="w-full border border-green-500 rounded-lg px-4 py-3 text-gray-700"
                    {...register(`plans.${index}.discountPercentage`)}
                  >
                    <option value={0}>0% off</option>
                    <option value={5}>5% off</option>
                    <option value={10}>10% off</option>
                    <option value={15}>15% off</option>
                    <option value={20}>20% off</option>
                  </select>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex gap-4 mt-8">
          <InteractiveButton
            isLoading={submitLoading}
            className="flex-1"
            type="submit"
          >
            Create subscription tier
          </InteractiveButton>
          <button
            type="button"
            onClick={handleCancel}
            className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}

CreateSubscription.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};
