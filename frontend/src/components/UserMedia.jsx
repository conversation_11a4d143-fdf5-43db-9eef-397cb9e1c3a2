import React, { useState, useRef } from "react";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";
import { FaPlay } from "react-icons/fa";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import Video from "yet-another-react-lightbox/plugins/video";

const UserMedia = ({ media = [] }) => {
  const navigate = useNavigate();
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const videoRefs = useRef({});

  const slides = media.map((item) => ({
    src: item.url.url,
    type: item.url.type === "video" ? "video" : "image",
    ...(item.url.type === "video" && {
      sources: [
        {
          src: item.url.url,
          type: `video/${item.url.format}`,
        },
      ],
    }),
  }));

  return (
    <div className="p-4">
      <div className="grid grid-cols-1 md:grid-cols-2  gap-1">
        {media.map((item, index) => (
          <div
            key={item.url.public_id}
            className="relative cursor-pointer aspect-square"
            onClick={() => {
              setLightboxIndex(index);
              setIsLightboxOpen(true);
            }}
          >
            {item.url.type === "video" ? (
              <div className="relative w-full h-full bg-gray-100 group">
                <video
                  ref={(el) => {
                    videoRefs.current[index] = el;
                    if (el) {
                      el.currentTime = 0;
                      el.pause();
                    }
                  }}
                  src={item.url.url}
                  preload="metadata"
                  className="w-full h-full object-cover"
                  playsInline
                  muted
                  onClick={(e) => {
                    e.stopPropagation();
                    const video = videoRefs.current[index];
                    if (video.paused) {
                      video.play();
                    } else {
                      video.pause();
                    }
                  }}
                />
                <div className="absolute inset-0 bg-black/10 flex items-center justify-center opacity-90 group-hover:opacity-100 transition-opacity">
                  <div className="w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center">
                    <FaPlay className="text-white text-xl ml-1" />
                  </div>
                </div>
              </div>
            ) : (
              <img
                src={item.url.url}
                alt={`Media ${index + 1}`}
                className="w-full h-full object-cover"
              />
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/post/${item.postId}`);
              }}
              className="absolute bottom-2 right-2 bg-black/50 backdrop-blur-sm text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        ))}
      </div>
      <Lightbox
        open={isLightboxOpen}
        close={() => setIsLightboxOpen(false)}
        index={lightboxIndex}
        slides={slides}
        plugins={[Video]}
      />
    </div>
  );
};

UserMedia.propTypes = {
  media: PropTypes.arrayOf(
    PropTypes.shape({
      url: PropTypes.shape({
        url: PropTypes.string.isRequired,
        public_id: PropTypes.string.isRequired,
        asset_id: PropTypes.string.isRequired,
        type: PropTypes.oneOf(["image", "video"]).isRequired,
        format: PropTypes.string.isRequired,
        width: PropTypes.number.isRequired,
        height: PropTypes.number.isRequired,
        thumbnail: PropTypes.string,
        duration: PropTypes.number,
      }).isRequired,
      createdAt: PropTypes.string.isRequired,
      postId: PropTypes.string.isRequired,
    })
  ).isRequired,
};

export default UserMedia;
