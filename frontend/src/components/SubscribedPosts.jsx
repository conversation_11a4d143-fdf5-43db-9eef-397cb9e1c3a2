import React, { useEffect } from "react";
import { usePostStore } from "../store/postStore";
import Post from "./Post";
import { BiLoaderAlt } from "react-icons/bi";

const SubscribedPosts = () => {
  const { subscribedPosts, isLoading, error, loadSubscribedPosts } =
    usePostStore();

  useEffect(() => {
    loadSubscribedPosts();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <BiLoaderAlt className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">{error}</div>;
  }

  if (!subscribedPosts?.length) {
    return (
      <div className="text-center py-8 text-gray-500">
        No posts from subscribed creators yet. Subscribe to some creators to see
        their content here!
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {subscribedPosts.map((post) => (
        <Post key={post._id} post={post} />
      ))}
    </div>
  );
};

export default SubscribedPosts;
