import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import AccountSetupStatus from './AccountSetupStatus';

// Mock user data for testing different states
const mockUsers = {
  newUser: {
    _id: '1',
    username: 'newuser',
    email: '<EMAIL>',
    isEmailVerified: false,
    isVerified: false,
    verified: false,
    avatar: null,
    displayName: null,
    about: null,
    creatorCategory: null,
    coverImage: null,
    paymentInfo: { verified: false },
    pricingPlans: [],
    totalPosts: 0,
  },
  
  partiallyCompleteUser: {
    _id: '2',
    username: 'partialuser',
    email: '<EMAIL>',
    isEmailVerified: true,
    isVerified: true,
    verified: false,
    avatar: 'avatar.jpg',
    displayName: 'Partial User',
    about: 'This is my bio',
    creatorCategory: 'fitness',
    coverImage: null,
    paymentInfo: { verified: false },
    pricingPlans: [],
    totalPosts: 0,
  },
  
  fullyCompleteUser: {
    _id: '3',
    username: 'completeuser',
    email: '<EMAIL>',
    isEmailVerified: true,
    isVerified: true,
    verified: true,
    avatar: 'avatar.jpg',
    displayName: 'Complete User',
    about: 'This is my bio',
    creatorCategory: 'fitness',
    coverImage: 'cover.jpg',
    paymentInfo: { verified: true },
    pricingPlans: [{ id: 1, amount: 10 }],
    totalPosts: 5,
  }
};

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('AccountSetupStatus Component', () => {
  test('shows setup component for new user', () => {
    renderWithRouter(<AccountSetupStatus user={mockUsers.newUser} />);
    
    expect(screen.getByText('Complete Your Creator Setup')).toBeInTheDocument();
    expect(screen.getByText('Verify Email')).toBeInTheDocument();
    expect(screen.getByText('Complete Profile')).toBeInTheDocument();
    expect(screen.getByText('Set Creator Category')).toBeInTheDocument();
    expect(screen.getByText('Payment Details')).toBeInTheDocument();
    expect(screen.getByText('Subscription Plans')).toBeInTheDocument();
  });

  test('shows progress for partially complete user', () => {
    renderWithRouter(<AccountSetupStatus user={mockUsers.partiallyCompleteUser} />);
    
    expect(screen.getByText('Complete Your Creator Setup')).toBeInTheDocument();
    // Should show some completed steps
    expect(screen.getAllByText('Completed')).toHaveLength(3); // Email, Profile, Category
  });

  test('hides component for fully complete and verified user', () => {
    renderWithRouter(<AccountSetupStatus user={mockUsers.fullyCompleteUser} />);
    
    // Component should not render anything
    expect(screen.queryByText('Complete Your Creator Setup')).not.toBeInTheDocument();
  });

  test('shows success message when required steps are complete but not verified', () => {
    const almostCompleteUser = {
      ...mockUsers.fullyCompleteUser,
      verified: false, // Not verified yet
    };
    
    renderWithRouter(<AccountSetupStatus user={almostCompleteUser} />);
    
    expect(screen.getByText('Great! You\'re ready to start earning')).toBeInTheDocument();
  });
});

// Manual test function to see component states
export const testAccountSetupStates = () => {
  console.log('Testing AccountSetupStatus component states:');
  
  console.log('\n1. New User State:');
  console.log('- Should show all setup steps as incomplete');
  console.log('- Should show "Complete required steps to start earning" message');
  
  console.log('\n2. Partially Complete User State:');
  console.log('- Should show some steps as completed');
  console.log('- Should still show setup component');
  
  console.log('\n3. Fully Complete User State:');
  console.log('- Should hide the component entirely');
  console.log('- Component returns null');
  
  console.log('\n4. Required Complete but Not Verified:');
  console.log('- Should show "Great! You\'re ready to start earning" message');
  console.log('- Should encourage completing optional steps');
};
