import React, { useState, useRef, useEffect } from "react";
import { BsPlayFill, BsPauseFill } from "react-icons/bs";

const VoiceNotePlayer = ({ audioUrl }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [audioData, setAudioData] = useState(null);
  const audioRef = useRef(null);
  const canvasRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    const fetchAudioData = async () => {
      try {
        const response = await fetch(audioUrl);
        const arrayBuffer = await response.arrayBuffer();
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        const channelData = audioBuffer.getChannelData(0);
        const dataPoints = reduceData(channelData, 100);
        setAudioData(dataPoints);
      } catch (error) {
        console.error("Error loading audio data:", error);
      }
    };

    fetchAudioData();
  }, [audioUrl]);

  useEffect(() => {
    if (audioData && canvasRef.current) {
      drawWaveform();
    }
  }, [audioData, currentTime]);

  const reduceData = (data, numPoints) => {
    const blockSize = Math.floor(data.length / numPoints);
    const reduced = [];

    for (let i = 0; i < numPoints; i++) {
      const start = i * blockSize;
      const end = start + blockSize;
      const chunk = data.slice(start, end);
      const amplitude = Math.max(...chunk.map(Math.abs));
      reduced.push(amplitude);
    }

    return reduced;
  };

  const drawWaveform = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    const width = canvas.width;
    const height = canvas.height;

    ctx.clearRect(0, 0, width, height);

    if (!audioData) return;

    const barWidth = width / audioData.length;
    const playedPercentage = (currentTime / duration) * width;

    audioData.forEach((amplitude, index) => {
      const x = index * barWidth;
      const barHeight = amplitude * (height / 2);

      // Draw played part
      if (x <= playedPercentage) {
        ctx.fillStyle = "#9CA3AF"; // gray-400
      } else {
        ctx.fillStyle = "#2A6B57"; // Original color
      }

      ctx.fillRect(x, height / 2 - barHeight / 2, barWidth - 1, barHeight);
    });
  };

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.addEventListener("loadedmetadata", () => {
        setDuration(audioRef.current.duration);
      });

      audioRef.current.addEventListener("timeupdate", () => {
        setCurrentTime(audioRef.current.currentTime);
      });

      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false);
        setCurrentTime(0);
      });
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("loadedmetadata", () => {});
        audioRef.current.removeEventListener("timeupdate", () => {});
        audioRef.current.removeEventListener("ended", () => {});
      }
    };
  }, []);

  const togglePlayPause = () => {
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className="flex items-center gap-2 bg-transparent rounded-full p-1.5 w-full max-w-[300px]">
      <audio ref={audioRef} src={audioUrl} className="hidden" />

      <button
        onClick={togglePlayPause}
        className="w-8 h-8 flex items-center justify-center bg-gray-400 rounded-full hover:bg-gray-500 transition-colors"
      >
        {isPlaying ? (
          <BsPauseFill className="text-white w-5 h-5" />
        ) : (
          <BsPlayFill className="text-white w-5 h-5 ml-0.5" />
        )}
      </button>

      <div className="flex-1 flex items-center">
        <canvas
          ref={canvasRef}
          className="w-full h-8"
          width={200}
          height={32}
        />
      </div>

      <span className="text-gray-300 text-sm min-w-[45px]">
        {formatTime(currentTime)}
      </span>
    </div>
  );
};

export default VoiceNotePlayer;
