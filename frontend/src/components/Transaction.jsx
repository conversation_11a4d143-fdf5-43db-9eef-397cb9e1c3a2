import Table from "./Table";
import { RiEmotionSadLine } from "react-icons/ri";
import { fCurrency } from "../utils/formatNumber";
import { Icon } from "@iconify/react";
import { getTipDescription } from "../utils/utils";

const columns = [
  {
    name: "Transaction ID",
    accessor: "_id",
  },
  {
    name: "Amount",
    accessor: "amount",
    cell: (row) => {
      return <span>{fCurrency(row?.amount)}</span>;
    },
  },
  {
    name: "Status",
    accessor: "status",
  },

  {
    name: "Date",
    accessor: "createdAt",
    cell: (row) => {
      return <span>{new Date(row.createdAt).toLocaleDateString()}</span>;
    },
  },
  {
    name: "Purpose",
    accessor: "purpose",
    cell: (row) => {
      return (
        <div className="flex items-center gap-2">
          <span>
            {row?.purpose === "tip" &&
              row?.transactionType === "credit" &&
              `${getTipDescription(row?.tipType)} from ${
                row?.recipientId?.username
              }`}
            {row?.purpose === "tip" &&
              row?.transactionType === "debit" &&
              `${getTipDescription(row?.tipType)} to ${
                row?.recipientId?.username
              }`}
          </span>
          <span>
            {row?.purpose === "withdrawal" && <Icon name="mdi:withdrawal" />}
          </span>
          <span>
            {row?.purpose === "deposit" && <Icon name="mdi:deposit" />}
          </span>
          <span>
            {row?.purpose === "transfer" && <Icon name="mdi:transfer-down" />}
          </span>
        </div>
      );
    },
  },
  // {
  //   name: "Transaction Type",
  //   accessor: "transactionType",
  //   cell: (row) => {
  //     return (
  //       <span>
  //         {row?.transactionType === "credit" ? (
  //           <Icon name="credit" />
  //         ) : (
  //           <Icon name="debit" />
  //         )}
  //       </span>
  //     );
  //   },
  // },
  // {
  //   name: "Action",
  //   accessor: "action",
  // },
];

export default function Transaction({ transactions }) {
  if (!transactions || transactions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow-sm">
        <RiEmotionSadLine className="w-16 h-16 text-gray-400 mb-4" />
        <h3 className="text-xl font-medium text-gray-900 mb-2">
          No Transactions Yet
        </h3>
        <p className="text-gray-500">You haven't made any transactions yet.</p>
      </div>
    );
  }

  return (
    <div>
      <Table columns={columns} data={transactions} />
    </div>
  );
}
