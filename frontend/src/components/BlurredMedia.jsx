import React from "react";
import { FaLock } from "react-icons/fa";

const BlurredMedia = ({ url, shouldBlur, type = "image", className = "" }) => {
  const isVideo = url.match(/\.(mp4|webm|ogg)$/i) || type === "video";

  const blurredStyle = shouldBlur
    ? {
        filter: "blur(20px)",
        WebkitFilter: "blur(20px)",
        transform: "scale(1.1)",
        pointerEvents: "none",
        userSelect: "none",
      }
    : {};

  const containerStyle = shouldBlur
    ? {
        overflow: "hidden",
        position: "relative",
      }
    : {};

  const OverlayMessage = () =>
    shouldBlur && (
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/50 text-white z-10">
        <FaLock className="w-8 h-8 mb-2" />
        <p className="text-center px-4">Subscribe to view this content</p>
      </div>
    );

  if (isVideo) {
    return (
      <div style={containerStyle} className={`relative ${className}`}>
        <OverlayMessage />
        <video
          className="w-full h-full object-cover"
          style={blurredStyle}
          controls={!shouldBlur}
          controlsList="nodownload"
          onContextMenu={(e) => e.preventDefault()}
        >
          <source src={url} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>
    );
  }

  return (
    <div style={containerStyle} className={`relative ${className}`}>
      <OverlayMessage />
      <img
        src={url}
        alt="Post media"
        className="w-full h-full object-cover"
        style={blurredStyle}
        onContextMenu={(e) => e.preventDefault()}
        draggable="false"
      />
    </div>
  );
};

export default BlurredMedia;
