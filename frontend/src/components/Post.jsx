import React, { useState, useRef } from "react";
import PropTypes from "prop-types";
import { formatDistanceToNow } from "date-fns";
import { Icon } from "@iconify/react";
import { useNavigate, useLocation } from "react-router-dom";
import usePostStore from "../store/postStore";
import { useAuth } from "../hooks/useAuth";
import { postService } from "../services/api";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import Video from "yet-another-react-lightbox/plugins/video";
import { FaPlay } from "react-icons/fa";
import TipModal from "./TipModal";

const Post = ({ post, isSuggested }) => {
  const navigate = useNavigate();
  const { likePost } = usePostStore();
  const { user } = useAuth();
  const location = useLocation();
  const isPostDetail = location.pathname.includes("/post/");
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [isTipModalOpen, setIsTipModalOpen] = useState(false);
  const videoRefs = useRef({});

  const handleLikePost = async () => {
    try {
      // Update the store state
      likePost(post._id, user?._id);

      // Create a new post object with updated likes and likesCount
      const updatedPost = {
        ...post,
        likes: post.likes.includes(user?._id)
          ? post.likes.filter((id) => id !== user?._id)
          : [...post.likes, user?._id],
        likesCount: post.likes.includes(user?._id)
          ? post.likesCount - 1
          : post.likesCount + 1,
      };

      // Update the post reference
      post.likes = updatedPost.likes;
      post.likesCount = updatedPost.likesCount;

      // Make the API call
      await postService.likePost(post?._id);
    } catch (error) {
      console.error("Error liking post:", error);
      // Revert the optimistic update on error
      likePost(post._id, user?._id);
    }
  };

  // Function to render media grid based on number of items
  const renderMediaGrid = () => {
    if (!post.mediaUrls || post.mediaUrls.length === 0) return null;

    const mediaCount = post.mediaUrls.length;
    const slides = post.mediaUrls.map((media) => ({
      src: media.url,
      type: media.type === "video" ? "video" : "image",
      ...(media.type === "video" && {
        sources: [
          {
            src: media.url,
            type: "video/mp4",
          },
        ],
      }),
    }));

    return (
      <>
        <div
          className={`grid gap-1 ${
            mediaCount === 1
              ? "grid-cols-1"
              : mediaCount === 2
              ? "grid-cols-2"
              : mediaCount === 3
              ? "grid-cols-2"
              : "grid-cols-2"
          }`}
        >
          {post.mediaUrls
            .slice(0, Math.min(3, mediaCount))
            .map((media, index) => {
              const isLastItem = index === 2 && mediaCount > 3;
              const remainingCount = mediaCount - 3;

              return (
                <div
                  key={media.public_id}
                  className={`relative cursor-pointer w-full ${
                    mediaCount === 3 && index === 0
                      ? "row-span-2"
                      : mediaCount === 1
                      ? "aspect-[4/3]"
                      : "aspect-square"
                  } ${
                    mediaCount > 2 && index === 0 ? "col-span-2" : ""
                  } max-h-[300px] sm:max-h-[400px] md:max-h-[450px] lg:max-h-[500px] overflow-hidden`}
                  onClick={() => {
                    setLightboxIndex(index);
                    setIsLightboxOpen(true);
                  }}
                >
                  {media.type === "video" ? (
                    <div className="relative w-full h-full bg-gray-100 group">
                      <video
                        ref={(el) => {
                          videoRefs.current[index] = el;
                          if (el) {
                            el.currentTime = 0;
                            el.pause();
                          }
                        }}
                        src={media.url}
                        preload="metadata"
                        className="w-full h-full object-cover rounded-lg max-h-[300px] sm:max-h-[400px] md:max-h-[450px] lg:max-h-[500px]"
                        playsInline
                        muted
                        onClick={(e) => {
                          e.stopPropagation();
                          const video = videoRefs.current[index];
                          if (video.paused) {
                            video.play();
                          } else {
                            video.pause();
                          }
                        }}
                      />
                      <div className="absolute inset-0 bg-black/10 flex items-center justify-center opacity-90 group-hover:opacity-100 transition-opacity">
                        <div className="w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center">
                          <FaPlay className="text-white text-xl ml-1" />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <img
                      src={media.url}
                      alt={`Post media ${index + 1}`}
                      className="w-full h-full object-cover rounded-lg max-h-[300px] sm:max-h-[400px] md:max-h-[450px] lg:max-h-[500px]"
                    />
                  )}
                  {isLastItem && remainingCount > 0 && (
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center rounded-lg">
                      <span className="text-white text-2xl font-semibold">
                        +{remainingCount}
                      </span>
                    </div>
                  )}
                </div>
              );
            })}
        </div>
        <Lightbox
          open={isLightboxOpen}
          close={() => setIsLightboxOpen(false)}
          index={lightboxIndex}
          slides={slides}
          plugins={[Video]}
        />
      </>
    );
  };

  // Function to render content with clickable mentions
  const renderContentWithMentions = () => {
    if (!post.content) return null;

    const mentionPattern = /@(\w+)/g;
    const parts = post.content.split(mentionPattern);

    return (
      <p className="text-gray-800">
        {parts.map((part, index) => {
          const mentionedUser = post.mentions?.find((m) => m.username === part);
          if (mentionedUser) {
            return (
              <button
                key={index}
                onClick={() => navigate(`/${mentionedUser.username}`)}
                className="text-primary hover:text-primary/80 font-medium"
              >
                @{mentionedUser.username}
              </button>
            );
          }
          return part;
        })}
      </p>
    );
  };

  return (
    <>
      <div
        className={`bg-white rounded-xl shadow-sm p-4 space-y-4 border border-gray-200 ${
          isSuggested ? "border border-primary/30" : ""
        }`}
      >
        {/* Post Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button onClick={() => navigate(`/${post?.creator?.username}`)}>
              {post?.creator?.avatar ? (
                <img
                  src={post?.creator?.avatar}
                  alt="Profile"
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                  <Icon fontSize={24} icon="ph:user" />
                </div>
              )}
            </button>
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium">
                  {post?.creator?.displayName || post?.creator?.username}
                </h3>
                {isSuggested && post?.creator?._id !== user?._id && (
                  <span className="text-xs px-2 py-1 bg-gradient-to-r from-primary/10 to-primary/20 text-primary rounded-full font-medium flex items-center gap-1">
                    <Icon icon="ph:sparkle" className="w-4 h-4" />
                    Suggested
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500">
                {formatDistanceToNow(new Date(post?.createdAt), {
                  addSuffix: true,
                })}
              </p>
            </div>
          </div>
          <button className="text-gray-400 hover:text-gray-600">
            <Icon icon="ph:dots-three-vertical-bold" className="w-6 h-6" />
          </button>
        </div>

        {/* Post Content */}
        <div className="space-y-3">
          {renderContentWithMentions()}
          {renderMediaGrid()}
        </div>

        {/* Post Actions */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <button
                onClick={handleLikePost}
                className="text-gray-500 hover:text-gray-700"
              >
                <span className="flex items-center space-x-1">
                  {post?.likes.includes(user?._id) ? (
                    <Icon
                      icon="icon-park-solid:like"
                      color="red"
                      fontSize={20}
                    />
                  ) : (
                    <Icon icon="icon-park-outline:like" fontSize={20} />
                  )}
                  <span className="text-sm">Like</span>
                </span>
              </button>
              <span className="text-gray-500 text-sm">{post?.likesCount}</span>
            </div>

            <div className="flex items-center space-x-1">
              <button
                onClick={() =>
                  isPostDetail ? null : navigate(`/post/${post?._id}`)
                }
                className="text-gray-500 hover:text-gray-700 flex items-center"
              >
                <Icon icon="ph:chat-circle" className="w-5 h-5 mr-1" />
                <span className="text-sm">Comment</span>
              </button>
              <span className="text-gray-500 text-sm">
                {post?.commentsCount}
              </span>
            </div>
          </div>

          {isSuggested && post?.creator?._id !== user?._id && (
            <div>
              <button
                onClick={() => setIsTipModalOpen(true)}
                className="text-primary hover:text-primary/80 border border-gray-200 rounded-full px-4 py-1 text-sm"
              >
                Tip
              </button>
            </div>
          )}
        </div>
      </div>
      <TipModal
        isOpen={isTipModalOpen}
        onClose={() => setIsTipModalOpen(false)}
        post={post}
        recipient={post?.creator}
        tipType="post_tip"
      />
    </>
  );
};

Post.propTypes = {
  post: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    content: PropTypes.string.isRequired,
    creator: PropTypes.shape({
      _id: PropTypes.string.isRequired,
      username: PropTypes.string.isRequired,
      displayName: PropTypes.string,
      avatar: PropTypes.string,
    }).isRequired,
    mediaUrls: PropTypes.arrayOf(
      PropTypes.shape({
        url: PropTypes.string.isRequired,
        public_id: PropTypes.string.isRequired,
        type: PropTypes.oneOf(["image", "video"]).isRequired,
        thumbnail: PropTypes.string,
      })
    ),
    mentions: PropTypes.arrayOf(
      PropTypes.shape({
        _id: PropTypes.string.isRequired,
        username: PropTypes.string.isRequired,
      })
    ),
    createdAt: PropTypes.string.isRequired,
    likes: PropTypes.arrayOf(PropTypes.string).isRequired,
    likesCount: PropTypes.number.isRequired,
    commentsCount: PropTypes.number.isRequired,
  }).isRequired,
  isSuggested: PropTypes.bool,
};

Post.defaultProps = {
  isSuggested: false,
};

export default Post;
