import useStoryStore from "../store/storyStore";
import { useEffect, useState, useMemo } from "react";
import StoryViewer from "./stories/StoryViewer";
import StoryCard from "./StoryCard";
import { storyService } from "../services/api";
import { useAuth } from "../hooks/useAuth";

export default function Stories() {
  const { stories, isLoading, error, loadStories } = useStoryStore();
  const [selectedCreatorIndex, setSelectedCreatorIndex] = useState(null);
  const [selectedStoryIndex, setSelectedStoryIndex] = useState(0);
  const { user } = useAuth();

  useEffect(() => {
    loadStories();
  }, []);

  async function onViewStory(story) {
    try {
      const alreadyViewed = story.viewers?.some(
        (viewer) => viewer.user == user._id
      );
      if (!alreadyViewed) {
        console.log("viewing story", story);
        await storyService.viewStory(story._id);
      } else {
        console.log("already viewed");
      }
    } catch (error) {
      console.log(error);
    }
  }

  // Group stories by creator and check if all stories are viewed
  const groupedStories = useMemo(() => {
    if (!stories?.length) return [];

    const grouped = stories.reduce((acc, story) => {
      const creatorId = story.creator._id;
      if (!acc[creatorId]) {
        acc[creatorId] = {
          creator: story.creator,
          stories: [],
          allViewed: true,
          firstUnviewedIndex: 0,
          latestStory: null,
        };
      }

      // Check if this story is not viewed before adding it
      const isViewed = story.viewers?.some((view) => view.user === user._id);

      // If this story is not viewed and we haven't found an unviewed story yet,
      // set this as the first unviewed story index
      if (!isViewed && acc[creatorId].allViewed) {
        acc[creatorId].firstUnviewedIndex = acc[creatorId].stories.length;
        acc[creatorId].allViewed = false;
      }

      acc[creatorId].stories.push(story);
      // Update latest story
      acc[creatorId].latestStory = story;

      return acc;
    }, {});

    // Sort stories within each group by creation date (oldest first)
    Object.values(grouped).forEach((group) => {
      group.stories.sort(
        (a, b) => new Date(a.createdAt) - new Date(b.createdAt)
      );
    });

    return Object.values(grouped);
  }, [stories, user._id]);

  const handleStoryClick = async (creatorIndex) => {
    setSelectedCreatorIndex(creatorIndex);
    const group = groupedStories[creatorIndex];

    // Start from the first unviewed story if available, otherwise start from beginning
    const startIndex = group.allViewed ? 0 : group.firstUnviewedIndex;
    setSelectedStoryIndex(startIndex);

    // Track view for the starting story
    try {
      const startingStory = group.stories[startIndex];
      await onViewStory(startingStory);
    } catch (error) {
      console.error("Failed to track story view:", error);
    }
  };

  const handleNext = async () => {
    const currentCreator = groupedStories[selectedCreatorIndex];

    // If there are more stories in the current group
    if (selectedStoryIndex < currentCreator.stories.length - 1) {
      const nextStoryIndex = selectedStoryIndex + 1;
      setSelectedStoryIndex(nextStoryIndex);
      try {
        const story = currentCreator.stories[nextStoryIndex];
        await onViewStory(story);
      } catch (error) {
        console.error("Failed to track story view:", error);
      }
    }
    // If we're at the last story of the current group, move to next creator
    else if (selectedCreatorIndex < groupedStories.length - 1) {
      const nextCreatorIndex = selectedCreatorIndex + 1;
      const nextCreator = groupedStories[nextCreatorIndex];
      setSelectedCreatorIndex(nextCreatorIndex);

      // Start from the first unviewed story in the next group if available
      const startIndex = nextCreator.allViewed
        ? 0
        : nextCreator.firstUnviewedIndex;
      setSelectedStoryIndex(startIndex);

      try {
        const story = nextCreator.stories[startIndex];
        await onViewStory(story);
      } catch (error) {
        console.error("Failed to track story view:", error);
      }
    } else {
      // No more stories, close viewer
      handleClose();
    }
  };

  const handlePrevious = async () => {
    const currentCreator = groupedStories[selectedCreatorIndex];

    // If there are previous stories in the current group
    if (selectedStoryIndex > 0) {
      const prevStoryIndex = selectedStoryIndex - 1;
      setSelectedStoryIndex(prevStoryIndex);
      try {
        const story = currentCreator.stories[prevStoryIndex];
        await onViewStory(story);
      } catch (error) {
        console.error("Failed to track story view:", error);
      }
    }
    // If we're at the first story of the current group, move to previous creator's last story
    else if (selectedCreatorIndex > 0) {
      const prevCreatorIndex = selectedCreatorIndex - 1;
      const prevCreator = groupedStories[prevCreatorIndex];
      const lastStoryIndex = prevCreator.stories.length - 1;
      setSelectedCreatorIndex(prevCreatorIndex);
      setSelectedStoryIndex(lastStoryIndex);
      try {
        const story = prevCreator.stories[lastStoryIndex];
        await onViewStory(story);
      } catch (error) {
        console.error("Failed to track story view:", error);
      }
    }
  };

  const handleClose = () => {
    setSelectedCreatorIndex(null);
    setSelectedStoryIndex(0);
    // Reload stories to update view status
    loadStories();
  };

  return (
    <div className="relative">
      <div className="flex gap-2 max-h-[220px] overflow-x-auto py-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 scroll-smooth">
        {/* Story previews */}
        {groupedStories.map((group, index) => (
          <StoryCard
            key={group.creator._id}
            story={group.stories[group.stories.length - 1]} // Use the latest story for preview
            index={index}
            handleStoryClick={handleStoryClick}
            storyCount={group.stories.length}
            allStoriesViewed={group.allViewed}
          />
        ))}

        {selectedCreatorIndex !== null && (
          <StoryViewer
            stories={groupedStories[selectedCreatorIndex].stories}
            currentIndex={selectedStoryIndex}
            onClose={handleClose}
            onNext={handleNext}
            onPrevious={handlePrevious}
            totalStories={groupedStories[selectedCreatorIndex].stories.length}
          />
        )}
      </div>
    </div>
  );
}
