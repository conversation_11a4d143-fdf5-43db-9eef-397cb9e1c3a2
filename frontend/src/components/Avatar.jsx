import { Icon } from "@iconify/react/dist/iconify.js";

export default function Avatar({ avatar, className, size }) {
  const sizeClass =
    size === "xl"
      ? "w-20 h-20"
      : size === "lg"
      ? "w-12 h-12"
      : size === "md"
      ? "w-8 h-8"
      : size === "sm"
      ? "w-6 h-6"
      : "w-10 h-10";
  return (
    <div>
      {avatar ? (
        <img
          src={avatar}
          alt="Avatar"
          className={`${className} ${sizeClass} rounded-full object-cover `}
        />
      ) : (
        <div
          className={`${className} ${sizeClass} rounded-full bg-gray-200 flex items-center justify-center`}
        >
          <Icon fontSize={18} icon="ph:user" />
        </div>
      )}
    </div>
  );
}
