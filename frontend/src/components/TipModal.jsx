import { useState } from "react";
import { IoClose } from "react-icons/io5";
import { fCurrency } from "../utils/formatNumber";
import { usePaystackPayment } from "react-paystack";
import { useAuth } from "../hooks/useAuth";
import { transactionService } from "../services/api";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
/**
 * @param {Object} props
 * @param {boolean} props.isOpen
 * @param {function} props.onClose
 * @param {Object} props.recipient
 * @param {string} props.tipType
 */
export default function TipModal({ isOpen, onClose, recipient, tipType }) {
  const [selectedAmount, setSelectedAmount] = useState(null);
  const [customAmount, setCustomAmount] = useState("");
  const [message, setMessage] = useState("");
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const config = {
    reference: new Date().getTime().toString(),
    email: user?.email,
    amount: selectedAmount * 100,
    publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY,
  };

  const presetAmounts = [5000, 10000, 15000, 25000, 50000];

  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount);
    setCustomAmount(amount.toString());
  };

  const handleCustomAmountChange = (e) => {
    const value = e.target.value.replace(/[^0-9]/g, "");
    setCustomAmount(value);
    setSelectedAmount(Number(value));
  };

  const onSuccess = async (payment) => {
    console.log(payment);
    if (payment.status === "success") {
      await transactionService.tipCreator({
        recipientId: recipient._id,
        amount: selectedAmount,
        paymentMethod: "paystack",
        tipType,
        transactionReference: payment.reference,
      });
      onClose();
      toast.success("Tip sent successfully");
    }
  };

  const paymentClose = () => {
    console.log("closed");
    onClose();
  };
  const initializePayment = usePaystackPayment(config);
  const handleSendTip = () => {
    if (!isAuthenticated) {
      toast.error("Please login to send a tip");
      navigate(`/login?redirect=${window.location.pathname}`);
      return;
    }
    initializePayment({ onSuccess, onClose: paymentClose, config });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 !mt-0 top-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-6 w-full max-w-lg mx-4">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-2">
            {/* <FaDollarSign className="text-2xl text-gray-600" /> */}
            <h2 className="text-lg font-semibold">
              Tip {recipient?.displayName || recipient?.username}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <IoClose className="text-2xl" />
          </button>
        </div>

        <div className="flex justify-between gap-1 flex-wrap mb-6">
          {presetAmounts.map((amount) => (
            <button
              key={amount}
              onClick={() => handleAmountSelect(amount)}
              className={`flex-1 text-sm py-2 rounded-full px-2 font-medium transition-colors
                ${
                  selectedAmount === amount
                    ? "bg-secondary text-white"
                    : "text-gray-500 hover:bg-gray-100"
                }`}
            >
              {fCurrency(amount)}
            </button>
          ))}
        </div>

        <div className="mb-4">
          <div className="relative">
            <span className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-500">
              ₦
            </span>
            <input
              type="text"
              value={customAmount}
              onChange={handleCustomAmountChange}
              className="w-full text-sm  px-8 py-3 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-gray-300"
              placeholder="Enter amount"
            />
            {customAmount && (
              <button
                onClick={() => {
                  setCustomAmount("");
                  setSelectedAmount(null);
                }}
                className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                <IoClose className="text-xl" />
              </button>
            )}
          </div>
        </div>

        <div className="mb-6">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full text-sm px-4 py-3 bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-gray-300"
            placeholder="Your message"
          />
        </div>

        <button
          onClick={handleSendTip}
          disabled={!selectedAmount}
          className={`w-full text-sm py-3 rounded-full font-medium transition-colors
            ${
              selectedAmount
                ? "bg-primary text-white hover:bg-primary/80"
                : "bg-gray-200 text-gray-500 cursor-not-allowed"
            }`}
        >
          Send tip
        </button>

        <p className="text-center text-gray-500 text-sm mt-4">
          VAT (if you are in the EU) will be applied on top of all charges.
        </p>
      </div>
    </div>
  );
}
