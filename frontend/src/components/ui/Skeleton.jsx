import PropTypes from "prop-types";
import { twMerge } from "tailwind-merge";

const Skeleton = ({
  variant = "text",
  width,
  height,
  className,
  rounded = "md",
  animation = "pulse",
  count = 1,
}) => {
  const baseClasses = "bg-gray-200 animate-pulse";

  const getVariantClasses = () => {
    switch (variant) {
      case "text":
        return "h-4 w-full";
      case "title":
        return "h-6 w-3/4";
      case "avatar":
        return "h-12 w-12 rounded-full";
      case "button":
        return "h-10 w-24";
      case "image":
        return "h-48 w-full";
      case "box":
        return "h-20 w-full";
      case "card":
        return "h-[300px] w-full";
      default:
        return "";
    }
  };

  const skeletonClasses = twMerge(
    baseClasses,
    getVariantClasses(),
    `rounded-${rounded}`,
    animation === "pulse" ? "animate-pulse" : "animate-shimmer",
    className
  );

  const style = {
    ...(width && { width }),
    ...(height && { height }),
  };

  const renderSkeleton = () => (
    <div className={skeletonClasses} style={style} />
  );

  if (count === 1) return renderSkeleton();

  return (
    <div className="flex flex-col gap-2">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={skeletonClasses} style={style} />
      ))}
    </div>
  );
};

Skeleton.propTypes = {
  variant: PropTypes.oneOf([
    "text",
    "title",
    "avatar",
    "button",
    "image",
    "box",
    "card",
  ]),
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  className: PropTypes.string,
  rounded: PropTypes.string,
  animation: PropTypes.oneOf(["pulse", "shimmer"]),
  count: PropTypes.number,
};

export default Skeleton;
