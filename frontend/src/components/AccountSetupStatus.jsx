import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaUserFriends,
  FaDollarSign,
  FaPen,
  FaUsers,
  FaEnvelope,
  FaIdCard,
  FaImage,
  FaInfoCircle,
  FaTimes,
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";

export default function AccountSetupStatus({ user }) {
  const navigate = useNavigate();

  // Calculate setup completion status
  const setupSteps = [
    {
      id: "email_verification",
      title: "Verify Email",
      description: "Confirm your email address to secure your account",
      icon: FaEnvelope,
      completed: user?.isEmailVerified || user?.isVerified,
      required: true,
      action: () => navigate("/verify-email"),
      actionText: "Verify Email",
    },
    {
      id: "profile_basic",
      title: "Complete Profile",
      description: "Add your avatar, display name, and bio",
      icon: FaUserFriends,
      completed: user?.avatar && user?.displayName && user?.about,
      required: true,
      action: () => navigate("/creator/account"),
      actionText:
        user?.avatar && user?.displayName && user?.about
          ? "Edit Profile"
          : "Complete Profile",
    },
    {
      id: "creator_category",
      title: "Set Creator Category",
      description: "Choose your content category to help fans find you",
      icon: FaIdCard,
      completed: user?.creatorCategory,
      required: true,
      action: () => navigate("/creator/account"),
      actionText: user?.creatorCategory ? "Change Category" : "Set Category",
    },
    {
      id: "cover_image",
      title: "Add Cover Image",
      description: "Upload a cover image to make your profile stand out",
      icon: FaImage,
      completed: user?.coverImage,
      required: false,
      action: () => navigate("/creator/account"),
      actionText: user?.coverImage ? "Change Cover" : "Add Cover",
    },
    {
      id: "payment_setup",
      title: "Payment Details",
      description: "Set up payment method to receive earnings",
      icon: FaDollarSign,
      completed: user?.paymentInfo?.verified,
      required: true,
      action: () => navigate("/creator/payment-details"),
      actionText: user?.paymentInfo?.verified ? "Edit Payment" : "Add Payment",
    },
    {
      id: "pricing_plans",
      title: "Subscription Plans",
      description: "Create subscription plans for your content",
      icon: FaDollarSign,
      completed: user?.pricingPlans?.length > 0,
      required: true,
      action: () => navigate("/creator/subscription-plans"),
      actionText:
        user?.pricingPlans?.length > 0 ? "Edit Plans" : "Create Plans",
    },
    {
      id: "first_post",
      title: "Create First Post",
      description: "Share your first piece of content",
      icon: FaPen,
      completed: user?.totalPosts > 0,
      required: false,
      action: () => navigate("/creator/posts/new"),
      actionText: user?.totalPosts > 0 ? "Create Another" : "Create Post",
    },
    {
      id: "profile_verification",
      title: "Profile Verification",
      description: "Submit your profile for verification to build trust",
      icon: FaUsers,
      completed: user?.verified,
      required: false,
      action: () => navigate("/creator/profile/review"),
      actionText: user?.verified ? "Verified" : "Submit for Review",
    },
  ];

  // Calculate progress
  const requiredSteps = setupSteps.filter((step) => step.required);
  const completedRequiredSteps = requiredSteps.filter((step) => step.completed);
  const totalSteps = setupSteps.length;
  const completedSteps = setupSteps.filter((step) => step.completed);

  const requiredProgress =
    (completedRequiredSteps.length / requiredSteps.length) * 100;
  const overallProgress = (completedSteps.length / totalSteps) * 100;

  // Check if all required steps are completed
  const allRequiredCompleted =
    completedRequiredSteps.length === requiredSteps.length;

  // Don't show if all required steps are completed and user is verified
  if (allRequiredCompleted && user?.verified) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-100">
      {/* Header with Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-secondary">
            Complete Your Creator Setup
          </h2>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              {completedSteps.length}/{totalSteps} completed
            </span>
            <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-primary to-purple-600 transition-all duration-500"
                style={{ width: `${overallProgress}%` }}
              />
            </div>
          </div>
        </div>

        {/* Progress Summary */}
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary rounded-full" />
            <span className="text-gray-600">
              Required: {completedRequiredSteps.length}/{requiredSteps.length}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-purple-500 rounded-full" />
            <span className="text-gray-600">
              Optional: {completedSteps.length - completedRequiredSteps.length}/
              {totalSteps - requiredSteps.length}
            </span>
          </div>
          {allRequiredCompleted && (
            <div className="flex items-center gap-2 text-green-600">
              <FaCheck className="w-3 h-3" />
              <span className="font-medium">Ready to earn!</span>
            </div>
          )}
        </div>
      </div>

      {/* Setup Steps */}
      <div className="space-y-3">
        {setupSteps.map((step, index) => {
          const IconComponent = step.icon;
          const isCompleted = step.completed;
          const isRequired = step.required;

          return (
            <div
              key={step.id}
              className={`flex flex-col sm:flex-row justify-between items-start p-4 rounded-lg border transition-all duration-200 ${
                isCompleted
                  ? "bg-green-50 border-green-200"
                  : isRequired
                  ? "bg-blue-50 border-blue-200 hover:bg-blue-100"
                  : "bg-gray-50 border-gray-200 hover:bg-gray-100"
              }`}
            >
              <div className="flex items-start gap-3 mb-3 sm:mb-0 flex-1">
                <div
                  className={`p-2 rounded-full ${
                    isCompleted
                      ? "bg-green-100"
                      : isRequired
                      ? "bg-blue-100"
                      : "bg-gray-100"
                  }`}
                >
                  {isCompleted ? (
                    <FaCheck
                      className={`w-4 h-4 ${
                        isCompleted ? "text-green-600" : "text-gray-500"
                      }`}
                    />
                  ) : (
                    <IconComponent
                      className={`w-4 h-4 ${
                        isRequired ? "text-blue-600" : "text-gray-500"
                      }`}
                    />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-sm font-semibold text-secondary">
                      {step.title}
                    </h3>
                    {isRequired && !isCompleted && (
                      <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
                        Required
                      </span>
                    )}
                    {isCompleted && (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">
                        Completed
                      </span>
                    )}
                    {!isRequired && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                        Optional
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-600">{step.description}</p>
                </div>
              </div>

              {(!isCompleted || step.id !== "profile_verification") && (
                <button
                  onClick={step.action}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    isCompleted
                      ? "bg-green-100 text-green-700 hover:bg-green-200"
                      : isRequired
                      ? "bg-primary text-white hover:bg-primary-dark"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  {step.actionText}
                </button>
              )}
            </div>
          );
        })}
      </div>

      {/* Call to Action */}
      {!allRequiredCompleted && (
        <div className="mt-6 p-4 bg-gradient-to-r from-primary to-purple-600 rounded-lg text-white">
          <div className="flex items-center gap-3">
            <FaInfoCircle className="w-5 h-5" />
            <div>
              <h4 className="font-semibold">
                Complete required steps to start earning
              </h4>
              <p className="text-sm opacity-90">
                Finish {requiredSteps.length - completedRequiredSteps.length}{" "}
                more required steps to activate your creator account
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {allRequiredCompleted && !user?.verified && (
        <div className="mt-6 p-4 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg text-white">
          <div className="flex items-center gap-3">
            <FaCheck className="w-5 h-5" />
            <div>
              <h4 className="font-semibold">
                Great! You're ready to start earning
              </h4>
              <p className="text-sm opacity-90">
                Complete the optional steps to maximize your earning potential
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
