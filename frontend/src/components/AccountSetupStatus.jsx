import React from "react";
import {
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaUser<PERSON><PERSON>ds,
  FaDollarSign,
  FaPen,
  FaUsers,
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";

export default function AccountSetupStatus({ user }) {
  const navigate = useNavigate();

  // Only show if KYC status is pending
  if (user?.kycStatus !== "pending") {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-4 border border-gray-100">
      <h2 className="text-lg font-bold text-secondary mb-4 flex items-center justify-between">
        <span>Complete your account setup</span>
        <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">
          {user?.kycStatus}
        </span>
      </h2>
      <div className="space-y-3">
        {/* Update Profile Section */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start p-3 border-b border-gray-100 ${
            user?.avatar ? "bg-green-50" : ""
          }`}
        >
          <div className="flex items-start gap-3 mb-3 sm:mb-0">
            <div
              className={`p-2 rounded-full ${
                user?.avatar ? "bg-green-100" : "bg-gray-100"
              }`}
            >
              {user?.avatar ? (
                <FaCheck className="w-4 h-4 text-green-600" />
              ) : (
                <FaUserFriends className="w-4 h-4 text-secondary" />
              )}
            </div>
            <div>
              <h3 className="text-sm font-semibold text-secondary flex items-center gap-2">
                Update profile
                {user?.avatar && (
                  <span className="text-xs text-green-600 bg-green-100 px-2 py-0.5 rounded-full">
                    Completed
                  </span>
                )}
              </h3>
            </div>
          </div>
          <button
            onClick={() => navigate("/creator/account")}
            className={`px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
              user?.avatar
                ? "bg-green-100 text-green-600 hover:bg-green-200"
                : "bg-primary text-white hover:bg-primary-dark"
            }`}
          >
            {user?.avatar ? "Edit profile" : "Update profile"}
          </button>
        </div>

        {/* Add Withdrawal Account Section */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start p-3 border-b border-gray-100 ${
            user?.paymentInfo?.verified ? "bg-green-50" : ""
          }`}
        >
          <div className="flex items-start gap-3 mb-3 sm:mb-0">
            <div
              className={`p-2 rounded-full ${
                user?.paymentInfo?.verified ? "bg-green-100" : "bg-gray-100"
              }`}
            >
              {user?.paymentInfo?.verified ? (
                <FaCheck className="w-4 h-4 text-green-600" />
              ) : (
                <FaDollarSign className="w-4 h-4 text-secondary" />
              )}
            </div>
            <div>
              <h3 className="text-sm font-semibold text-secondary flex items-center gap-2">
                Add payment details
                {user?.paymentInfo?.verified && (
                  <span className="text-xs text-green-600 bg-green-100 px-2 py-0.5 rounded-full">
                    Completed
                  </span>
                )}
              </h3>
            </div>
          </div>
          <button
            onClick={() => navigate("/creator/payment-details")}
            className={`px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
              user?.paymentInfo?.verified
                ? "bg-green-100 text-green-600 hover:bg-green-200"
                : "bg-primary text-white hover:bg-primary-dark"
            }`}
          >
            {user?.paymentInfo?.verified ? "Edit details" : "Add details"}
          </button>
        </div>

        {/* Add Subscription Plans Section */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start p-3 border-b border-gray-100 ${
            user?.pricingPlans?.length > 0 ? "bg-green-50" : ""
          }`}
        >
          <div className="flex items-start gap-3 mb-3 sm:mb-0">
            <div
              className={`p-2 rounded-full ${
                user?.pricingPlans?.length > 0 ? "bg-green-100" : "bg-gray-100"
              }`}
            >
              {user?.pricingPlans?.length > 0 ? (
                <FaCheck className="w-4 h-4 text-green-600" />
              ) : (
                <FaPen className="w-4 h-4 text-secondary" />
              )}
            </div>
            <div>
              <h3 className="text-sm font-semibold text-secondary flex items-center gap-2">
                Add subscription plans
                {user?.pricingPlans?.length > 0 && (
                  <span className="text-xs text-green-600 bg-green-100 px-2 py-0.5 rounded-full">
                    Completed
                  </span>
                )}
              </h3>
            </div>
          </div>
          <button
            onClick={() => navigate("/creator/subscription-plans")}
            className={`px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
              user?.pricingPlans?.length > 0
                ? "bg-green-100 text-green-600 hover:bg-green-200"
                : "bg-primary text-white hover:bg-primary-dark"
            }`}
          >
            {user?.pricingPlans?.length > 0 ? "Edit plans" : "Add plans"}
          </button>
        </div>

        {/* Create Post Section */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start p-3 border-b border-gray-100 ${
            user?.totalPosts > 0 ? "bg-green-50" : ""
          }`}
        >
          <div className="flex items-start gap-3 mb-3 sm:mb-0">
            <div
              className={`p-2 rounded-full ${
                user?.totalPosts > 0 ? "bg-green-100" : "bg-gray-100"
              }`}
            >
              {user?.totalPosts > 0 ? (
                <FaCheck className="w-4 h-4 text-green-600" />
              ) : (
                <FaPen className="w-4 h-4 text-secondary" />
              )}
            </div>
            <div>
              <h3 className="text-sm font-semibold text-secondary flex items-center gap-2">
                Create a post
                {user?.totalPosts > 0 && (
                  <span className="text-xs text-green-600 bg-green-100 px-2 py-0.5 rounded-full">
                    Completed
                  </span>
                )}
              </h3>
            </div>
          </div>
          <button
            onClick={() => navigate("/creator/posts/new")}
            className={`px-4 py-1.5 rounded-full text-sm transition-colors duration-200 ${
              user?.totalPosts > 0
                ? "bg-green-100 text-green-600 hover:bg-green-200"
                : "bg-primary text-white hover:bg-primary-dark"
            }`}
          >
            {user?.totalPosts > 0 ? "Create another" : "Create post"}
          </button>
        </div>

        {/* Review Profile Section */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start p-3 ${
            user?.verified ? "bg-green-50" : ""
          }`}
        >
          <div className="flex items-start gap-3 mb-3 sm:mb-0">
            <div
              className={`p-2 rounded-full ${
                user?.verified ? "bg-green-100" : "bg-gray-100"
              }`}
            >
              {user?.verified ? (
                <FaCheck className="w-4 h-4 text-green-600" />
              ) : (
                <FaUsers className="w-4 h-4 text-secondary" />
              )}
            </div>
            <div>
              <h3 className="text-sm font-semibold text-secondary flex items-center gap-2">
                Verify profile
                {user?.verified && (
                  <span className="text-xs text-green-600 bg-green-100 px-2 py-0.5 rounded-full">
                    Verified
                  </span>
                )}
              </h3>
            </div>
          </div>
          {!user?.verified && (
            <button
              onClick={() => navigate("/creator/profile/review")}
              className="px-4 py-1.5 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors duration-200 text-sm"
            >
              Submit for review
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
