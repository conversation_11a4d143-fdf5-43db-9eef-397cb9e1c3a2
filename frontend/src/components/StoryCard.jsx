import { BsImages } from "react-icons/bs";
import Avatar from "./Avatar";

export default function StoryCard({
  story,
  index,
  handleStoryClick,
  storyCount,
  allStoriesViewed,
}) {
  return (
    <div
      onClick={() => handleStoryClick(index)}
      className="relative aspect-[9/16] w-[120px] rounded-lg overflow-hidden cursor-pointer group flex-shrink-0"
    >
      {story.mediaType === "image" ? (
        <img
          src={story.mediaUrl}
          alt={`Story by ${story.creator.username}`}
          className="w-full h-full object-cover"
        />
      ) : (
        <video
          src={story.mediaUrl}
          className="w-full h-full object-cover"
          muted
        />
      )}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/30">
        <div className="p-2 flex flex-col h-full">
          <div className="flex items-center space-x-2">
            {story.creator.avatar ? (
              <img
                src={story.creator.avatar}
                alt={story.creator.username}
                className={`w-8 h-8 rounded-full border-2 ${
                  allStoriesViewed ? "border-gray-600/80" : "border-primary"
                }`}
              />
            ) : (
              <Avatar className={"h-8 w-8 border-2 border-primary"} />
            )}
            {/* {storyCount > 1 && (
              <div
                className={`${
                  allStoriesViewed ? "bg-gray-600/80" : "bg-primary/80"
                } rounded-full px-2 py-0.5 flex items-center`}
              >
                <BsImages className="w-3 h-3 text-white mr-1" />
                <span className="text-white text-xs">{storyCount}</span>
              </div>
            )} */}
          </div>
          <p className="text-white absolute bottom-2 left-2 text-xs font-medium mt-2 truncate z-[9999]">
            {story.creator.displayName || story.creator.username}
          </p>
        </div>
      </div>
    </div>
  );
}
