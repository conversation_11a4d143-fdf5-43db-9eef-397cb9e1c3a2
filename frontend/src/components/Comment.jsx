import { useState, useEffect } from "react";
import { formatDistanceToNow } from "date-fns";
import usePostStore from "../store/postStore";
import { Icon } from "@iconify/react";
import InteractiveButton from "./InteractiveButton";
import { commentService } from "../services/api";
import PropTypes from "prop-types";
import { toast } from "react-hot-toast";
import { useAuth } from "../hooks/useAuth";
import Avatar from "./Avatar";

const CommentContainer = ({ comment }) => {
  return (
    <div className="flex space-x-3">
      <Avatar className="!h-10 !w-10" avatar={comment.user?.avatar} />
      <div className="flex-1">
        <div className="bg-gray-100 rounded-2xl px-4 py-2">
          <div className="font-medium">{comment.user?.username}</div>
          <p className="text-gray-600">{comment?.content}</p>
        </div>
        <div className="flex items-center justify-between space-x-4 mt-1 text-sm text-gray-500">
          <span>{formatDistanceToNow(new Date(comment?.createdAt))} ago</span>
          <div className="flex items-center gap-2">
            <Icon icon="mdi:heart" fontSize={16} />
            <button className="hover:text-pink-500 text-sm">
              {comment?.likes?.length || 0} Likes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
const Comment = ({ comment, comments }) => {
  const { addReply, likeComment, comments: postComments } = usePostStore();
  const [showReplies, setShowReplies] = useState(false);
  const [reply, setReply] = useState("");
  const [replyLoading, setReplyLoading] = useState(false);
  const [showReplyInput, setShowReplyInput] = useState(false);
  const { user } = useAuth();

  const isReplyComment = comment.isReply;

  const handleAddReply = async () => {
    if (!reply.length) return;
    try {
      setReplyLoading(true);
      const response = await commentService.createReply(comment._id, reply);
      console.log("response", response);
      addReply(comment._id, response.reply);
      setReplyLoading(false);
      setReply("");
      setShowReplyInput(false);
    } catch (error) {
      setReplyLoading(false);
      if (error?.response?.data?.message) {
        toast.error(error?.response?.data?.message);
      } else {
        toast.error("Something went wrong");
      }
    }
  };
  const handleLikeComment = async () => {
    try {
      likeComment(comment._id, user._id);
      await commentService.likeComment(comment._id);
    } catch (error) {
      console.log("error", error);
    }
  };

  return (
    <div className="flex space-x-3">
      <Avatar className="!h-10 !w-10" avatar={comment.user?.avatar} />
      <div className="flex-1">
        <div className="bg-gray-100 rounded-2xl px-4 py-2">
          <div className="font-medium">{comment.user?.username}</div>
          <p className="text-gray-600">{comment?.content}</p>
        </div>
        <div className="flex items-center justify-between space-x-4 mt-1 text-sm text-gray-500">
          <span>{formatDistanceToNow(new Date(comment?.createdAt))} ago</span>
          <div className="flex items-center gap-2">
            <button onClick={handleLikeComment}>
              {comment?.likes.includes(user?._id) ? (
                <Icon icon="icon-park-solid:like" color="red" fontSize={20} />
              ) : (
                <Icon icon="icon-park-outline:like" fontSize={20} />
              )}
            </button>
            <button className="hover:text-pink-500 text-sm">
              {comment?.likesCount || 0} Likes
            </button>

            {!isReplyComment && (
              <button
                className="hover:text-pink-500 text-sm"
                onClick={() => setShowReplyInput(!showReplyInput)}
              >
                Reply
              </button>
            )}
          </div>
        </div>

        {!isReplyComment && (
          <>
            {showReplyInput && (
              <div className="flex-1 flex items-center mt-2 ml-10">
                <input
                  type="text"
                  onChange={(e) => setReply(e.target.value)}
                  value={reply}
                  placeholder="Share something"
                  className="w-full bg-gray-100 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500"
                />
                <div className="flex space-x-2 ml-2">
                  <InteractiveButton
                    className="!py-2"
                    onClick={handleAddReply}
                    disabled={!reply.length}
                    loading={replyLoading}
                  >
                    Reply
                  </InteractiveButton>
                </div>
              </div>
            )}

            {comments.filter((item) => item.isReply)?.length > 0 && (
              <button
                className="text-sm text-gray-500 hover:text-pink-500 mt-1 ml-2"
                onClick={() => setShowReplies(!showReplies)}
              >
                {showReplies
                  ? "Hide Replies"
                  : `Show Replies (${
                      comments.filter(
                        (item) =>
                          item.isReply && item.parentComment === comment._id
                      )?.length || 0
                    })`}
              </button>
            )}

            {showReplies && (
              <div className="ml-8 mt-2 space-y-3">
                {comments
                  .filter(
                    (item) => item.isReply && item.parentComment === comment._id
                  )
                  .map((reply) => (
                    <CommentContainer
                      key={reply._id}
                      comment={reply}
                      isReplyComment={true}
                      showReplyInput={showReplyInput}
                      setShowReplyInput={setShowReplyInput}
                    />
                  ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

Comment.propTypes = {
  comment: PropTypes.object.isRequired,
  comments: PropTypes.array.isRequired,
};

export default Comment;
