import { useState } from "react";
import { Icon } from "@iconify/react";
import InteractiveButton from "../InteractiveButton";

const UserTypeSelection = ({ onSelect, selectedType, className = "" }) => {
  const [hoveredType, setHoveredType] = useState(null);

  const userTypes = [
    {
      id: "fan",
      title: "Join as a Fan",
      subtitle: "Discover and support amazing creators",
      icon: "solar:heart-line-duotone",
      features: [
        "Subscribe to your favorite creators",
        "Access exclusive content",
        "Direct messaging with creators",
        "Personalized content feed",
        "Support creators with tips"
      ],
      color: "blue",
      gradient: "from-blue-500 to-purple-600"
    },
    {
      id: "creator",
      title: "Become a Creator",
      subtitle: "Monetize your content and build your community",
      icon: "solar:star-line-duotone",
      features: [
        "Earn 80% on all subscriptions",
        "Create premium content",
        "Build your subscriber base",
        "Direct fan engagement",
        "Weekly automated payouts"
      ],
      color: "purple",
      gradient: "from-purple-500 to-pink-600"
    }
  ];

  return (
    <div className={`w-full ${className}`}>
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Choose Your Experience
        </h2>
        <p className="text-gray-600">
          Select how you'd like to use Erossphere
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {userTypes.map((type) => (
          <div
            key={type.id}
            className={`relative p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
              selectedType === type.id
                ? `border-${type.color}-500 bg-${type.color}-50`
                : hoveredType === type.id
                ? `border-${type.color}-300 bg-${type.color}-25`
                : "border-gray-200 bg-white hover:border-gray-300"
            }`}
            onClick={() => onSelect(type.id)}
            onMouseEnter={() => setHoveredType(type.id)}
            onMouseLeave={() => setHoveredType(null)}
          >
            {/* Selection Indicator */}
            <div className={`absolute top-4 right-4 w-6 h-6 rounded-full border-2 transition-all duration-200 ${
              selectedType === type.id
                ? `bg-${type.color}-500 border-${type.color}-500`
                : `border-gray-300`
            }`}>
              {selectedType === type.id && (
                <Icon 
                  icon="solar:check-bold" 
                  className="w-4 h-4 text-white absolute top-0.5 left-0.5" 
                />
              )}
            </div>

            {/* Icon */}
            <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${type.gradient} flex items-center justify-center mb-4`}>
              <Icon 
                icon={type.icon} 
                className="w-8 h-8 text-white" 
              />
            </div>

            {/* Content */}
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {type.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {type.subtitle}
                </p>
              </div>

              {/* Features */}
              <ul className="space-y-2">
                {type.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-sm text-gray-700">
                    <Icon 
                      icon="solar:check-circle-bold" 
                      className={`w-4 h-4 mt-0.5 text-${type.color}-500 flex-shrink-0`} 
                    />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Badge */}
              <div className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium ${
                type.id === 'creator' 
                  ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white'
                  : 'bg-blue-100 text-blue-700'
              }`}>
                {type.id === 'creator' ? (
                  <>
                    <Icon icon="solar:fire-bold" className="w-3 h-3" />
                    <span>Start Earning Today</span>
                  </>
                ) : (
                  <>
                    <Icon icon="solar:heart-bold" className="w-3 h-3" />
                    <span>Free to Join</span>
                  </>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Continue Button */}
      {selectedType && (
        <div className="mt-8 text-center">
          <InteractiveButton
            onClick={() => onSelect(selectedType)}
            className="px-8 py-3 text-lg font-semibold"
            variant="primary"
          >
            Continue as {selectedType === 'fan' ? 'Fan' : 'Creator'}
            <Icon icon="solar:arrow-right-linear" className="w-5 h-5 ml-2" />
          </InteractiveButton>
        </div>
      )}
    </div>
  );
};

export default UserTypeSelection;
