import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react";
import { useAuth } from "../../hooks/useAuth";
import UserTypeSelection from "./UserTypeSelection";
import InteractiveButton from "../InteractiveButton";
import toast from "react-hot-toast";

const OnboardingFlow = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [userType, setUserType] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user, updateProfile } = useAuth();
  const navigate = useNavigate();

  const steps = [
    {
      id: 1,
      title: "Welcome to Erossphere!",
      subtitle: "Let's get you set up in just a few steps",
    },
    {
      id: 2,
      title: "Choose Your Path",
      subtitle: "How would you like to experience Erossphere?",
    },
    {
      id: 3,
      title: "Complete Your Profile",
      subtitle: "Add some details to personalize your experience",
    },
  ];

  const handleUserTypeSelect = (type) => {
    setUserType(type);
  };

  const handleContinue = async () => {
    if (currentStep === 2 && userType) {
      setIsLoading(true);
      try {
        // Update user profile with selected user type
        await updateProfile({
          userType: userType,
          isCreator: userType === "creator",
        });

        // Mark onboarding as completed
        localStorage.setItem(`onboarding_completed_${user._id}`, "true");

        // Navigate to appropriate dashboard
        if (userType === "creator") {
          navigate("/creator");
        } else {
          navigate("/");
        }
      } catch (error) {
        toast.error("Something went wrong. Please try again.");
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    } else if (currentStep === 1) {
      setCurrentStep(2);
    } else if (currentStep === 3) {
      // Complete onboarding
      navigate("/");
    }
  };

  const handleSkip = () => {
    // Mark onboarding as skipped
    localStorage.setItem(`onboarding_skipped_${user._id}`, "true");
    navigate("/");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300 ${
                    currentStep >= step.id
                      ? "bg-primary text-white"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {currentStep > step.id ? (
                    <Icon icon="solar:check-bold" className="w-5 h-5" />
                  ) : (
                    step.id
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`w-20 h-1 mx-4 rounded-full transition-all duration-300 ${
                      currentStep > step.id ? "bg-primary" : "bg-gray-200"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-500">
              Step {currentStep} of {steps.length}
            </p>
          </div>
        </div>

        {/* Content Card */}
        <div className="bg-white rounded-3xl shadow-xl p-8 md:p-12">
          {/* Step 1: Welcome */}
          {currentStep === 1 && (
            <div className="text-center space-y-6">
              <div className="w-24 h-24 bg-gradient-to-br from-primary to-purple-600 rounded-3xl flex items-center justify-center mx-auto">
                <Icon icon="solar:star-bold" className="w-12 h-12 text-white" />
              </div>

              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  {steps[0].title}
                </h1>
                <p className="text-lg text-gray-600 mb-8">
                  {steps[0].subtitle}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-12">
                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Icon
                      icon="solar:users-group-two-rounded-bold"
                      className="w-8 h-8 text-blue-600"
                    />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Connect</h3>
                  <p className="text-sm text-gray-600">
                    Build meaningful relationships with creators and fans
                  </p>
                </div>

                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Icon
                      icon="solar:gallery-bold"
                      className="w-8 h-8 text-purple-600"
                    />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Create</h3>
                  <p className="text-sm text-gray-600">
                    Share your content and express your creativity
                  </p>
                </div>

                <div className="text-center p-6">
                  <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Icon
                      icon="solar:dollar-minimalistic-bold"
                      className="w-8 h-8 text-green-600"
                    />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Earn</h3>
                  <p className="text-sm text-gray-600">
                    Monetize your passion and support your favorites
                  </p>
                </div>
              </div>

              <InteractiveButton
                onClick={handleContinue}
                className="px-8 py-3 text-lg font-semibold"
                variant="primary"
              >
                Get Started
                <Icon
                  icon="solar:arrow-right-linear"
                  className="w-5 h-5 ml-2"
                />
              </InteractiveButton>
            </div>
          )}

          {/* Step 2: User Type Selection */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  {steps[1].title}
                </h1>
                <p className="text-lg text-gray-600">{steps[1].subtitle}</p>
              </div>

              <UserTypeSelection
                onSelect={handleUserTypeSelect}
                selectedType={userType}
              />

              {userType && (
                <div className="flex justify-center mt-8">
                  <InteractiveButton
                    onClick={handleContinue}
                    isLoading={isLoading}
                    className="px-8 py-3 text-lg font-semibold"
                    variant="primary"
                  >
                    {userType === "creator"
                      ? "Continue as Creator"
                      : "Continue as Fan"}
                    <Icon
                      icon="solar:arrow-right-linear"
                      className="w-5 h-5 ml-2"
                    />
                  </InteractiveButton>
                </div>
              )}
            </div>
          )}

          {/* Step 3: Profile Completion (for fans) */}
          {currentStep === 3 && (
            <div className="text-center space-y-6">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto">
                <Icon
                  icon="solar:check-circle-bold"
                  className="w-12 h-12 text-white"
                />
              </div>

              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  You're All Set!
                </h1>
                <p className="text-lg text-gray-600 mb-8">
                  Welcome to Erossphere! Start exploring amazing content from
                  creators.
                </p>
              </div>

              <div className="bg-blue-50 rounded-2xl p-6 mb-8">
                <h3 className="font-semibold text-gray-900 mb-4">
                  What's Next?
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                  <div className="flex items-start gap-3">
                    <Icon
                      icon="solar:magnifer-bold"
                      className="w-5 h-5 text-blue-600 mt-0.5"
                    />
                    <div>
                      <p className="font-medium text-gray-900">
                        Discover Creators
                      </p>
                      <p className="text-sm text-gray-600">
                        Browse and find creators you love
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Icon
                      icon="solar:heart-bold"
                      className="w-5 h-5 text-blue-600 mt-0.5"
                    />
                    <div>
                      <p className="font-medium text-gray-900">
                        Subscribe & Support
                      </p>
                      <p className="text-sm text-gray-600">
                        Get exclusive access to premium content
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <InteractiveButton
                onClick={handleContinue}
                className="px-8 py-3 text-lg font-semibold"
                variant="primary"
              >
                Start Exploring
                <Icon
                  icon="solar:arrow-right-linear"
                  className="w-5 h-5 ml-2"
                />
              </InteractiveButton>
            </div>
          )}

          {/* Skip Option */}
          <div className="text-center mt-8">
            <button
              onClick={handleSkip}
              className="text-gray-500 hover:text-gray-700 text-sm font-medium transition-colors"
            >
              Skip for now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingFlow;
