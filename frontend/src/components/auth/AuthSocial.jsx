import { FcGoogle } from "react-icons/fc";
import { useAuth } from "../../hooks/useAuth";
import InteractiveButton from "../InteractiveButton";
import toast from "react-hot-toast";

export default function AuthSocial() {
  const { googleSignIn } = useAuth();

  const handleGoogleSignIn = async () => {
    try {
      const response = await googleSignIn();
      window.location.href = response.data.url;
    } catch (error) {
      toast.error("Failed to initialize Google Sign In");
    }
  };

  return (
    <div className="flex flex-col gap-3">
      <InteractiveButton
        onClick={handleGoogleSignIn}
        variant="outline"
        className="w-full flex items-center justify-center gap-2"
      >
        <FcGoogle className="text-xl" />
        <span>Continue with Google</span>
      </InteractiveButton>
    </div>
  );
}
