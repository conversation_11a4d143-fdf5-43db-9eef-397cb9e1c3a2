import React, { useState, useRef } from "react";
import { FiSend, FiSmile, FiPaperclip, FiMic, FiX } from "react-icons/fi";
import { uploadService } from "../services/api";

const QUICK_EMOJIS = ["💦", "👍", "💦", "🍓", "🍆", "😍", "💦", "🍑", "😍"];

const MessageInput = ({ value, onChange, onSubmit, isLoading }) => {
  const [showEmojis, setShowEmojis] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [selectedFile, setSelectedFile] = useState(null);
  const fileInputRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const recordingTimerRef = useRef(null);

  const getMessageType = (fileType) => {
    console.log("fileType", fileType);
    if (fileType.startsWith("image/")) return "image";
    if (fileType.startsWith("audio/")) return "voice";
    return "text";
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (selectedFile && value.trim()) {
        // Send both text and file
        const formData = new FormData();
        formData.append("file", selectedFile);
        const response = await uploadService.uploadSingle(formData);
        const payload = {
          content: value,
          messageType: getMessageType(selectedFile.type),
          media: {
            url: response.data?.url || response.url,
            type: selectedFile.type,
            filename: selectedFile.name,
          },
        };
        console.log("payload", payload);
        await onSubmit(payload);
        setSelectedFile(null);
        onChange("");
      } else if (selectedFile) {
        // Send only file
        const formData = new FormData();
        formData.append("file", selectedFile);
        const response = await uploadService.uploadSingle(formData);
        await onSubmit({
          content: selectedFile.name,
          messageType: getMessageType(selectedFile.type),
          media: {
            url: response.data?.url || response.url,
            type: selectedFile.type,
            filename: selectedFile.name,
          },
        });
        setSelectedFile(null);
        onChange("");
      } else if (value.trim()) {
        // Send only text
        await onSubmit({ content: value, messageType: "text" });
        onChange("");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
    }
  };

  const handleEmojiClick = (emoji) => {
    onChange(value + emoji);
    setShowEmojis(false);
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        try {
          const audioBlob = new Blob(audioChunksRef.current, {
            type: "audio/wav",
          });
          const formData = new FormData();
          formData.append("file", audioBlob, "voice-note.wav");

          const response = await uploadService.uploadSingle(formData);
          const messageData = {
            content: "Voice note",
            messageType: "voice",
            media: {
              url: response.url || response.data.url,
              mediaType: "audio/wav",
              filename: "voice-note.wav",
              duration: recordingTime || 0,
            },
          };
          console.log("Voice note payload:", messageData);
          await onSubmit(messageData);
          setRecordingTime(0);
        } catch (error) {
          console.error("Error uploading voice note:", error);
        }
      };

      mediaRecorder.start();
      setIsRecording(true);
      startRecordingTimer();
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream
        .getTracks()
        .forEach((track) => track.stop());
      setIsRecording(false);
      clearInterval(recordingTimerRef.current);
      setRecordingTime(0);
    }
  };

  const startRecordingTimer = () => {
    setRecordingTime(0);
    recordingTimerRef.current = setInterval(() => {
      setRecordingTime((prev) => prev + 1);
    }, 1000);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="p-4 border-t !mt-auto">
      {selectedFile && (
        <div className="mb-2 p-2 bg-gray-100 rounded-lg flex items-center justify-between">
          <span className="text-sm truncate">
            {selectedFile.type.startsWith("image/") ? (
              <div className="flex items-center gap-2">
                <img
                  src={URL.createObjectURL(selectedFile)}
                  alt="Preview"
                  className="w-10 h-10 object-cover rounded"
                />
                <span>{selectedFile.name}</span>
              </div>
            ) : selectedFile.type.startsWith("audio/") ? (
              <div className="flex items-center gap-2">
                <FiMic className="w-4 h-4" />
                <span>{selectedFile.name}</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <FiPaperclip className="w-4 h-4" />
                <span>{selectedFile.name}</span>
              </div>
            )}
          </span>
          <button
            onClick={() => setSelectedFile(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <FiX className="w-4 h-4" />
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex items-center gap-2">
        <div className="relative">
          <button
            type="button"
            onClick={() => setShowEmojis(!showEmojis)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
          >
            <FiSmile className="w-5 h-5" />
          </button>
          {showEmojis && (
            <div className="absolute bottom-12 left-0 bg-white shadow-lg rounded-lg p-2 flex gap-2 z-50">
              {QUICK_EMOJIS.map((emoji) => (
                <button
                  key={emoji}
                  type="button"
                  onClick={() => handleEmojiClick(emoji)}
                  className="hover:bg-gray-100 p-1 rounded"
                >
                  {emoji}
                </button>
              ))}
            </div>
          )}
        </div>

        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={
            isRecording
              ? "Recording..."
              : selectedFile
              ? "Add a caption..."
              : "Type your message..."
          }
          disabled={isRecording}
          className="flex-1 bg-gray-100 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50"
        />

        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx"
        />

        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
        >
          <FiPaperclip className="w-5 h-5" />
        </button>

        {!isRecording ? (
          <button
            type="button"
            onClick={startRecording}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
          >
            <FiMic className="w-5 h-5" />
          </button>
        ) : (
          <button
            type="button"
            onClick={stopRecording}
            className="p-2 text-red-500 hover:text-red-700 rounded-full hover:bg-red-100 flex items-center gap-1"
          >
            <FiMic className="w-5 h-5" />
            <span className="text-sm">{formatTime(recordingTime)}</span>
          </button>
        )}

        <button
          type="submit"
          disabled={
            (!value.trim() && !selectedFile) || isLoading || isRecording
          }
          className="p-2 rounded-full bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <FiSend className="w-5 h-5" />
        </button>
      </form>
    </div>
  );
};

export default MessageInput;
