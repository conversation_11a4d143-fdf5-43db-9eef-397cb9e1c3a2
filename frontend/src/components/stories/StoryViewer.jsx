import React, { useState, useEffect, useCallback } from "react";
import { IoClose } from "react-icons/io5";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { BsFillPlayFill, BsPauseFill } from "react-icons/bs";
import { formatDistanceToNow } from "date-fns";
import Avatar from "../Avatar";

export default function StoryViewer({
  stories,
  currentIndex,
  onClose,
  onNext,
  onPrevious,
  totalStories,
}) {
  const [progress, setProgress] = useState(new Array(totalStories).fill(0));
  const [isPaused, setIsPaused] = useState(false);
  const story = stories[currentIndex];

  const resetProgress = useCallback(() => {
    setProgress((prev) => {
      const newProgress = [...prev];
      newProgress[currentIndex] = 0;
      return newProgress;
    });
  }, [currentIndex]);

  // Reset progress when switching between users
  useEffect(() => {
    setProgress(
      new Array(totalStories)
        .fill(0)
        .map((_, i) => (i < currentIndex ? 100 : 0))
    );
  }, [totalStories, currentIndex]);

  useEffect(() => {
    if (isPaused) return;

    const timer = setInterval(() => {
      setProgress((prev) => {
        const newProgress = [...prev];
        if (newProgress[currentIndex] < 100) {
          newProgress[currentIndex] += 0.5; // Slower progress (20 seconds per story)
          return newProgress;
        } else {
          onNext();
          return prev;
        }
      });
    }, 100);

    return () => clearInterval(timer);
  }, [currentIndex, isPaused, onNext]);

  const handlePlayPause = (e) => {
    e.stopPropagation();
    setIsPaused(!isPaused);
  };

  const handleTouchStart = useCallback(
    (e) => {
      const touch = e.touches[0];
      const screenWidth = window.innerWidth;
      const touchX = touch.clientX;

      // Left third of the screen
      if (touchX < screenWidth / 3) {
        onPrevious();
      }
      // Right third of the screen
      else if (touchX > (screenWidth * 2) / 3) {
        onNext();
      }
      // Middle third - toggle pause
      else {
        handlePlayPause(e);
      }
    },
    [onNext, onPrevious]
  );

  return (
    <div
      className="fixed inset-0 bg-black z-50 flex items-center justify-center"
      onTouchStart={handleTouchStart}
    >
      {/* Top shadow overlay */}
      <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-black/70 to-transparent z-10" />

      {/* Bottom shadow overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/70 to-transparent z-10" />

      {/* Close button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 text-white z-50 hover:opacity-80 transition-opacity"
      >
        <IoClose size={24} />
      </button>

      {/* Play/Pause button */}
      <button
        onClick={handlePlayPause}
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white z-50 hover:opacity-80 transition-opacity bg-black/30 rounded-full p-3"
      >
        {isPaused ? <BsFillPlayFill size={24} /> : <BsPauseFill size={24} />}
      </button>

      {/* Navigation buttons */}
      {currentIndex > 0 && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onPrevious();
          }}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white z-20 hover:bg-white/10 p-2 rounded-full transition-colors"
        >
          <IoIosArrowBack size={24} />
        </button>
      )}

      {currentIndex < totalStories - 1 && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onNext();
          }}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white z-20 hover:bg-white/10 p-2 rounded-full transition-colors"
        >
          <IoIosArrowForward size={24} />
        </button>
      )}

      {/* Progress bars */}
      <div className="absolute top-2 left-0 right-0 flex gap-1 px-3 z-20">
        {progress.map((value, index) => (
          <div
            key={index}
            className="flex-1 h-1 bg-gray-700/50 rounded-full overflow-hidden"
          >
            <div
              className={`h-full transition-all duration-100 ease-linear rounded-full ${
                index < currentIndex
                  ? "w-full bg-white"
                  : index === currentIndex
                  ? "bg-white"
                  : "w-0 bg-white/50"
              }`}
              style={{
                width: `${value}%`,
              }}
            />
          </div>
        ))}
      </div>

      {/* Story content */}
      <div className="relative w-full h-full">
        {/* Story header */}
        <div className="absolute top-8 left-4 flex items-center space-x-3 text-white z-20">
          {story.creator.avatar ? (
            <img
              src={story.creator.avatar}
              alt={story.creator.username}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <Avatar className={"h-10 w-10"} />
          )}
          <div>
            <p className="font-semibold">{story.creator.username}</p>
            <p className="text-sm opacity-75">
              {formatDistanceToNow(new Date(story.createdAt), {
                addSuffix: true,
              })}
            </p>
          </div>
        </div>

        {/* Story media */}
        {story.mediaType === "image" ? (
          <img
            src={story.mediaUrl}
            alt="Story"
            className="w-full h-full object-cover"
          />
        ) : (
          <video
            src={story.mediaUrl}
            className="w-full h-full object-cover"
            autoPlay
            muted
            playsInline
            loop={isPaused}
            onEnded={onNext}
          />
        )}
      </div>
    </div>
  );
}
