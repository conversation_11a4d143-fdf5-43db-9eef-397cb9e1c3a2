import React, { useState, useRef } from "react";
import { IoClose, IoEarth } from "react-icons/io5";
import { MdAddPhotoAlternate, MdVideoLibrary, MdPeople } from "react-icons/md";
import { uploadService, storyService } from "../../services/api";
import toast from "react-hot-toast";

export default function StoryUploader({ onClose, onSuccess }) {
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [mediaType, setMediaType] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [videoDuration, setVideoDuration] = useState(0);
  const [visibility, setVisibility] = useState("public");
  const videoRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const chunksRef = useRef([]);

  // Trim video to 60 seconds using MediaRecorder
  const trimVideo = async (file) => {
    try {
      setIsProcessing(true);

      const videoUrl = URL.createObjectURL(file);
      const video = document.createElement("video");

      video.src = videoUrl;
      await new Promise((resolve) => {
        video.onloadedmetadata = resolve;
      });

      const canvas = document.createElement("canvas");
      const stream = canvas.captureStream();
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "video/webm;codecs=h264",
        videoBitsPerSecond: 2500000, // 2.5 Mbps
      });

      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      const trimmedVideo = await new Promise((resolve) => {
        mediaRecorder.onstop = () => {
          const blob = new Blob(chunksRef.current, { type: "video/webm" });
          const trimmedFile = new File([blob], "trimmed-video.webm", {
            type: "video/webm",
          });
          resolve(trimmedFile);
        };

        video.currentTime = 0;
        video.play();
        mediaRecorder.start();

        // Stop recording after 60 seconds or at video end
        const duration = Math.min(60, video.duration);
        setTimeout(() => {
          video.pause();
          mediaRecorder.stop();
          URL.revokeObjectURL(videoUrl);
        }, duration * 1000);
      });

      return trimmedVideo;
    } catch (error) {
      console.error("Error trimming video:", error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileSelect = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const type = file.type.startsWith("image/") ? "image" : "video";

    // Check video duration
    if (type === "video") {
      try {
        const duration = await getVideoDuration(file);
        setVideoDuration(duration);

        if (duration > 60) {
          const shouldTrim = window.confirm(
            "Video is longer than 1 minute. Would you like to trim it to the first 60 seconds?"
          );

          if (shouldTrim) {
            const trimmedFile = await trimVideo(file);
            setSelectedFile(trimmedFile);
            setPreview(URL.createObjectURL(trimmedFile));
          } else {
            toast.error("Please select a video shorter than 1 minute");
            return;
          }
        } else {
          setSelectedFile(file);
          setPreview(URL.createObjectURL(file));
        }
      } catch (error) {
        console.error("Error processing video:", error);
        toast.error("Error processing video. Please try again.");
        return;
      }
    } else {
      setSelectedFile(file);
      setPreview(URL.createObjectURL(file));
    }

    setMediaType(type);
  };

  // Helper function to get video duration
  const getVideoDuration = (file) => {
    return new Promise((resolve, reject) => {
      const video = document.createElement("video");
      video.preload = "metadata";
      video.onloadedmetadata = () => {
        window.URL.revokeObjectURL(video.src);
        resolve(video.duration);
      };
      video.onerror = reject;
      video.src = URL.createObjectURL(file);
    });
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);

      // First upload the file to get the URL
      const formData = new FormData();
      formData.append("file", selectedFile);

      const uploadResponse = await uploadService.uploadSingle(formData);

      // Create the story with the uploaded file URL and visibility
      await storyService.createStory({
        mediaUrl: uploadResponse.data.url,
        mediaType: uploadResponse.data.type,
        public_id: uploadResponse.data.public_id,
        asset_id: uploadResponse.data.asset_id,
        format: uploadResponse.data.format,
        duration: uploadResponse.data.duration,
        width: uploadResponse.data.width,
        height: uploadResponse.data.height,
        thumbnail: uploadResponse.data.thumbnail,
        visibility: visibility,
      });

      onSuccess();
      onClose();
      toast.success("Story uploaded successfully");
    } catch (error) {
      if (error.response.data.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error("An error occurred. Please try again.");
      }
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-4 sm:p-6 w-full max-w-lg shadow-lg border border-gray-100 mx-2">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl sm:text-2xl font-bold text-secondary">
            Add to Story
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-50"
          >
            <IoClose size={24} />
          </button>
        </div>

        {!selectedFile ? (
          <div className="space-y-4">
            <label className="block w-full p-4 sm:p-8 border-2 border-dashed border-gray-200 rounded-2xl cursor-pointer hover:border-primary transition-all hover:bg-primary-100/30">
              <input
                type="file"
                accept="image/*,video/*"
                className="hidden"
                onChange={handleFileSelect}
              />
              <div className="flex flex-col items-center space-y-4 text-gray-400">
                <div className="flex space-x-4 text-primary">
                  <MdAddPhotoAlternate className="w-6 h-6 sm:w-8 sm:h-8" />
                  <MdVideoLibrary className="w-6 h-6 sm:w-8 sm:h-8" />
                </div>
                <div className="text-center">
                  <p className="font-medium mb-1 text-gray-600 text-sm sm:text-base">
                    Drop your media here
                  </p>
                  <p className="text-xs sm:text-sm">or click to browse</p>
                </div>
              </div>
            </label>
          </div>
        ) : (
          <div className="space-y-4 sm:space-y-6">
            <div className="relative w-full aspect-square rounded-xl overflow-hidden ring-2 ring-gray-100">
              {mediaType === "image" ? (
                <img
                  src={preview}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
              ) : (
                <video
                  ref={videoRef}
                  src={preview}
                  className="w-full h-full object-cover"
                  controls
                />
              )}
              {isProcessing && (
                <div className="absolute inset-0 bg-white/80 flex items-center justify-center">
                  <div className="text-gray-800 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm sm:text-base">Processing video...</p>
                  </div>
                </div>
              )}
            </div>
            {mediaType === "video" && (
              <p className="text-xs sm:text-sm text-gray-500">
                Duration: {Math.round(videoDuration)} seconds
              </p>
            )}
            <div className="grid grid-cols-2 gap-2 sm:gap-4">
              <button
                type="button"
                onClick={() => setVisibility("public")}
                className={`flex items-center justify-center gap-1 sm:gap-2 p-2 sm:p-3 rounded-xl border-2 transition-all ${
                  visibility === "public"
                    ? "border-primary bg-primary-100 text-primary"
                    : "border-gray-200 text-gray-600 hover:border-primary hover:text-primary"
                }`}
              >
                <IoEarth className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="font-medium text-sm sm:text-base">Public</span>
              </button>

              <button
                type="button"
                onClick={() => setVisibility("subscribers")}
                className={`flex items-center justify-center gap-1 sm:gap-2 p-2 sm:p-3 rounded-xl border-2 transition-all ${
                  visibility === "subscribers"
                    ? "border-primary bg-primary-100 text-primary"
                    : "border-gray-200 text-gray-600 hover:border-primary hover:text-primary"
                }`}
              >
                <MdPeople className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="font-medium text-sm sm:text-base">
                  Subscribers
                </span>
              </button>
            </div>
            <button
              onClick={handleUpload}
              disabled={isUploading || isProcessing}
              className="w-full py-2.5 sm:py-3 px-4 bg-primary hover:bg-primary-500 text-white rounded-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium text-sm sm:text-base shadow-lg shadow-primary/25 hover:shadow-primary/40"
            >
              {isUploading ? (
                <span className="flex items-center justify-center space-x-2">
                  <svg
                    className="animate-spin h-4 w-4 sm:h-5 sm:w-5"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  <span className="text-sm sm:text-base">Uploading...</span>
                </span>
              ) : (
                <span className="text-sm sm:text-base">Share to Story</span>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
