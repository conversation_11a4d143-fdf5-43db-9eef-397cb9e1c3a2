import { create } from "zustand";
import { storyService } from "../services/api";

const useStoryStore = create((set) => ({
  stories: [],
  isLoading: false,
  error: null,
  loadStories: async (query) => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await storyService.getStories(query);
      set((state) => ({
        ...state,
        stories: response.stories,
        isLoading: false,
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message, isLoading: false }));
    }
  },
  loadUserStories: async (userId) => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await storyService.getUserStories(userId);
      set((state) => ({
        ...state,
        stories: response.stories,
        isLoading: false,
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message, isLoading: false }));
    }
  },
  createStory: async (data) => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await storyService.createStory(data);
      set((state) => ({
        ...state,
        stories: response.stories,
        isLoading: false,
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message, isLoading: false }));
    }
  },
  viewStory: async (storyId) => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await storyService.viewStory(storyId);
      set((state) => ({
        ...state,
        stories: response.stories,
        isLoading: false,
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message, isLoading: false }));
    }
  },
  deleteStory: async (storyId) => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await storyService.deleteStory(storyId);
      set((state) => ({
        ...state,
        stories: response.stories,
        isLoading: false,
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message, isLoading: false }));
    }
  },
}));

export default useStoryStore;
