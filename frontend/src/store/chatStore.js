import { create } from "zustand";

const useChatStore = create((set, get) => ({
  currentConversation: null,
  socketConnected: false,
  onlineUsers: new Map(),
  lastSeenTimes: new Map(),

  setSocketConnected: (connected) =>
    set((state) => ({ socketConnected: connected })),

  setCurrentConversation: (conversation) =>
    set({ currentConversation: conversation }),

  updateUserStatus: (userId, isOnline, lastSeen) => {
    set((state) => {
      const newOnlineUsers = new Map(state.onlineUsers);
      const newLastSeenTimes = new Map(state.lastSeenTimes);

      newOnlineUsers.set(userId, isOnline);
      if (lastSeen) {
        newLastSeenTimes.set(userId, lastSeen);
      }

      return {
        onlineUsers: newOnlineUsers,
        lastSeenTimes: newLastSeenTimes
      };
    });
  },

  getUserStatus: (userId) => {
    const state = get();
    return {
      isOnline: state.onlineUsers.get(userId) || false,
      lastSeen: state.lastSeenTimes.get(userId) || null
    };
  },

  updateMessageReadStatus: (conversationId, readBy) => {
    set((state) => ({
      messages: state.messages.map((message) => {
        if (message.conversation === conversationId) {
          return {
            ...message,
            readBy: [...new Set([...message.readBy, readBy])],
          };
        }
        return message;
      }),
    }));
  },
}));

export default useChatStore;
