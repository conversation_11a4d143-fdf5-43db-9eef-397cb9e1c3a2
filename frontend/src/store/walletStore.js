import { create } from "zustand";
import { walletService } from "../services/api";

const useWalletStore = create((set) => ({
  wallet: null,
  transactions: [],
  isLoading: false,
  error: null,
  getWallet: async () => {
    set((state) => ({ ...state, isLoading: true }));
    const response = await walletService.getWallet();
    return set((state) => ({
      ...state,
      wallet: response.data,
      isLoading: false,
    }));
  },
  getTransactions: async () => {
    set((state) => ({ ...state, isLoading: true }));
    const response = await walletService.getTransactions();
    return set((state) => ({
      ...state,
      transactions: response.data,
      isLoading: false,
    }));
  },
  fundWallet: (wallet, transaction) => {
    set((state) => ({
      ...state,
      wallet: wallet,
      transaction: transaction,
    }));
  },
  debitWallet: async (wallet, transaction) => {
    return set((state) => ({
      ...state,
      wallet,
      transaction,
    }));
  },
}));

export default useWalletStore;
