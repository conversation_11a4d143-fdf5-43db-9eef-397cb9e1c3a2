import { create } from "zustand";
import { notificationService } from "../services/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";

// Query keys
export const notificationKeys = {
  all: ["notifications"],
  list: (filters, page) => [...notificationKeys.all, filters, page],
};

// Custom hook for notifications query
export const useNotificationsQuery = (page = 1, filters = {}) => {
  return useQuery({
    queryKey: notificationKeys.list(filters, page),
    queryFn: async () => {
      const activeFilters = Object.entries(filters).reduce(
        (acc, [key, value]) => {
          if (value !== "all") {
            acc[key] = value;
          }
          return acc;
        },
        {}
      );

      const response = await notificationService.getNotifications(
        page,
        activeFilters
      );

      if (!response.notifications) {
        return {
          notifications: [],
          currentPage: 1,
          totalPages: 0,
          totalNotifications: 0,
          totalUnread: 0,
        };
      }

      return response;
    },
  });
};

// Store for managing filters and other UI state
const useNotificationStore = create((set) => ({
  filters: {
    status: "all", // 'all', 'read', 'unread'
    type: "all", // 'all', 'LIKE', 'COMMENT', 'FOLLOW'
  },
  currentPage: 1,

  setFilter: (filterKey, value) => {
    set((state) => ({
      filters: {
        ...state.filters,
        [filterKey]: value,
      },
      currentPage: 1,
    }));
  },

  setPage: (page) => {
    set({ currentPage: page });
  },
}));

// Custom hook for marking notifications as read
export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient();

  return async (notificationId) => {
    await notificationService.markNotificationAsRead(notificationId);
    // Invalidate queries to refetch data
    queryClient.invalidateQueries({ queryKey: notificationKeys.all });
  };
};

export default useNotificationStore;
