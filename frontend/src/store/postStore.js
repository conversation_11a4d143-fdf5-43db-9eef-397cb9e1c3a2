import { create } from "zustand";
import { commentService, postService } from "../services/api";

const usePostStore = create((set) => ({
  posts: [],
  subscribedPosts: [],
  isLoading: false,
  error: null,
  post: null,
  comments: null,
  media: null,
  totalPostCount: 0,
  totalMediaCount: 0,
  totalVideoCount: 0,
  totalImageCount: 0,
  isSubscribed: false,
  loadPosts: async (query) => {
    const response = await postService.getPosts(query);
    return set((state) => ({ ...state, posts: response.posts }));
  },
  loadSubscribedPosts: async () => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await postService.getSubscribedPosts();
      set((state) => ({
        ...state,
        subscribedPosts: response.posts,
        isLoading: false,
      }));
    } catch (error) {
      set((state) => ({
        ...state,
        error: error.message,
        isLoading: false,
      }));
    }
  },
  loadUserMedia: async (userId) => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await postService.getUserMedia(userId);
      console.log("media response", response);
      set((state) => ({
        ...state,
        media: response.media,
        totalMediaCount: response.totalMediaCount,
        totalPostCount: response.totalPostCount,
        isSubscribed: response.isSubscribed,
        totalVideoCount: response.totalVideoCount,
        totalImageCount: response.totalImageCount,
        isLoading: false,
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message }));
    }
  },
  getPost: async (postId) => {
    const response = await postService.getPost(postId);
    return set((state) => ({ ...state, post: response.post }));
  },
  addPost: (post) => {
    return set((state) => ({
      ...state,
      posts: [post, ...state.posts],
    }));
  },
  removePost: async (postId) => {
    try {
      await postService.deletePost(postId);
      return set((state) => ({
        posts: state.posts.filter((item) => item.id !== postId),
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message }));
    }
  },
  editPost: async (postId, updatedPost) => {
    try {
      await postService.updatePost(postId, updatedPost);
      return set((state) => ({
        posts: state.posts.map((post) =>
          post.id === postId ? updatedPost : post
        ),
      }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message }));
    }
  },
  likePost: (postId, userId) => {
    return set((state) => ({
      ...state,
      posts: state.posts.map((post) =>
        post._id === postId
          ? {
              ...post,
              likes: post.likes.includes(userId)
                ? post.likes.filter((id) => id !== userId)
                : [...post.likes, userId],
              likesCount: post.likes.includes(userId)
                ? post.likesCount - 1
                : post.likesCount + 1,
            }
          : post
      ),
    }));
  },
  addComment: (postId, comment) => {
    return set((state) => ({
      ...state,
      comments: {
        ...state.comments,
        comments: [comment, ...state.comments.comments],
      },
      post: {
        ...state.post,
        commentsCount: state.post.commentsCount + 1,
      },
    }));
  },
  getPostComments: async (postId) => {
    const response = await commentService.getComments(postId);
    console.log("response", response);
    return set((state) => ({ ...state, comments: response }));
  },
  getCommentReplies: async (commentId) => {
    const response = await commentService.getCommentReplies(commentId);
    return set((state) => ({ ...state, replies: response }));
  },
  addReply: (commentId, reply) => {
    return set((state) => ({
      ...state,
      comments: {
        ...state.comments,
        comments: [...state.comments.comments, reply],
      },
      post: {
        ...state.post,
        commentsCount: state.post.commentsCount + 1,
      },
    }));
  },
  likeComment: (commentId, userId) => {
    return set((state) => ({
      ...state,
      comments: {
        ...state.comments,
        comments: state.comments.comments.map((comment) =>
          comment._id === commentId
            ? {
                ...comment,
                likes: comment.likes.includes(userId)
                  ? comment.likes.filter((id) => id !== userId)
                  : [...comment.likes, userId],
                likesCount: comment.likes.includes(userId)
                  ? comment.likesCount - 1
                  : comment.likesCount + 1,
              }
            : comment
        ),
      },
    }));
  },

  loadUserPosts: async (userId) => {
    try {
      set((state) => ({ ...state, isLoading: true, error: null }));
      const response = await postService.getUserPosts(userId);
      set((state) => ({ ...state, posts: response.posts, isLoading: false }));
    } catch (error) {
      set((state) => ({ ...state, error: error.message, isLoading: false }));
    }
  },
}));

export default usePostStore;
