# Frontend Augment Rules - OnlyFans-like Platform (Erossphere)

## 🎨 UI/UX Design Principles

### 1. **OnlyFans-Inspired Design Language**

- **Dark/Light Theme Support**: Implement theme switching with user preference persistence
- **Mobile-First Approach**: Responsive design prioritizing mobile experience (70%+ mobile users)
- **Content-Focused Layout**: Minimize UI chrome to maximize content visibility
- **Intuitive Navigation**: Bottom navigation on mobile, sidebar on desktop
- **Visual Hierarchy**: Clear content separation with cards, shadows, and spacing

### 2. **Color Scheme & Branding**

- **Primary Colors**: Use brand colors consistently across components
- **Content Safety**: Subtle backgrounds that don't compete with user content
- **Accessibility**: WCAG 2.1 AA compliance with proper contrast ratios
- **Status Colors**: Clear visual feedback for different states (success, error, warning)

### 3. **Typography & Content**

- **Readable Fonts**: Use system fonts or web-safe alternatives
- **Content Hierarchy**: Clear heading structure (H1-H6) for accessibility
- **Text Truncation**: Graceful text overflow handling with "read more" functionality
- **Internationalization**: Support for multiple languages and RTL layouts

## 🏗️ Component Architecture

### 1. **Component Organization**

```
src/
├── components/
│   ├── common/          # Reusable UI components
│   ├── dashboard/       # Dashboard-specific components
│   ├── creator/         # Creator-specific components
│   ├── chat/           # Messaging components
│   ├── booking/        # Appointment booking components
│   └── payment/        # Payment-related components
├── pages/              # Route components
├── hooks/              # Custom React hooks
├── context/            # React Context providers
├── services/           # API service functions
├── utils/              # Utility functions
└── store/              # State management (Zustand)
```

### 2. **Component Design Patterns**

- **Compound Components**: For complex UI like modals, dropdowns
- **Render Props**: For flexible component composition
- **Custom Hooks**: Extract reusable logic into custom hooks
- **Higher-Order Components**: For cross-cutting concerns like authentication
- **Error Boundaries**: Graceful error handling in component trees

### 3. **Reusable Component Library**

```javascript
// Core UI Components
Button, Input, TextArea, Select, Checkbox, Radio;
Modal, Dropdown, Tooltip, Popover, Tabs;
Card, Avatar, Badge, Spinner, Skeleton;
Table, Pagination, InfiniteScroll;
Form, FormField, FormValidation;
```

## 📱 Responsive Design & Mobile Experience

### 1. **Breakpoint Strategy**

```css
/* Mobile-first breakpoints */
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */
xl: 1280px  /* Extra large devices */
2xl: 1536px /* 2X large devices */
```

### 2. **Mobile-Specific Features**

- **Touch Gestures**: Swipe navigation for stories, pull-to-refresh
- **Native-like Interactions**: Smooth animations and transitions
- **Optimized Images**: Responsive images with lazy loading
- **Offline Support**: Service worker for basic offline functionality
- **PWA Features**: Add to home screen, push notifications

### 3. **Performance Optimization**

- **Code Splitting**: Route-based and component-based code splitting
- **Lazy Loading**: Lazy load images, components, and routes
- **Bundle Optimization**: Tree shaking and dead code elimination
- **Caching Strategy**: Implement proper caching for API responses

## 🔐 Authentication & User Management

### 1. **Authentication Flow**

```javascript
// Auth states and transitions
UNAUTHENTICATED → LOGIN → AUTHENTICATED
AUTHENTICATED → LOGOUT → UNAUTHENTICATED
UNAUTHENTICATED → REGISTER → EMAIL_VERIFICATION → AUTHENTICATED
```

### 2. **Protected Routes**

- **Route Guards**: Implement AuthGuard and GuestGuard components
- **Role-based Access**: Different layouts for users, creators, and admins
- **Conditional Rendering**: Show/hide features based on user permissions
- **Session Management**: Handle token expiry and refresh

### 3. **User Profile Management**

- **Profile Editing**: In-place editing with validation
- **Avatar Upload**: Drag-and-drop image upload with preview
- **Social Links**: Manage external social media links
- **Privacy Settings**: Control profile visibility and content access

## 💰 Subscription & Payment UI

### 1. **Subscription Management**

- **Pricing Plans Display**: Clear pricing tiers with feature comparison
- **Subscription Status**: Visual indicators for active/expired subscriptions
- **Payment Methods**: Support multiple payment providers (Paystack, Flutterwave)
- **Billing History**: Comprehensive transaction history with receipts

### 2. **Creator Monetization UI**

- **Earnings Dashboard**: Revenue analytics with charts and metrics
- **Payout Management**: Withdrawal requests and payout history
- **Subscription Analytics**: Subscriber growth and retention metrics
- **Content Performance**: Views, likes, and engagement analytics

### 3. **Payment Flow UX**

- **Seamless Checkout**: Minimal steps from selection to completion
- **Payment Security**: Clear security indicators and SSL badges
- **Error Handling**: Graceful payment failure handling with retry options
- **Success Confirmation**: Clear confirmation with next steps

## 📨 Real-time Features & Messaging

### 1. **Chat System**

- **Real-time Messaging**: Socket.IO integration for instant messaging
- **Message Types**: Text, images, videos, voice messages
- **Read Receipts**: Message delivery and read status indicators
- **Typing Indicators**: Show when users are typing
- **Message History**: Infinite scroll with message search

### 2. **Notifications**

- **In-app Notifications**: Real-time notification dropdown
- **Push Notifications**: Browser push notifications for important events
- **Notification Preferences**: User control over notification types
- **Notification History**: Persistent notification center

### 3. **Live Features**

- **Online Status**: Real-time user presence indicators
- **Live Streaming**: Integration with streaming services (future)
- **Live Chat**: Real-time chat during live streams
- **Activity Feed**: Real-time updates for likes, comments, subscriptions

## 📅 Booking System UI (To Be Implemented)

### 1. **Calendar Interface**

- **Availability Calendar**: Visual calendar showing creator availability
- **Time Slot Selection**: Easy time slot picking with timezone support
- **Booking Form**: Streamlined booking request form
- **Calendar Integration**: Export to Google Calendar, Outlook

### 2. **Appointment Management**

- **Booking Dashboard**: Manage upcoming and past appointments
- **Meeting Links**: Secure video call integration (Zoom, Google Meet)
- **Rescheduling**: Easy appointment rescheduling interface
- **Cancellation**: Clear cancellation policy and process

### 3. **Creator Availability**

- **Schedule Management**: Set available hours and days
- **Booking Settings**: Configure meeting types, durations, and rates
- **Automatic Scheduling**: Buffer times and availability rules
- **Booking Notifications**: Real-time booking alerts and confirmations

## 🎯 Content Creation & Management

### 1. **Content Upload**

- **Multi-media Support**: Images, videos, audio, documents
- **Drag-and-Drop**: Intuitive file upload interface
- **Progress Indicators**: Upload progress with cancel option
- **Content Preview**: Preview before publishing
- **Batch Upload**: Multiple file upload with queue management

### 2. **Content Editor**

- **Rich Text Editor**: WYSIWYG editor for post descriptions
- **Media Gallery**: Organize and manage uploaded media
- **Content Scheduling**: Schedule posts for future publication
- **Content Visibility**: Public vs premium content settings
- **Content Tags**: Categorize content with tags

### 3. **Content Discovery**

- **Feed Algorithm**: Personalized content feed
- **Search Functionality**: Search posts, creators, and hashtags
- **Content Filtering**: Filter by content type, date, popularity
- **Trending Content**: Highlight popular and trending posts
- **Recommendations**: Suggest similar content and creators

## 📊 Analytics & Insights

### 1. **Creator Analytics Dashboard**

- **Revenue Metrics**: Earnings, tips, subscription revenue
- **Audience Insights**: Subscriber demographics and behavior
- **Content Performance**: Post engagement and reach metrics
- **Growth Tracking**: Follower and subscriber growth over time

### 2. **User Engagement Metrics**

- **Interaction Tracking**: Likes, comments, shares, saves
- **Time Spent**: Content consumption patterns
- **Conversion Metrics**: Free to paid subscription conversion
- **Retention Analysis**: User retention and churn rates

### 3. **Data Visualization**

- **Interactive Charts**: Use Chart.js or D3.js for data visualization
- **Date Range Filters**: Flexible date range selection
- **Export Functionality**: Export analytics data as CSV/PDF
- **Real-time Updates**: Live updating metrics and charts

## 🔍 Search & Discovery

### 1. **Search Functionality**

- **Global Search**: Search across users, posts, and content
- **Advanced Filters**: Filter by content type, date, creator
- **Search Suggestions**: Auto-complete and search suggestions
- **Search History**: Save and manage search history
- **Trending Searches**: Show popular search terms

### 2. **Content Discovery**

- **Explore Page**: Curated content discovery
- **Category Browsing**: Browse content by categories
- **Creator Recommendations**: Suggest new creators to follow
- **Personalized Feed**: Algorithm-based content recommendations
- **Trending Content**: Highlight viral and trending posts

## 🛡️ Privacy & Safety Features

### 1. **Content Protection**

- **Watermarking**: Add creator watermarks to images/videos
- **Screenshot Detection**: Detect and discourage screenshots (mobile)
- **Right-click Protection**: Disable right-click on premium content
- **Content Encryption**: Secure content delivery for premium users

### 2. **User Safety**

- **Reporting System**: Easy content and user reporting
- **Block/Mute Features**: User blocking and muting functionality
- **Content Warnings**: Age-appropriate content warnings
- **Privacy Controls**: Granular privacy settings for profiles

### 3. **Moderation Tools**

- **Content Moderation**: Automated and manual content review
- **Community Guidelines**: Clear guidelines and enforcement
- **Appeal Process**: Content removal appeal system
- **Moderator Dashboard**: Tools for content moderators

## 🎨 Design System & Styling

### 1. **CSS Framework**

- **Tailwind CSS**: Utility-first CSS framework for rapid development
- **Custom Components**: Styled components with consistent design tokens
- **Design Tokens**: Centralized colors, spacing, typography, shadows
- **Component Variants**: Multiple variants for different use cases

### 2. **Animation & Interactions**

- **Micro-interactions**: Subtle animations for better UX
- **Page Transitions**: Smooth transitions between routes
- **Loading States**: Skeleton screens and loading animations
- **Hover Effects**: Interactive hover states for better feedback

### 3. **Accessibility**

- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Clear focus indicators and logical tab order
- **Color Contrast**: WCAG compliant color combinations

## 🔄 State Management

### 1. **Zustand Store Organization**

```javascript
// Store structure
stores/
├── authStore.js        # Authentication state
├── postStore.js        # Posts and content state
├── chatStore.js        # Messaging state
├── subscriptionStore.js # Subscription management
├── bookingStore.js     # Appointment booking state
├── notificationStore.js # Notifications state
└── uiStore.js          # UI state (modals, themes, etc.)
```

### 2. **State Management Patterns**

- **Normalized State**: Flatten nested data structures
- **Optimistic Updates**: Update UI before API confirmation
- **Error Handling**: Consistent error state management
- **Loading States**: Track loading states for better UX
- **Cache Management**: Implement proper cache invalidation

### 3. **React Query Integration**

- **Server State**: Use React Query for server state management
- **Cache Strategies**: Implement appropriate cache strategies
- **Background Updates**: Keep data fresh with background refetching
- **Offline Support**: Handle offline scenarios gracefully

## 📱 Progressive Web App (PWA)

### 1. **PWA Features**

- **Service Worker**: Implement service worker for offline functionality
- **App Manifest**: Configure web app manifest for installation
- **Push Notifications**: Browser push notifications for engagement
- **Offline Mode**: Basic offline functionality for cached content

### 2. **Installation & Updates**

- **Install Prompt**: Custom install prompt for better conversion
- **Update Notifications**: Notify users of app updates
- **Background Sync**: Sync data when connection is restored
- **App Shell**: Fast loading app shell architecture

## 🧪 Testing Strategy

### 1. **Testing Pyramid**

- **Unit Tests**: Test individual components and functions
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows
- **Visual Regression Tests**: Catch visual changes

### 2. **Testing Tools**

- **Jest**: Unit testing framework
- **React Testing Library**: Component testing utilities
- **Cypress**: End-to-end testing framework
- **Storybook**: Component development and testing

### 3. **Testing Best Practices**

- **Test User Behavior**: Focus on user interactions, not implementation
- **Accessibility Testing**: Test with screen readers and keyboard navigation
- **Performance Testing**: Test loading times and responsiveness
- **Cross-browser Testing**: Ensure compatibility across browsers

## 🚀 Performance Optimization

### 1. **Bundle Optimization**

- **Code Splitting**: Split code by routes and features
- **Tree Shaking**: Remove unused code from bundles
- **Dynamic Imports**: Load components and libraries on demand
- **Bundle Analysis**: Regular bundle size analysis and optimization

### 2. **Runtime Performance**

- **React Optimization**: Use React.memo, useMemo, useCallback appropriately
- **Virtual Scrolling**: For large lists and feeds
- **Image Optimization**: Responsive images with lazy loading
- **Memory Management**: Prevent memory leaks in components

### 3. **Loading Performance**

- **Critical CSS**: Inline critical CSS for faster first paint
- **Resource Hints**: Use preload, prefetch, and preconnect
- **CDN Integration**: Serve static assets from CDN
- **Compression**: Enable gzip/brotli compression

## 🔧 Development Workflow

### 1. **Development Tools**

- **Vite**: Fast build tool and dev server
- **ESLint**: Code linting with custom rules
- **Prettier**: Code formatting for consistency
- **Husky**: Git hooks for pre-commit checks

### 2. **Code Quality**

- **TypeScript**: Gradual TypeScript adoption for type safety
- **Code Reviews**: Mandatory code reviews for all changes
- **Documentation**: Component documentation with Storybook
- **Style Guide**: Consistent coding standards and patterns

### 3. **Deployment & CI/CD**

- **Environment Management**: Separate dev, staging, and production configs
- **Automated Testing**: Run tests on every commit
- **Deployment Pipeline**: Automated deployment with rollback capability
- **Feature Flags**: Toggle features without code deployment

## 📋 OnlyFans-Specific UI Patterns

### 1. **Content Feed**

- **Infinite Scroll**: Smooth infinite scrolling with virtualization
- **Content Cards**: Consistent card design for different content types
- **Engagement Actions**: Like, comment, share, save actions
- **Content Previews**: Blur/preview for premium content

### 2. **Creator Profiles**

- **Profile Header**: Cover image, avatar, bio, subscription button
- **Content Tabs**: Posts, media, about sections
- **Subscription Tiers**: Clear pricing and feature display
- **Social Proof**: Subscriber count, verification badges

### 3. **Messaging Interface**

- **Chat List**: Conversation list with unread indicators
- **Message Bubbles**: Distinct styling for sent/received messages
- **Media Sharing**: Easy image/video sharing in chat
- **Message Actions**: Reply, react, delete message options

### 4. **Subscription Flow**

- **Plan Selection**: Clear plan comparison and selection
- **Payment Form**: Secure and user-friendly payment interface
- **Confirmation**: Success confirmation with next steps
- **Subscription Management**: Easy upgrade/downgrade/cancel options

## 🎯 User Experience Guidelines

### 1. **Onboarding Experience**

- **Welcome Flow**: Guided onboarding for new users
- **Profile Setup**: Step-by-step profile creation
- **Feature Discovery**: Introduce key features gradually
- **Creator Onboarding**: Specialized onboarding for creators

### 2. **Content Consumption**

- **Seamless Browsing**: Smooth content discovery and consumption
- **Content Interaction**: Easy engagement with likes, comments, shares
- **Personalization**: Customizable feed and recommendations
- **Content Organization**: Save, bookmark, and organize content

### 3. **Creator Tools**

- **Content Creation**: Intuitive content creation workflow
- **Analytics Dashboard**: Clear and actionable analytics
- **Subscriber Management**: Tools to engage with subscribers
- **Revenue Tracking**: Transparent earnings and payout information

## 🌐 Internationalization & Localization

### 1. **Multi-language Support**

- **i18n Framework**: Use react-i18next for internationalization
- **Language Detection**: Automatic language detection and switching
- **RTL Support**: Right-to-left language support
- **Currency Localization**: Display prices in local currencies

### 2. **Cultural Adaptation**

- **Date/Time Formats**: Localized date and time formatting
- **Number Formats**: Localized number and currency formatting
- **Content Guidelines**: Adapt content guidelines for different regions
- **Payment Methods**: Support regional payment methods

## 🔍 SEO & Marketing

### 1. **Search Engine Optimization**

- **Meta Tags**: Dynamic meta tags for better SEO
- **Structured Data**: Schema markup for rich snippets
- **Sitemap**: Dynamic sitemap generation
- **Social Sharing**: Open Graph and Twitter Card meta tags

### 2. **Analytics & Tracking**

- **Google Analytics**: Track user behavior and conversions
- **Event Tracking**: Custom event tracking for key actions
- **A/B Testing**: Framework for testing different UI variations
- **Performance Monitoring**: Real user monitoring and core web vitals

### 3. **Marketing Integration**

- **Referral System**: Built-in referral tracking and rewards
- **Social Media Integration**: Easy sharing to social platforms
- **Email Marketing**: Integration with email marketing tools
- **Affiliate Program**: Creator affiliate program interface
