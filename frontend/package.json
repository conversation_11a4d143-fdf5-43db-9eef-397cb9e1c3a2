{"name": "my-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.7", "@ffmpeg/util": "^0.12.1", "@floating-ui/react": "^0.27.3", "@hookform/resolvers": "^3.9.1", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "axios": "^1.7.7", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-timezone": "^0.1.4", "emoji-picker-react": "^4.12.0", "flutterwave-react-v3": "^1.3.2", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "multer": "^1.4.5-lts.1", "numeral": "^2.0.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-paystack": "^6.0.0", "react-router-dom": "^6.27.0", "recharts": "^2.15.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.1", "yet-another-react-lightbox": "^3.15.1", "yup": "^1.4.0", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@iconify/react": "^5.0.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001695", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "postcss": "^8.5.1", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^3.4.17", "vite": "^5.4.9"}}